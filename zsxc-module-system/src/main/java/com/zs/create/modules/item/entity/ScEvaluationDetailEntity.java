package com.zs.create.modules.item.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目评价维度分数详细
 *
 * <AUTHOR> @email 
 * @date 2023-02-16 16:30:36
 */
@Data
@TableName("sc_evaluation_detail")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_evaluation_detail对象", description="项目评价维度分数详细")
public class ScEvaluationDetailEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "id")
	    private String id;
	/**
	 * 项目id
	 */
	    @ApiModelProperty(value = "项目id")
	    private String itemId;
	/**
	 * 字典详情维度id
	 */
	    @ApiModelProperty(value = "字典详情维度id")
	    private String evaluationId;
	/**
	 * 分数
	 */
	    @ApiModelProperty(value = "分数")
	    private Integer evaluationScore;
	/**
	 * 学号
	 */
	    @ApiModelProperty(value = "学号")
	    private String userCode;
	/**
	 * 创建时间
	 */
	    @ApiModelProperty(value = "创建时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date createTime;
	/**
	 * 创建人
	 */
	    @ApiModelProperty(value = "创建人")
	    private String createBy;
	/**
	 * 更新时间
	 */
	    @ApiModelProperty(value = "更新时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date updateTime;
	/**
	 * 更新人
	 */
	    @ApiModelProperty(value = "更新人")
	    private String updateBy;

}
