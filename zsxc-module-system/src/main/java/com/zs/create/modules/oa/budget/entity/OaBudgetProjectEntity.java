package com.zs.create.modules.oa.budget.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * 预算
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-18 10:10:04
 */
@Data
@TableName("oa_budget_project")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oa_budget_project对象", description="预算项目")
public class OaBudgetProjectEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.AUTO)
	@ApiModelProperty(value = "预算项目主键")
	private Long id;
	/**
	 * 部门预算id
	 */
	@ApiModelProperty(value = "部门预算id")
	private Long budgetId;
	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	@NotBlank(message = "项目名称不能为空")
	@Length(max=128, message = "项目名称过长")
	private String itemName;
	/**
	 * 项目模块
	 */
	@ApiModelProperty(value = "项目模块")
	@NotBlank(message = "项目模块不能为空")
	private String module;

	@TableField(exist = false)
	@ApiModelProperty(value = "项目模块名称")
	private String module_text;
	/**
	 * 项目形式
	 */
	@ApiModelProperty(value = "项目形式")
	@NotBlank(message = "项目形式不能为空")
	private String form;

	@TableField(exist = false)
	@ApiModelProperty(value = "项目形式名称")
	private String form_text;
	/**
	 * 删除标记  0未删除  1已删除
	 */
	@ApiModelProperty(value = "删除标记  0未删除  1已删除")
	private Integer delFlag;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

	/**
	 * 部门预算对象
	 */
	@ApiModelProperty(value = "部门预算对象")
	@TableField(exist = false)
	private OaBudgetDeptEntity oaBudgetDept;

	/**
	 * 是否发起项目  0 未发起  1 发起
	 */
	@ApiModelProperty(value = "是否发起项目  0 未发起  1 发起")
	private Integer launched;

	public static final Integer LAUNCHED_NO = 0 ;
	public static final Integer LAUNCHED_YES = 1 ;

	@TableField(exist = false)
	@ApiModelProperty(value = "预算明细list")
	@Valid
	@NotNull(message = "项目预算明细不能为空")
	@Size(min = 1 , message = "项目预算明细不能为空")
	private List<OaBudgetItemEntity> budgetItemList = Collections.EMPTY_LIST;

}
