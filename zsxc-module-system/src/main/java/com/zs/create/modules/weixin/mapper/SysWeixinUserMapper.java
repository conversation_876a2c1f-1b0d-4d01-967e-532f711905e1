package com.zs.create.modules.weixin.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zs.create.modules.weixin.dto.WxUserInfoDTO;
import com.zs.create.modules.weixin.entity.SysWeixinUserEntity;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * <AUTHOR>
 * @Description 微信管理Mapper层
 * @email <EMAIL>
 * @date 2020-04-22 11:45:41
 * @Version: V1.0
 */
public interface SysWeixinUserMapper extends BaseMapper<SysWeixinUserEntity> {

    String getNextOpenId();

    List<SysWeixinUserEntity> selectOnlineList(SysWeixinUserEntity sysWeixinUserEntity);

    void removeOnlineById(@Param("id") String id);

    int deleteOpenid(String openid);

    WxUserDTO findUserOpenId(@Param("userId") String userId, @Param("appId") String appId);

    List<WxUserDTO> findUserOpenIds(@Param("userIds") Collection<String> userIds, @Param("appId") String appId);

    WxUserInfoDTO findWxUserInfo(@Param("userId") String userId);


}
