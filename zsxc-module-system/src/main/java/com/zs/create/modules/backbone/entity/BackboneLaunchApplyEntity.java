package com.zs.create.modules.backbone.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zs.create.common.aspect.annotation.Dict;
import com.zs.create.config.SerializerBigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 学生评议申请
 * 
 * <AUTHOR>
 * @email null
 * @date 2021-05-22 12:47:22
 */
@Data
@TableName("backbone_launch_apply")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="backbone_launch_apply对象", description="学生评议申请")
public class BackboneLaunchApplyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private String id;
	/**
	 * 评议发起的id
	 */
	@ApiModelProperty(value = "评议发起的id")
	private String launchId;
	/**
	 * 评议发起的标题
	 */
	@ApiModelProperty(value = "评议发起的标题")
	private String title;
	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	@Excel(name = "姓名", width = 15)
	private String realName;
	/**
	 * 学号
	 */
	@ApiModelProperty(value = "学号")
	@Excel(name = "学号", width = 15)
	private String userName;
	/**
	 * 电子照片路径
	 */
	@ApiModelProperty(value = "电子照片路径")
	private String photoUrl;
	/**
	 * 年级
	 */
	@ApiModelProperty(value = "年级")
	private String grade;
	/**
	 * 排名
	 */
	@ApiModelProperty(value = "排名")
	private String ranking;
	/**
	 * 班级总人数
	 */
	@ApiModelProperty(value = "班级总人数")
	private String allPersonNum;
	/**
	 * 政治面貌
	 */
	@ApiModelProperty(value = "政治面貌")
	private String political;
	/**
	 * 手机号
	 */
	@ApiModelProperty(value = "手机号")
	private String phone;
	/**
	 * 所在组织
	 */
	@ApiModelProperty(value = "所在组织")
	private String depId;
	/**
	 * 所在组织名称
	 */
	@ApiModelProperty(value = "所在组织名称")
	@Excel(name = "所在组织", width = 15)
	private String depName;
	/**
	 * 职务类型
	 */
	@ApiModelProperty(value = "职务类型")
	private String postType;
	/**
	 * 职务类型名称
	 */
	@ApiModelProperty(value = "职务类型名称")
	private String postTypeName;
	/**
	 * 职务
	 */
	@ApiModelProperty(value = "职务")
	@Excel(name = "申请职务", width = 15)
	private String post;
	/**
	 * 工作职责
	 */
	@ApiModelProperty(value = "工作职责")
	private String workDuty;
	/**
	 * 合计时长
	 */
	@JsonFormat( shape = JsonFormat.Shape.NUMBER_FLOAT)
	@JsonSerialize(using = SerializerBigDecimal.class)
	@ApiModelProperty(value = "合计时长")
	private BigDecimal allHours;
	/**
	 * 工作成效
	 */
	@ApiModelProperty(value = "工作成效")
	private String workResults;
	/**
	 * "待评议_1", "待审核_2", "审核通过_3", "审核驳回_-1", "暂存_-2","评议驳回_4","已公示_5","驳回_9"
	 */
	@ApiModelProperty(value = "状态   -2暂存 ")
	@Excel(name = "状态", width = 15, replace = {"待评议_1", "待审核_2", "审核通过_3", "审核驳回_-1", "暂存_-2","评议驳回_4","已公示_5","驳回_9"})
	private Integer status;
	/**
	 * 删除标识  0未删除  1已删除
	 */
	@ApiModelProperty(value = "删除标识  0未删除  1已删除")
	private Integer delFlag;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**
	 * 创建时间
	 */
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	@ApiModelProperty(value = "创建时间")
	@Excel(name = "填写时间", width = 15, exportFormat = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 修改人
	 */
	@ApiModelProperty(value = "修改人")
	private String updateBy;
	/**
	 * 修改时间
	 */
	@ApiModelProperty(value = "修改时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

	@TableField(exist = false)
	@ApiModelProperty(value = "对应的履职时长集合")
	private List<BackboneLaunchApplyHoursEntity> hourList;

	@ApiModelProperty(value = "流程实例id")
	private String proInstId;

	@ApiModelProperty(value = "是否处理")
	private String isDeal;

	@ApiModelProperty(value = "考核等级")
	@Dict(dicCode = "khdj")
	@Excel(name = "考核等级", width = 15, replace = {"优秀_100", "良好_80","合格_60", "不合格_0"})
	private String assessGrade;

	@ApiModelProperty(value = "考核等级转为字符串")
	@TableField(exist = false)
	private String assessGradeToString;

	@ApiModelProperty(value = "赋予学时")
	@Excel(name = "建议履职时长", width = 15,type = 4)
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	private BigDecimal finalHours;

	@ApiModelProperty(value = "评议申报的开始时间")
	@TableField(exist = false)
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date startTime;

	@ApiModelProperty(value = "评议申报的结束时间")
	@TableField(exist = false)
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date endTime;

	@TableField(exist = false)
	@ApiModelProperty(value = "所在组织的人数")
	private Integer departPersons;

	@TableField(exist = false)
	@ApiModelProperty(value = "评议时当前登录人的id--带入条件")
	private String userId;

	@ApiModelProperty(value = "评议时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date reviewTime;

	@ApiModelProperty(value = "公示的id（废弃）")
	private String publicityId;

	@ApiModelProperty(value = "公示的结束时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date publicityEndTime;

	@ApiModelProperty(value = "评议会标题（废弃）")
	private String publicityTitle;

	/**
	 * 是否需要公示  废弃
	 */
	private Integer isPublicity;

	/**
	 * 查询的状态
	 */
	@ApiModelProperty(value = "0  待审核  1已审核")
	@TableField(exist = false)
	private Integer type;

	@TableField(exist = false)
	private String assignee;

	/**
	 * 定义前端是否是局部刷新的
	 */
	@TableField(exist = false)
	private String refresh;

	@ApiModelProperty(value = "是否进行学时等级赋予  0 未赋予  1已赋予")
	private Integer isGiven;

	@ApiModelProperty(value = "评议会标题")
	@TableField(exist = false)
	@Excel(name = "评议会标题", width = 15)
	private String meetingTitle;

	@ApiModelProperty(value = "总赋予学时")
	@TableField(exist = false)
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	private BigDecimal totalHours;

	@ApiModelProperty(value = "评议时间")
	@TableField(exist = false)
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date meetingTime;



}
