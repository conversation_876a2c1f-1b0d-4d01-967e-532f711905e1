package com.zs.create.modules.oa.aoumaAndBones.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 青马/大骨班班级管理
 *
 * <AUTHOR> @email 
 * @date 2022-11-03 08:41:11
 */
@Data
@TableName("qd_class")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="qd_class对象", description="青马/大骨班班级管理")
public class QdClassEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "主键")
	    private String id;
	/**
	 * 班级名称
	 */
		@Length(max=128, message = "班级名称过长，不可超过128字符")
	    @ApiModelProperty(value = "班级名称")
	    private String className;
	/**
	 * 班级类型：0为大骨班，1为青马班
	 */
	    @ApiModelProperty(value = "班级类型：0为大骨班，1为青马班")
	    private Integer type;
	/**
	 * 部门id
	 */
	    @ApiModelProperty(value = "部门id")
	    private String deptId;
	/**
	 * 年级
	 */
	    @ApiModelProperty(value = "年级")
	    private String nj;
	/**
	 * 班级状态：0未开始，1进行中，2已结束
	 */
	    @ApiModelProperty(value = "班级状态：0未开始，1进行中，2已结束")
	    private Integer status;
	/**
	 * 报名开始时间
	 */
	    @ApiModelProperty(value = "报名开始时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date applyStarttime;
	/**
	 * 报名结束时间
	 */
	    @ApiModelProperty(value = "报名结束时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date applyEndtime;
	/**
	 * 创建时间
	 */
	    @ApiModelProperty(value = "创建时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date createTime;
	/**
	 * 创建人
	 */
	    @ApiModelProperty(value = "创建人")
	    private String createBy;
	/**
	 * 最后更新时间
	 */
	    @ApiModelProperty(value = "最后更新时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date updateTime;
	/**
	 * 最后更新人
	 */
	    @ApiModelProperty(value = "最后更新人")
	    private String updateBy;
	/**
	 * 班级总人数
	 */
	    @ApiModelProperty(value = "班级总人数")
	    private Integer sumPerson;

	/**
	 * 青马/大骨班班级状态
	 */
		@TableField(exist = false)
		private String ClassStatus;

	/**
	 * 报名部门名称
	 */
		@TableField(exist = false)
		private String deptName;
	/**
	 * 报名年级名称
	 */
		@TableField(exist = false)
		private String NjName;
	/**
	 * 班级报名是否已公示
	 */
		@TableField(exist = false)
		private Boolean bmIsPublic;
	/**
	 * 班级成绩是否已公示
	 */
		@TableField(exist = false)
		private Boolean scIsPublic;
	/**
	 * 成绩确认按钮出现
	 */
		@TableField(exist = false)
		private Boolean isConfirm;
}
