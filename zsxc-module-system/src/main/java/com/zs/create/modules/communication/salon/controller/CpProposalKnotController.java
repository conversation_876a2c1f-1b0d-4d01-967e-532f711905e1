package com.zs.create.modules.communication.salon.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.communication.salon.entity.CpProposalKnotDeatilDto;
import com.zs.create.modules.communication.salon.entity.CpProposalKnotEntity;
import com.zs.create.modules.communication.salon.entity.CpSalonProposalEntity;
import com.zs.create.modules.communication.salon.service.CpProposalKnotService;
import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.communication.salon.service.CpSalonProposalService;
import com.zs.create.modules.item.entity.ScItemFinishEntity;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * @Description 话题结项Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-09 15:18:43
 * @Version: V1.0
 */
@Slf4j
@Api(tags="话题结项")
@RestController
@RequestMapping("/salon/cpProposalKnot")
public class CpProposalKnotController {
    @Autowired
    private CpProposalKnotService cpProposalKnotService;
    @Autowired
    private CpSalonProposalService cpSalonProposalService;
    /**
     * 分页列表查询
     */
    @AutoLog(value = "话题结项-分页列表查询")
    @ApiOperation(value="话题结项-分页列表查询", notes="话题结项-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CpProposalKnotEntity>> queryPageList(CpProposalKnotEntity cpProposalKnot,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<CpProposalKnotEntity>> result = new Result<>();
        QueryWrapper<CpProposalKnotEntity> queryWrapper = QueryGenerator.initQueryWrapper(cpProposalKnot, req.getParameterMap());
        Page<CpProposalKnotEntity> page = new Page<>(pageNo, pageSize);
        IPage<CpProposalKnotEntity> pageList = cpProposalKnotService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "话题结项-添加")
    @ApiOperation(value="话题结项-添加", notes="话题结项-添加")
    @PostMapping(value = "/add")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<CpProposalKnotEntity> add(@RequestBody @Valid CpProposalKnotEntity cpProposalKnot) {
        Result<CpProposalKnotEntity> result = new Result<>();
        if(StringUtils.isBlank(cpProposalKnot.getNewsHref())&&StringUtils.isBlank(cpProposalKnot.getPics())){
            throw new ZsxcBootException("新闻稿链接或图片最少填一个，不能全为空");
        }
        //话题结项驳回后还是结项  结项驳回后重新编辑提交删除原始结项表数据  保证结项表对应话题表只有一条数据
        String proposalId = cpProposalKnot.getProposalId();
        QueryWrapper<CpProposalKnotEntity> knotEntityWrapper = new QueryWrapper<>();
        knotEntityWrapper.eq("proposal_id",proposalId);
        int count = cpProposalKnotService.count(knotEntityWrapper);
        if(count==0){
            cpProposalKnotService.saveProposalKnot(cpProposalKnot);
        }else{
            cpProposalKnotService.remove(knotEntityWrapper);
            cpProposalKnotService.saveProposalKnot(cpProposalKnot);
        }
        result.success("添加成功！");
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "话题结项-编辑")
    @ApiOperation(value="话题结项-编辑", notes="话题结项-编辑")
    @PutMapping(value = "/edit")
    public Result<CpProposalKnotEntity> edit(@RequestBody CpProposalKnotEntity cpProposalKnot) {
        Result<CpProposalKnotEntity> result = new Result<>();
        CpProposalKnotEntity cpProposalKnotEntity = cpProposalKnotService.getById(cpProposalKnot.getId());
        if(cpProposalKnotEntity==null) {
           return result.error500("未找到对应实体");
        }else {
            boolean ok = cpProposalKnotService.updateById(cpProposalKnot);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "话题结项-通过id删除")
    @ApiOperation(value="话题结项-通过id删除", notes="话题结项-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id") String id) {
        cpProposalKnotService.removeById(id);
        return Result.ok("删除成功!");
    }



    /**
     * 通过id查询
     * 封装结项信息并将话题结项表process_instance_id返回
     */
    @AutoLog(value = "话题结项-通过id查询")
    @ApiOperation(value="话题结项-通过id查询", notes="话题结项-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CpProposalKnotDeatilDto> queryById(@RequestParam(name="id") String id) {
        Result<CpProposalKnotDeatilDto> result = new Result<>();
        CpProposalKnotDeatilDto cpProposalKnotDeatilDto = new CpProposalKnotDeatilDto();
        CpSalonProposalEntity cpSalonProposalEntity = cpSalonProposalService.getById(id);
        QueryWrapper<CpProposalKnotEntity> objectQueryWrapper = new QueryWrapper<>();
        objectQueryWrapper.eq("proposal_id",id);
        CpProposalKnotEntity cpProposalKnotServiceOne = cpProposalKnotService.getOne(objectQueryWrapper);
        BeanUtil.copyProperties(cpSalonProposalEntity,cpProposalKnotDeatilDto,true, CopyOptions.create()
                .setIgnoreNullValue(true).setIgnoreError(true));
        cpProposalKnotDeatilDto.setProcessInstanceId(cpProposalKnotServiceOne.getProcessInstanceId())
                .setNewsHref(cpProposalKnotServiceOne.getNewsHref())
                .setPics(cpProposalKnotServiceOne.getPics())
                .setRemark(cpProposalKnotServiceOne.getRemark())
                .setGuestName(cpSalonProposalEntity.getGuestName())
                .setGuestEmail(cpSalonProposalEntity.getGuestEmail());
        result.setResult(cpProposalKnotDeatilDto);
        result.setSuccess(true);
        return result;
    }

}
