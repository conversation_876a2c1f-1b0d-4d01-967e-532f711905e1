package com.zs.create;

import com.zs.create.base.util.JwtUtil;
import com.zs.create.base.util.SpringContextUtils;
import com.zs.create.common.constant.CommonConstant;
import com.zs.create.modules.shiro.authc.JwtToken;
import com.zs.create.modules.statistic.entity.ItemComprehensiveAnalysisDto;
import com.zs.create.modules.statistic.service.ItemComprehensiveAnalysisService;
import com.zs.create.modules.xwl.excelPrecision;
import com.zs.create.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.enmus.ExcelType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2025/9/2
 */
@Slf4j
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class AppTests {

    @Autowired
    private SecurityManager securityManager;

    @BeforeEach
    public void login() {
        SecurityUtils.setSecurityManager(securityManager);
        SecurityUtils.getSubject().login(new JwtToken(generateToken()));
    }

    @Test
    public void empty() {
    }

    /**
     * 生成token
     */
    private String generateToken() {
        String username = "9202013997";
        String password = "Zs#%011040201#PQ";
        String token = JwtUtil.sign(username, password);

        RedisUtil redisUtil = SpringContextUtils.getBean(RedisUtil.class);
        redisUtil.set(CommonConstant.PREFIX_USER_TOKEN + token, token, JwtUtil.EXPIRE_TIME * 2 / 1000);

        log.info("token={}", token);
        return token;
    }

    @Test
    public void test() throws IOException {
        ItemComprehensiveAnalysisService itemComprehensiveAnalysisService = SpringContextUtils.getBean(ItemComprehensiveAnalysisService.class);
        ItemComprehensiveAnalysisDto dto = new ItemComprehensiveAnalysisDto();
        dto.setColumn("createTime");
        dto.setOrder("desc");
        dto.setExamineStatus("32");
        dto.setDepIds(Collections.singletonList("211134"));
        List<ItemComprehensiveAnalysisDto> list = itemComprehensiveAnalysisService.exportList(dto);

        ExportParams exportParams = new ExportParams();
        exportParams.setType(ExcelType.HSSF);
        Map<String, List<Integer>> map = new HashMap<>();
        Integer[] indexArrays1 = {2};
        Integer[] indexArrays2 = {5, 11};
        Integer[] indexArrays3 = {15};
        List<Integer> ArraysToList1 = Arrays.asList(indexArrays1);
        List<Integer> ArraysToList2 = Arrays.asList(indexArrays2);
        List<Integer> ArraysToList3 = Arrays.asList(indexArrays3);
        map.put("0.0", ArraysToList1);
        map.put("0", ArraysToList2);
        map.put("0.00", ArraysToList3);
        Workbook workbook = excelPrecision.getWorkbook(list, ItemComprehensiveAnalysisDto.class, exportParams, map);

        File file = FileUtils.getFile(System.getProperty("user.home"), "Desktop", "export.xls");
        try (FileOutputStream out = FileUtils.openOutputStream(file)) {
            workbook.write(out);
        }
    }
}
