package com.zs.create.modules.oa.prize.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.modules.oa.prize.entity.OaPrizeHourDemandEntity;
import com.zs.create.modules.oa.prize.mapper.OaPrizeHourDemandMapper;
import com.zs.create.modules.oa.prize.service.OaPrizeHourDemandService;
import org.springframework.stereotype.Service;

/**
 * @Description 奖项各学时要求记录Service实现层
 *
 * <AUTHOR> @email 
 * @date 2023-10-16 10:14:03
 * @Version: V1.0
 */
@Service
public class OaPrizeHourDemandServiceImpl extends ServiceImpl<OaPrizeHourDemandMapper, OaPrizeHourDemandEntity> implements OaPrizeHourDemandService {

}
