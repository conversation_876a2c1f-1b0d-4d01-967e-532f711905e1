<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>反面</title>
    <style>
        body {
            margin: 0;
        }

        table {
            border-collapse: collapse;
            border-spacing: 0;
            font-size: 14px;
        }

        td {
            /* padding: 1px; */
        }

        .bg {
            height: 776px;
            width: 1080px;
            margin: 0 auto;
            position: relative;
            background-color: #fff;
            background-image: url([[${qrUrl} + 'images/scReportCard/background.png' ]]);
            background-size: 100%;
            font-family: Microsoft YaHei;
        }

        .back_area {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            padding: 20px;
            z-index: 2;
        }

        .back_top_area {
            margin-bottom: 10px;
        }

        .back_left_title {
            width: 49%;
            display: inline-block;
            text-align: center;
        }

        img {
            width: 70%;
        }

        .back_right_title {
            width: 49%;
            display: inline-block;
            text-align: center;
        }

        .authorTab {
            text-align: center;
            border: 1px solid #4c86da;
            width: 100%;
        }

        .first_tr_td {
            width: 170px;
            height: 30px;
            background-color: #4c86da;
            color: #fff;
        }

        .td_first_span {
            display: inline-block;
            width: 52%;
            text-align: left;
        }

        .td_last_span {
            display: inline-block;
            width: 40%;
            text-align: right;
        }

        .first_span {
            display: inline-block;
            width: 70%;
            text-align: left;
        }

        .last_span {
            display: inline-block;
            width: 20%;
            text-align: right;
            vertical-align: super
        }

        .td_style {
            width: 173px;
        }

        td {
            border: 1px solid #4c86da;
        }
    </style>
</head>
<body>
<div class="bg">
    <div class="back_area">
        <div class="back_top_area">
            <div class="back_left_title">
                <img alt="" src="./images/front_logo.png" th:src="${qrUrl} + 'images/scReportCard/logo.png'"/>
            </div>
            <div class="back_right_title">
                <img alt="" src="./images/score_detail.png" th:src="${qrUrl} + 'images/scReportCard/score_detail.png'"/>
            </div>
        </div>
        <table border="1px solid #4c86da" cellpadding="0" cellspacing="0" class="authorTab">
            <tr>
                <td class="first_tr_td">总学时：（<span th:text="${#numbers.formatDecimal(sumHours, 1, 1)}">100</span>）</td>
                <td class="first_tr_td">德（<span th:text="${#numbers.formatDecimal(hours.d, 1, 1)}">10</span>学时）</td>
                <td class="first_tr_td">智（<span th:text="${#numbers.formatDecimal(hours.z, 1, 1)}">10</span>学时）</td>
                <td class="first_tr_td">体（<span th:text="${#numbers.formatDecimal(hours.t, 1, 1)}">10</span>学时）</td>
                <td class="first_tr_td">美（<span th:text="${#numbers.formatDecimal(hours.m, 1, 1)}">10</span>学时）</td>
                <td class="first_tr_td">劳（<span th:text="${#numbers.formatDecimal(hours.l, 1, 1)}">10</span>学时）</td>
            </tr>
            <tr>
                <td>学年</td>
                <td>
                    <span class="td_first_span">项目名称</span>
                    <span class="td_last_span">学时</span>
                </td>
                <td>
                    <span class="td_first_span">项目名称</span>
                    <span class="td_last_span">学时</span>
                </td>
                <td>
                    <span class="td_first_span">项目名称</span>
                    <span class="td_last_span">学时</span>
                </td>
                <td>
                    <span class="td_first_span">项目名称</span>
                    <span class="td_last_span">学时</span>
                </td>
                <td>
                    <span class="td_first_span">项目名称</span>
                    <span class="td_last_span">学时</span>
                </td>
            </tr>
        </table>
        <table border="1px solid #4c86da" cellpadding="0" cellspacing="0" class="authorTab"
               style="border-top: none;">
            <div th:each="content : ${contentList}" th:remove="tag">
                <tr th:each="_,iter : ${content.itemsRecords?.dCheckedRecords}">
                    <td class="td_style" rowspan="5"
                        style="text-align: center" th:if="${iter.first}" th:rowspan="${iter.size}">
                    <span>
                        <span th:text="${T(com.zs.create.modules.score.util.ReportCardUtils).getXn(content.xn)}">2021-2022学年</span>
                    </span>
                    </td>
                    <td class="td_style"
                        th:each="module : ${T(com.zs.create.modules.score.util.ReportCardUtils).getModuleList()}"
                        th:with="record=${content.itemsRecords?.get(module + 'CheckedRecords')[iter.index]}">
                    <span class="first_span"
                          th:utext="${T(com.zs.create.modules.score.util.ReportCardUtils).getItemName(record.itemName)}">我是我是我是我是<br/>我是我是我是我是</span>
                        <span class="last_span"
                              th:text="${T(com.zs.create.modules.score.util.ReportCardUtils).formatItemHour(record.hours)}">22</span>
                    </td>
                </tr>
            </div>
        </table>
    </div>
</div>
</body>
</html>
