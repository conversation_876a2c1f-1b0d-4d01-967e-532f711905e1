package com.zs.create.modules.oa.prize.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zs.create.common.aspect.annotation.Dict;
import com.zs.create.config.SerializerBigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * 申报人员
 * 
 * <AUTHOR>
 * @email null
 * @date 2021-01-16 16:06:53
 */
@Data
@TableName("oa_declare_user")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oa_declare_user对象", description="申报人员")
public class OaDeclareUserEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public static final String ZC = "-2";//暂存
	public static final String APPLY = "1";//申请中
	public static final String AUDIT_REJECT = "3";//驳回不可修改
	public static final String AUDIT_EDIT = "6";//修改后提交

	public static final String AUDIT_PASS = "2";//已审核
	public static final String START_PUBLIC = "4";//公示中
	public static final String END_PUBLIC = "5";//公示已结束
	public static final String WAIT_REVIEW = "7";//待评审
	public static final String START_REVIEW = "8";//评审中
	public static final String END_REVIEW = "9";//已评审
	public static final String REVOKE = "10";//撤回
	public static final String GAIN_PRIZE = "11";//已获奖
	public static final String NO_GAIN_PRIZE = "12";//未获奖

	public static final Integer PROCESS_REJECT = 0;
	public static final Integer PROCESS_PASS = 1;

	public static final String QUERY_TODO = "todo";
	public static final String QUERY_HISTORY = "history";

	@TableField(exist = false)
	@ApiModelProperty(value = "查询类型 ： todo ： 代办   history ：  已办")
	private String queryType;

	@TableField(exist = false)
	private String assignee;

	@TableField(exist = false)
	private String taskId;

	@TableField(exist = false)
	private String taskName;

	@TableField(exist = false)
	private String type;

	@TableField(exist = false)
	private String searchType;

	/**
	 * 
	 */
	@TableId
	private String id;
	/**
	 * 学生id
	 */
	@ApiModelProperty(value = "学生id")
	private String userId;
	/**
	 * 学号
	 */
	@ApiModelProperty(value = "学号")
	private String username;
	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	private String realname;
	/**
	 * 奖项类型
	 */
	@ApiModelProperty(value = "奖项类型")
	private String materialUrl;

	/**
	 * 类型名称
	 * */
	@ApiModelProperty(value = "类型名称")
	private String materialName;

	/**
	  获奖等级
	 * */
	@ApiModelProperty(value = "获奖等级")
	@Dict(dicCode = "id" ,dictTable = "oa_prize_level" ,dicText = "name")
	private String awardLevel;

	/**
	获奖等级结果
	 * */
	@ApiModelProperty(value = "获奖等级结果")
	@Dict(dicCode = "id" ,dictTable = "oa_prize_level" ,dicText = "name")
	private String awardLevelResult;

	/**
	  获奖等级
	 * */
	@ApiModelProperty(value = "成绩")
	private BigDecimal score;

	/**
	 * 学时
	 */
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	@ApiModelProperty(value = "学时")
	private BigDecimal hours;

	/**
	 * 状态 0 未申报、-2 暂存、10 撤回、1 申报中、2 已审核、3 驳回、6 驳回待修改、7 待评审、8 评审中、9 已评审、4 公示中、5 公示已结束、11 已获奖、12 未获奖
	 */
	@ApiModelProperty(value = "状态 0 未申报、-2 暂存、10 撤回、1 申报中、2 已审核、3 驳回、6 驳回待修改、7 待评审、8 评审中、9 已评审、4 公示中、5 公示已结束、11 已获奖、12 未获奖")
	private String status;

	/**
	 * 奖项id
	 */
	@ApiModelProperty(value = "奖项id")
	@Dict(dicCode = "id" ,dictTable = "oa_prize" ,dicText = "title")
	private String prizeId;

	/**
	 * 申请表信息
	 */
	@ApiModelProperty(value = "申请表信息")
	@TableField(exist = false)
	private Map memberFormData;

	/**
	 * 申请表JSON信息
	 */
	@ApiModelProperty(value = "申请表JSON信息")
	private String material;

	/**
	 * 流程实例id
	 */
	@ApiModelProperty(value = "流程实例id")
	private String processInstanceId;

	/**
	 * 当前节点key值
	 */
	@ApiModelProperty(value = "当前节点id")
	private String nowTaskId;

	/**
	 * 当前节点key值
	 */
	@ApiModelProperty(value = "当前节点key值")
	private String nowTaskKey;

	/**
	 * 当前节点名称
	 */
	@ApiModelProperty(value = "当前节点名称")
	private String nowTaskName;

	/**
	 * 公示开始时间
	 */
	private Date publicitySt;

	/**
	 * 公示结束时间
	 */
	private Date publicityEt;

	/**
	 * 创建人
	 */
	private String createBy;

	/**
	 * 创建时间
	 */
	private Date createTime;

	@TableField(exist = false)
	private String prizeIdDictText;

	//@TableField(exist = false)
	private String depId;

	@TableField(exist = false)
	private String module;

	@TableField(exist = false)
	private String prizeName;

	private Integer isShow;//0 未公示 1 已公示

	private Integer isAdd;//0 未公示 1 已公示

	/**
	 * 是否审核  0：未审核  1：已审核
	 */
	private Integer isReview;

	@TableField(exist = false)
	private String declareType;

	/**
	 * 排序
	 */
	@TableField(exist = false)
	private String sort;

	/**
	 * 是否是自组织团支部，0不是，1是
	 */
	private String tzbType;

}
