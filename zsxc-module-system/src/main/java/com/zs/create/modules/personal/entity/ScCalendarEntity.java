package com.zs.create.modules.personal.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.hibernate.validator.constraints.Length;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.Date;

/**
 * 我的日程
 * 
 * <AUTHOR>
 * @email null
 * @date 2020-07-06 16:21:56
 */
@Data
@TableName("sc_calendar")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_calendar对象", description="我的日程")
public class ScCalendarEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private String id;
	/**
	 * 内容
	 */
	@Length(max=255, message = "内容长度最长为255")
	private String title;
	/**
	 * 内容
	 */
	@Length(max=255, message = "地点长度最长为255")
	private String place;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 开始时间
	 */
	private Date startTime;
	/**
	 * 结束时间
	 */
	private Date endTime;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 项目id
	 */
	private String itemId;
	/**
	 * 删除标记
	 */
	//@TableLogic
	private Integer delFlag;
	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改人
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

}
