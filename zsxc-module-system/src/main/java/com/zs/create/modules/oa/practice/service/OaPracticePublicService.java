package com.zs.create.modules.oa.practice.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.oa.practice.dto.OapracticeTeamDto;
import com.zs.create.modules.oa.practice.entity.OaPracticePublicEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * @Description 社会实践公示表Service层
 *
 * <AUTHOR> @email 
 * @date 2023-01-17 16:03:52
 * @Version: V1.0
 */
public interface OaPracticePublicService extends IService<OaPracticePublicEntity> {

    Result addPublic(String practiceItemId, Integer type);

    IPage<OaPracticePublicEntity> pagePublic(Integer pageNo, Integer pageSize,String queryFlag);

    List<OapracticeTeamDto> bmPublicList(String itemId, String queryFlag, String userCode);

    List<OapracticeTeamDto> jxPublicList(String itemId, String queryFlag, String userCode);

    /**
     * 查询社会实践报名公示数据
     * @param practiceItemId
     * @return
     */
    OaPracticePublicEntity qryPublicByType(String practiceItemId,String type);

    OaPracticePublicEntity qryByItemId(String practiceItemId,String type);

    /**
     * 查询社会实践报名公示数据
     * @param type
     * @return
     */
    List<OaPracticePublicEntity> qryBmPublicList(String type,String status);
}

