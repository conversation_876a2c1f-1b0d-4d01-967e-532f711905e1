package com.zs.create.base.util;

import com.zs.create.base.entity.ZipModel;
import com.zs.create.modules.system.service.ISysFileService;
import org.springframework.context.ApplicationContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.context.support.WebApplicationContextUtils;

import javax.servlet.ServletContext;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * @createUser hy
 * @createTime 2020-7-18
 * @description
 */
public class ZipUtil {

    private static String IFastdfsService = "com.zs.create.modules.system.service.ISysFileService";

    /**
     * 压缩文件列表中的文件
     *
     * @param files
     * @param outputStream
     * @throws IOException
     */
    public static void zipFile(List<ZipModel> files, ZipOutputStream outputStream) throws IOException {
        try {
            int size = files.size();
            //压缩列表中的文件
            for (int i = 0; i < size; i++) {
                ZipModel zipModel = files.get(i);
                try {
                    zipFile(zipModel, outputStream);
                } catch (Exception e) {
                    continue;
                }
            }
        } catch (Exception e) {
            throw e;
        }
    }

    /**
     * 将文件写入到zip文件中
     *
     * @param zipModel
     * @param outputstream
     * @throws IOException
     */
    public static void zipFile(ZipModel zipModel, ZipOutputStream outputstream) throws Exception {
        try {
            if (zipModel != null && zipModel.getFilePath() != null && zipModel.getFileName() != null) {
                ISysFileService iFastdfsService = (ISysFileService) getBean(IFastdfsService);
                InputStream bInStream = new ByteArrayInputStream(iFastdfsService.getFile(zipModel.getFilePath()));
                ZipEntry entry = new ZipEntry(zipModel.getFileName());
                outputstream.putNextEntry(entry);

                final int MAX_BYTE = 10 * 1024 * 1024; //最大的流为10M
                long streamTotal = 0;      //接受流的容量
                int streamNum = 0;      //流需要分开的数量
                int leaveByte = 0;      //文件剩下的字符数
                byte[] inOutbyte;       //byte数组接受文件的数据

                streamTotal = bInStream.available();      //通过available方法取得流的最大字符数
                streamNum = (int) Math.floor(streamTotal / MAX_BYTE); //取得流文件需要分开的数量
                leaveByte = (int) streamTotal % MAX_BYTE;    //分开文件之后,剩余的数量

                if (streamNum > 0) {
                    for (int j = 0; j < streamNum; ++j) {
                        inOutbyte = new byte[MAX_BYTE];
                        //读入流,保存在byte数组
                        bInStream.read(inOutbyte, 0, MAX_BYTE);
                        outputstream.write(inOutbyte, 0, MAX_BYTE); //写出流
                    }
                }
                //写出剩下的流数据
                inOutbyte = new byte[leaveByte];
                bInStream.read(inOutbyte, 0, leaveByte);
                outputstream.write(inOutbyte);
                outputstream.closeEntry();
                bInStream.close(); //关闭
            }
        } catch (IOException e) {
            throw e;
        }
    }

    /**
     * 下载打包的文件
     *
     * @param file
     * @param response
     */
    public static void downloadZip(File file, String fileName,  HttpServletResponse response) {
        try {
            if (!file.exists()) {
                file.createNewFile();
            }
            // 以流的形式下载文件。
            BufferedInputStream fis = new BufferedInputStream(new FileInputStream(file.getPath()));
            byte[] buffer = new byte[fis.available()];
            fis.read(buffer);
            fis.close();
            // 清空response
            response.reset();

            OutputStream toClient = new BufferedOutputStream(response.getOutputStream());
            response.setContentType("application/octet-stream");
            response.addHeader("Content-Disposition", "attachment;fileName=" + new String(fileName.getBytes("UTF-8"), "iso-8859-1"));
            toClient.write(buffer);
            toClient.flush();
            toClient.close();
            //file.delete();  //将生成的服务器端文件删除
        } catch (IOException ex) {
            ex.printStackTrace();
        }finally {
            File f = new File(file.getPath());
            f.delete();
        }
    }

    /**
     * 获得bean,通过ApplicationContext获取
     *
     * @return
     */
    public static Object getBean(String className) {
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        ServletContext sc = request.getSession().getServletContext();
        ApplicationContext ac = WebApplicationContextUtils.getRequiredWebApplicationContext(sc);
        Class c;
        try {
            c = Class.forName(className);
            return ac.getBean(c);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }
}
