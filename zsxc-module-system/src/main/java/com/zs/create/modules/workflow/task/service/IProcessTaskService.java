package com.zs.create.modules.workflow.task.service;

import com.zs.create.modules.workflow.common.enums.TaskStatusEumn;
import org.activiti.engine.task.Task;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> guodl
 * @Date: 2019/8/9 09:39
 * @Description:流程任务接口
 */
public interface IProcessTaskService {
    /**
     * 查询个人任务
     *
     * @param taskAssignee         用户唯一标识(userId)
     * @param firstResult(从第几条开始查询 0开始 LIMIT 10 OFFSET 0
     * @param maxResults(每次查询多少条)  LIMIT 10 OFFSET 0
     * @return Map<String,Object> count:总条数
     */
    Map<String, Object> queryMyDaiBanTaskList(String taskAssignee, int firstResult, int maxResults);

    /**
     * 查询多人任务(任务类型为多人任务）
     *
     * @param taskAssignee         用户唯一标识(userId)
     * @param firstResult(从第几条开始查询 0开始 LIMIT 10 OFFSET 0
     * @param maxResults(每次查询多少条)  LIMIT 10 OFFSET 0
     * @return Map<String,Object> count:总条数
     */
    Map<String, Object> queryCandidateUserTaskList(String taskAssignee, int firstResult, int maxResults);

    /**
     * 查询角色组任务
     *
     * @param groupIds             用户角色组ID集合
     * @param firstResult(从第几条开始查询 0开始 LIMIT 10 OFFSET 0
     * @param maxResults(每次查询多少条)  LIMIT 10 OFFSET 0
     * @return Map<String,Object> count:总条数
     */
    Map<String, Object> queryCandidateGroupTaskList(List<String> groupIds, int firstResult, int maxResults);

    /**
     * 查询历史任务
     *
     * @param taskAssignee         用户唯一标识(userId)
     * @param firstResult(从第几条开始查询 0开始 LIMIT 10 OFFSET 0
     * @param maxResults(每次查询多少条)  LIMIT 10 OFFSET 0
     * @return Map<String,Object> count:总条数
     */
    Map<String, Object> queryHistroyTaskList(String taskAssignee, int firstResult, int maxResults);

    /**
     * 查询个人任务
     *
     * @param taskId 任务ID
     * @return map 包括 task 和流程变量
     */
    Map<String, Object> getTask(String taskId);

    /**
     * 完成任务
     *
     * @param taskId    完成任务
     * @param variables 流程变量
     */
    TaskStatusEumn compeleteTask(String taskId, Map<String, Object> variables);

    /**
     * 拾取任务
     * 主要针对组任务、多人任务的时候，需要拾取该
     * 任务变成个人认为
     *
     * @param taskId
     * @param userId
     * @return
     */
    void claimTask(String taskId, String userId);

    /**
     * 根据任务ID获取个人任务信息
     * @param taskId
     * @return
     */
    Task getTaskById(String taskId);
}
