package com.zs.create.modules.item.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @createUser lzd
 * @createTime 2020-7-29
 * @description
 */
@Data
@Accessors(chain = true)
@ApiModel(value="ScItemListVo", description="项目返回显示")
public class ScItemListVo extends ScItemEntity implements Serializable {
    @ApiModelProperty(value = "模块名称")
    private String module_dictText;
    @ApiModelProperty(value = "形式名称")
    private String form_dictText;
    @ApiModelProperty(value = "结束时间")
    private String endTime;
    @ApiModelProperty(value = "报名状态")
    private String itemStatus_dictText;
}
