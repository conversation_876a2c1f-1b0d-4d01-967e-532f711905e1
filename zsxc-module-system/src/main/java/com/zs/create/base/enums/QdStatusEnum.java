package com.zs.create.base.enums;

public enum QdStatusEnum {

    data_null(200,""),
    zc_status(-1,"暂存"),
    wsh_status(0,"申请中"),
    shz_status(1,"申请中"),
    ysh_status(2,"已审核"),
    btjbm_status(3,"不推荐报名"),
    xghztj_status(4,"修改再提交"),
    gsz_status(5,"公示中"),
    gsyjs_status(6,"公示已结束"),
    ybm_status(7,"已报名"),
    yjy_status(8,"已结业"),
    wbm_status(99,"未报名");

    private Integer code;
    private String desc;

     QdStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static QdStatusEnum codeOf(Integer code){
        for (QdStatusEnum qdStatusEnum : values()) {
            if (qdStatusEnum.getCode().equals(code)) {
                return qdStatusEnum;
            }
        }
        return QdStatusEnum.data_null;
    }
}
