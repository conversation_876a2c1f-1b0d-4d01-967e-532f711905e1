package com.zs.create.modules.oa.credit.service;

import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.oa.credit.entity.OaConfigureCourseEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description 学分申请Service层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-18 09:40:16
 * @Version: V1.0
 */
public interface OaConfigureCourseService extends IService<OaConfigureCourseEntity> {

    Result<List<OaConfigureCourseEntity>>  selectCourselist();

    /**
     * 新增学分申请配置
     * @param oaConfigureCourse
     */
    void saveCourse(OaConfigureCourseEntity oaConfigureCourse);

    /**
     * 修改学分申请配置
     * @param oaConfigureCourse
     * @return
     */
    boolean updateCourse(OaConfigureCourseEntity oaConfigureCourse);

    OaConfigureCourseEntity queryById(String id);
}

