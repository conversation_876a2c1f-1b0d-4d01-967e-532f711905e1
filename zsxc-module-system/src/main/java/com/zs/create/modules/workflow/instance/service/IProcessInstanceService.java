package com.zs.create.modules.workflow.instance.service;

import org.activiti.engine.runtime.ProcessInstance;

import java.util.Map;

/**
 * @Auther: guodl
 * @Date: 2019/8/8 16:16
 * @Description:
 */
public interface IProcessInstanceService {
    /**
     * 启动流程实例
     *
     * @param processDefinitionKey 流程定义的key
     * @param variable             流程变量
     * @param enableTaskListener   是否启用任务监听
     */
    ProcessInstance startProcessInstance(String processDefinitionKey, Map<String, Object> variable, Boolean enableTaskListener);

    /**
     * 判断流程实例是否结束
     *
     * @param processInstanceId 流程实例ID
     * @return
     */
    boolean judgeProcessIsEnd(String processInstanceId);
}
