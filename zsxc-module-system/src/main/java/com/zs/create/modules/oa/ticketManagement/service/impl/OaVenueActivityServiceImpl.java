package com.zs.create.modules.oa.ticketManagement.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.utils.PseudoPagingUtil;
import com.zs.create.modules.oa.ticketManagement.entity.*;
import com.zs.create.modules.oa.ticketManagement.mapper.OaVenueActivityMapper;
import com.zs.create.modules.oa.ticketManagement.service.*;
import com.zs.create.modules.paramdesign.entity.ScWeeksEntity;
import com.zs.create.modules.paramdesign.mapper.ScWeeksMapper;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.entity.SysUserDepart;
import com.zs.create.modules.system.mapper.SysUserDepartMapper;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.system.service.ISysUserService;
import jodd.util.StringPool;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;


/**
 * @Description 票券管理—活动Service实现层
 *
 * <AUTHOR> @email 
 * @date 2023-04-04 08:48:35
 * @Version: V1.0
 */
@Service
public class OaVenueActivityServiceImpl extends ServiceImpl<OaVenueActivityMapper, OaVenueActivityEntity> implements OaVenueActivityService {

    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private OaVenueActivitySeatService oaVenueActivitySeatService;
    @Resource
    private ScWeeksMapper scWeeksMapper;
    @Resource
    private SysUserDepartMapper sysUserDepartMapper;
    @Autowired
    private OaVenueActOrderService oaVenueActOrderService;
    @Autowired
    private OaVenueActivityDevolutionService oaVenueActivityDevolutionService;
    @Autowired
    private OaVenueActivityImportService oaVenueActivityImportService;

    private static final String TEACHER = "6";
    private static final String GRADUATE = "5";

    @Override
    public List<OaVenueActivityEntity> getFutureActByVenueId(String venueId) {
        return this.baseMapper.getFutureActByVenueId(venueId);
    }

    @Override
    @Transactional
    public Result<OaVenueActivityEntity> addAct(OaVenueActivityEntity oaVenueActivity) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Boolean twPersonInCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
        if (!twPersonInCharge) throw new ZsxcBootException("非团委负责人不可新增");
        //校验抢票时间在开始时间之后
         if (oaVenueActivity.getGetTicketEt().compareTo(oaVenueActivity.getSt()) > 0)
            throw new ZsxcBootException("购票时间段需在活动开始时间前");


        //校验当前场馆当前时间是否闲置
        int count = this.checkActTimeRepeat(oaVenueActivity.getVenueId(),oaVenueActivity.getSt(),oaVenueActivity.getEt(),null);
        if (count!=0) throw new ZsxcBootException("当前时间该场馆已被预约");


        //范围必须有一个
        //无导入名单时，deptId 和 nj 必须有一个
        if (oConvertUtils.isEmpty(oaVenueActivity.getUuidKey())){
            if (oConvertUtils.isEmpty(oaVenueActivity.getDeptId()) && oConvertUtils.isEmpty(oaVenueActivity.getNj())){
                throw new ZsxcBootException("开放对象不可为空！");
            }
        }

        List<OaVenueActivitySeatEntity> seatList = new ArrayList<>();
        oaVenueActivity.getSeatActInfoList().forEach(seatList::addAll);
//        //判断购票限制是否超过可用座位数
//        long seatCanUse = seatList.stream().filter(t -> t.getIsSeat().equals(0)).count();
//        if (Integer.parseInt(oaVenueActivity.getTicketLimit()) >(int)seatCanUse )
//            throw new ZsxcBootException("最大购票数不可超过可用座位数量！");

        Result<OaVenueActivityEntity> result = new Result<OaVenueActivityEntity>();
        oaVenueActivity.setCreateBy(sysUser.getUsername()).setUpdateBy(sysUser.getUsername());
        this.save(oaVenueActivity);

        //座位信息数组赋值

        if (seatList.size()>0){
            seatList.forEach(t ->{
                t.setActivityId(oaVenueActivity.getId())
                        .setCreateBy(sysUser.getUsername()).setUpdateBy(sysUser.getUsername()).setId(null);
//                if (oConvertUtils.isNotEmpty(t.getSeatNumber())){
//                    t.setXNumbering(t.getCodeX());
//                }
            });

        }
        //活动-座位信息落库
        oaVenueActivitySeatService.saveBatch(seatList);
        result.success("添加成功！");
        return result;
    }

    @Override
    public IPage<OaVenueActivityEntity> queryPage(Page<OaVenueActivityEntity> page, OaVenueActivityEntity oaVenueActivity,Integer pageNo,Integer pageSize,Integer query) {

        //查询前先更新状态，只更新未开始和进行中的
        LambdaQueryWrapper<OaVenueActivityEntity> statusWrapper = new LambdaQueryWrapper<>();
        statusWrapper.in(OaVenueActivityEntity::getStatus,1,0);
        List<OaVenueActivityEntity> list = this.list(statusWrapper);
        Date now = new Date();
        list.forEach(t ->{
            if (now.compareTo(t.getGetTicketSt())> 0 && now.compareTo(t.getGetTicketEt()) <0) t.setStatus(0).setStart("1");
            if (now.compareTo(t.getGetTicketEt())> 0 ) t.setStatus(2).setStart("2");
            if (now.compareTo(t.getGetTicketSt())< 0 ) t.setStatus(1).setStart("0");
        });
        if (list.size()>0) {this.updateBatchById(list);}

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Boolean twPersonInCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
        if (twPersonInCharge) {
//            IPage<OaVenueActivityEntity> oaVenueActivityEntityIPage = this.baseMapper.queryPage(page, oaVenueActivity, query);
            IPage<OaVenueActivityEntity> oaVenueActivityEntityIPage = this.baseMapper.pageListAct(page, oaVenueActivity, query);
            List<OaVenueActivityEntity> records = oaVenueActivityEntityIPage.getRecords();
            //联查已购票数，赋值
            if (records.size()>0) {
                records.forEach(t ->{
                    //23.04.11 只统计已确认的订单，如有未确认订单目前 让他直接跳转订单确认页面，有变动再改
                    LambdaQueryWrapper<OaVenueActOrderEntity> wrapper = new LambdaQueryWrapper<>();
                    wrapper.eq(OaVenueActOrderEntity::getActivityId,t.getId());
                    wrapper.eq(OaVenueActOrderEntity::getUserId,sysUser.getUsername());
                    wrapper.eq(OaVenueActOrderEntity::getStatus,1);
                    int count = oaVenueActOrderService.count(wrapper);
                    t.setOwnTicket(count);

                    //拼接组织方名字
                    List<String> newList = new ArrayList<>();
                    List<String> deptIdList = Arrays.stream(t.getBusinessDeptId().split(","))
                            .map(String::trim)
                            .collect(toList());
                    Collection<SysDepart> depts = new ArrayList<>();
                    for (String depId : deptIdList) {
                        SysDepart byId = sysDepartService.getById(depId);
                        if (oConvertUtils.isNotEmpty(byId)) {
                            depts.add(byId);
                            newList.add(depId);
                        }
                    }
                    String collect = newList.stream().collect(Collectors.joining(StringPool.COMMA));
                    t.setBusinessDeptId(collect);
                    if (!CollectionUtils.isEmpty(depts)) {
                        String businessDeptName = depts.stream().map(SysDepart::getDepartName).collect(Collectors.joining(StringPool.COMMA));
                        t.setBusinessDeptName(businessDeptName);
                    }

                    //增加状态值显示 pc用（活动举办时间判断）
                    if (now.compareTo(t.getSt()) <0){
                        t.setActStatus("未开始");
                    }else if (now.compareTo(t.getSt()) >= 0 && now.compareTo(t.getEt()) < 0){
                        t.setActStatus("进行中");
                    }else {
                        t.setActStatus("已结束");
                    }

                    t.setDuration(t.getDuration()+"分钟");
                });
                oaVenueActivityEntityIPage.setRecords(records);
            }
            return oaVenueActivityEntityIPage;
        } else {
            return new Page<>();
        }
    }

    /**
     * 根据范围排除活动
     */
    private void removeAct(List<OaVenueActivityEntity> actList, String username) {
        SysUser sysUser = sysUserService.getUserById(username);
        Boolean twCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
        //查询托管表，根据学号查询所有被托管活动
        LambdaQueryWrapper<OaVenueActivityDevolutionEntity> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(OaVenueActivityDevolutionEntity::getUsername,username);
        List<OaVenueActivityDevolutionEntity> list = oaVenueActivityDevolutionService.list(queryWrapper);
        //取出所有的活动id，在下方进行匹配
        List<String> activityIdList = list.stream().map(OaVenueActivityDevolutionEntity::getActivityId).collect(Collectors.toList());

        //查询当前人员被导入的项目活动
        LambdaQueryWrapper<OaVenueActivityImportEntity> queryWrapper1=new LambdaQueryWrapper<>();
        queryWrapper1.eq(OaVenueActivityImportEntity::getUserId,username);
        List<OaVenueActivityImportEntity> importEntities = oaVenueActivityImportService.list(queryWrapper1);
        //取出所有的活动uuidKey，在下方进行匹配
        List<String> uuidKeyList = importEntities.stream().map(OaVenueActivityImportEntity::getUuidKey).collect(Collectors.toList());

        //根据范围排除
        ScWeeksEntity scWeeksEntity = scWeeksMapper.selectCurrentWeek();
        Iterator<OaVenueActivityEntity> iterator = actList.iterator();
        while (iterator.hasNext()) {
            OaVenueActivityEntity oaVenueActivity = iterator.next();
            //是否可以购票，初始为全可以
            oaVenueActivity.setIsCan("1");
//
            //0425 团委默认全部被托管,展示所有
            if (twCharge){
                oaVenueActivity.setHosting("1");
            }else {
                //判断是否被托管，被托管hosting字段设为 “1”
                if (activityIdList.size()>0) {
                    if (activityIdList.contains(oaVenueActivity.getId())){
                        oaVenueActivity.setHosting("1");
                    }
                }
            }
            //uuidKeyList为当前登陆人被导入的所有活动的uuid集合
            //是否是此活动导入人员，是为ture,不是为false
            boolean isImport = uuidKeyList.contains(oaVenueActivity.getUuidKey());

            //如果只有导入人员，部门及年级都未选，则判断是否是导入及是否托管即可跳出
            if (!isImport && oConvertUtils.isEmpty(oaVenueActivity.getNj())
                    && oConvertUtils.isEmpty(oaVenueActivity.getDeptId())){
                oaVenueActivity.setIsCan("0");
                if (oaVenueActivity.getHosting().equals("0")){//同时非托管，则移除
                    iterator.remove();
                    continue;
                }
            }

            if (oConvertUtils.isNotEmpty(oaVenueActivity.getNj())) {
                //当前时间没有学年学期，例如寒假暑假则无法区分大几  过滤掉
                if (scWeeksEntity == null) {
                    iterator.remove();
                    continue;
                }
                String grade = sysUser.getGrade();   //当前用户所在年级  2012级学生就是2012年进校  大一
                SimpleDateFormat format = new SimpleDateFormat("yyyy");
                int year = Integer.parseInt(format.format(new Date()));
                int currentNj;   //  5研究生   6老师
                if ("T".equals(sysUser.getType())) {   //该用户为老师  T老师没有grade
                    if (!oaVenueActivity.getNj().contains(TEACHER)) {
                        if (!isImport){oaVenueActivity.setIsCan("0");}
                        if (!oaVenueActivity.getHosting().equals("1") && !isImport) {
                            iterator.remove();
                            continue;
                        }
                    }
                } else if ("G".equals(sysUser.getType())) {  //该用户为研究生    G研究生有grade
                    if (!oaVenueActivity.getNj().contains(GRADUATE)) {
                        if (!isImport){oaVenueActivity.setIsCan("0");}
                        if (!oaVenueActivity.getHosting().equals("1") && !isImport) {
                            iterator.remove();
                            continue;
                        }
                    }
                } else if ("S".equals(sysUser.getType())) {   //该用户为学生
                    if (StringUtils.isNotEmpty(grade)) {
                        //2018年9月份入学--2019年9月份为大一    2019年9月份-2020年9月份为大二
                        //如果当前时间为2020年第一学期为大二-------2020-2018    如果当前时间为2020年第二学期为大三-----2020-2018+1
                        if (scWeeksEntity.getXq().equals("1")) {
                            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM");
                            int month = Integer.parseInt(simpleDateFormat.format(new Date()));
                            if (month > 6) {
                                currentNj = year - Integer.parseInt(grade) + 1;
                            } else {
                                currentNj = year - Integer.parseInt(grade);
                            }
                        } else {
                            //新的大一班级年级为当前年
                            currentNj = year - Integer.parseInt(grade);
                        }
                        if (currentNj > 4) {
                            if (!isImport){oaVenueActivity.setIsCan("0");}
                            if (!oaVenueActivity.getHosting().equals("1") && !isImport) {
                                iterator.remove();
                                continue;
                            }
                        } else {
                            if (!oaVenueActivity.getNj().contains(String.valueOf(currentNj))) {
                                if (!isImport){oaVenueActivity.setIsCan("0");}
                                if (!oaVenueActivity.getHosting().equals("1") && !isImport) {
                                    iterator.remove();
                                    continue;
                                }
                            }
                        }
                    } else {   //该学生没有年级字段  数据有误 则不让他报名
                        iterator.remove();
                        oaVenueActivity.setIsCan("0");
                        continue;
                    }
                } else {   //其他身份
                    iterator.remove();
                    oaVenueActivity.setIsCan("0");
                    continue;
                }
            }

            if (StringUtils.isNotEmpty(oaVenueActivity.getDeptId())) {
                String[] deptid = oaVenueActivity.getDeptId().split(",");
                QueryWrapper<SysUserDepart> departQueryWrapper = new QueryWrapper<>();
                departQueryWrapper.eq("user_id", sysUser.getUsername());
                List<SysUserDepart> sysUserDeparts = sysUserDepartMapper.selectList(departQueryWrapper);//用户所在部门
                ArrayList<String> userDepartList = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(sysUserDeparts)) {
                    for (SysUserDepart sysUserDepart : sysUserDeparts) {
                        userDepartList.add(sysUserDepart.getDepId());
                    }
                }
                List<String> deptIdList = Arrays.asList(deptid);
                //当两个集合中没有相同的元素的时候 返回 true 。当有相同的元素的时候返回 false.
                boolean flag = Collections.disjoint(userDepartList, deptIdList);
                if (flag) {
                    if (!isImport){oaVenueActivity.setIsCan("0");}
                    if (!oaVenueActivity.getHosting().equals("1") && !isImport) {
                        iterator.remove();
                        continue;
                    }
                }
            }
        }
    }

    @Override
    public Result<OaVenueActivityEntity> queryById(String id, Integer type) {
        Result<OaVenueActivityEntity> result = new Result<OaVenueActivityEntity>();
        OaVenueActivityEntity oaVenueActivity = this.baseMapper.getActById(id);
        if (oConvertUtils.isEmpty(oaVenueActivity)) throw new ZsxcBootException("未找到对应活动信息");
        List<OaVenueActivityEntity> reList = new ArrayList<>();
        reList.add(oaVenueActivity);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Boolean twPersonInCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
        this.removeAct(reList,sysUser.getUsername());
        if (reList.size() == 0 && !twPersonInCharge){
            throw new ZsxcBootException("您不在此活动开放对象内！");
        }

        //更新活动状态
        Date now = new Date();
        if (now.compareTo(oaVenueActivity.getGetTicketSt())> 0 && now.compareTo(oaVenueActivity.getGetTicketEt()) <0) oaVenueActivity.setStatus(0).setStart("1");
        if (now.compareTo(oaVenueActivity.getGetTicketEt())> 0 ) oaVenueActivity.setStatus(2).setStart("2");
        this.updateById(oaVenueActivity);

        if (now.compareTo(oaVenueActivity.getGetTicketSt()) < 0 ) oaVenueActivity.setStart("0");
        if (!type.equals(1)) {//1代表移动端查询
            //找座位信息，填充可用座位
            LambdaQueryWrapper<OaVenueActivitySeatEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaVenueActivitySeatEntity::getActivityId,id);
            List<OaVenueActivitySeatEntity> actSeatEntityList = oaVenueActivitySeatService.list(wrapper);


            LambdaQueryWrapper<OaVenueActOrderEntity> orderWrapper = new LambdaQueryWrapper<>();
            orderWrapper.eq(OaVenueActOrderEntity::getStatus,0);
            orderWrapper.ne(OaVenueActOrderEntity::getUserId,sysUser.getUsername());
            List<String> otherReserveSeatList = oaVenueActOrderService.list(orderWrapper).stream().map(OaVenueActOrderEntity::getActSeatId).collect(Collectors.toList());
            //转为二维数组
            List<List<OaVenueActivitySeatEntity>> actSeatInfo = oaVenueActivitySeatService.packSeat(actSeatEntityList);
            oaVenueActivity.setSeatActInfoList(actSeatInfo);

            //封装部门/年级名称
            if (StringUtils.isNotBlank(oaVenueActivity.getDeptId())) {
                Collection<SysDepart> sysDeparts = sysDepartService.listByIds(Arrays.asList(oaVenueActivity.getDeptId().split(",")));
                String deptBuilder = "";
                if (!CollectionUtils.isEmpty(sysDeparts)) {
                    //拼接部门名称
                    StringBuilder stringBuilder = new StringBuilder();
                    sysDeparts.forEach(sysDepart -> {
                        stringBuilder.append(sysDepart.getDepartName()).append(",");
                    });
                    deptBuilder = stringBuilder.toString();
                    deptBuilder = deptBuilder.substring(0, deptBuilder.length() - 1);
                }
                oaVenueActivity.setDeptName(deptBuilder);
            }
            if (StringUtils.isNotBlank(oaVenueActivity.getNj())) {
                StringBuilder njName = new StringBuilder();
                String[] list = oaVenueActivity.getNj().split(",");
                for (String s : list) {
                    if (s.equals("1")) {
                        njName.append("大一" + ",");
                    } else if (s.equals("2")) {
                        njName.append("大二" + ",");
                    } else if (s.equals("3")) {
                        njName.append("大三" + ",");
                    } else if (s.equals("4")) {
                        njName.append("大四" + ",");
                    } else if (s.equals("5")) {
                        njName.append("研究生" + ",");
                    } else {
                        njName.append("老师" + ",");
                    }
                }
                oaVenueActivity.setNjName(njName.substring(0, njName.length() - 1));
            }
        }
        if (!type.equals(1)) {
            //23.04.11 只统计已确认的订单，如有未确认订单目前 让他直接跳转订单确认页面，有变动再改
            LambdaQueryWrapper<OaVenueActOrderEntity> wrappers = new LambdaQueryWrapper<>();
            wrappers.eq(OaVenueActOrderEntity::getActivityId, oaVenueActivity.getId());
            wrappers.eq(OaVenueActOrderEntity::getUserId, sysUser.getUsername());
            wrappers.eq(OaVenueActOrderEntity::getStatus, 1);
            int count = oaVenueActOrderService.count(wrappers);
            oaVenueActivity.setOwnTicket(count);
            Map<String, BigDecimal> kindNum = oaVenueActivitySeatService.getTicketKindNum(oaVenueActivity.getId());
            oaVenueActivity.setSaleTicket(kindNum.get("saleTicket").toString());
            oaVenueActivity.setWaitTicket(kindNum.get("waitTicket").toString());
            oaVenueActivity.setLockTicket(kindNum.get("lockTicket").toString());
            oaVenueActivity.setBanTicket(kindNum.get("banTicket").toString());
        }

        if (type.equals(1)) {
            //0423 增加字段移动端判断按钮状态
            //1.统计是否有可购票
            LambdaQueryWrapper<OaVenueActivitySeatEntity> ticWrapper = new LambdaQueryWrapper<>();
            ticWrapper.eq(OaVenueActivitySeatEntity::getIsUse, 0);
            ticWrapper.eq(OaVenueActivitySeatEntity::getActivityId, id);
            long ticketCount = oaVenueActivitySeatService.list(ticWrapper).size();
            if (now.compareTo(oaVenueActivity.getGetTicketSt()) < 0) {
                oaVenueActivity.setButtonStatus("0");//未开始
            } else {
                if (ticketCount > 0) {
                    oaVenueActivity.setButtonStatus("1");//开始且未抢完
                } else {
                    oaVenueActivity.setButtonStatus("2");//票已抢完
                }
                if (now.compareTo(oaVenueActivity.getGetTicketEt()) >= 0) {
                    oaVenueActivity.setButtonStatus("3");//活动购票结束
                }
            }
        }

        //拼接组织方名称
        //拼接组织方名字
        List<String> newList = new ArrayList<>();
        List<String> deptIdList = Arrays.stream(oaVenueActivity.getBusinessDeptId().split(","))
                .map(String::trim)
                .collect(toList());
        Collection<SysDepart> depts = new ArrayList<>();
        for (String depId : deptIdList) {
            SysDepart byId = sysDepartService.getById(depId);
            if (oConvertUtils.isNotEmpty(byId)) {
                depts.add(byId);
                newList.add(depId);
            }
        }
        String collect = newList.stream().collect(Collectors.joining(StringPool.COMMA));
        oaVenueActivity.setBusinessDeptId(collect);
        if (!CollectionUtils.isEmpty(depts)) {
            String businessDeptName = depts.stream().map(SysDepart::getDepartName).collect(Collectors.joining(StringPool.COMMA));
            oaVenueActivity.setBusinessDeptName(businessDeptName);
        }

        result.setResult(oaVenueActivity);
        result.setSuccess(true);
        return result;
    }

    @Override
    public int checkActTimeRepeat(String venueId, Date st, Date et,String actId) {
        return this.baseMapper.checkActTimeRepeat(venueId,st,et,actId);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean updateAct(OaVenueActivityEntity oaVenueActivity) {
        //判断权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Boolean twPersonInCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
        if (!twPersonInCharge) throw new ZsxcBootException("非团委负责人不可编辑");

        OaVenueActivityEntity oldAct = this.getById(oaVenueActivity.getId());
        if (oConvertUtils.isEmpty(oldAct)) throw new ZsxcBootException("未找到对应活动信息");

        //校验当前场馆当前时间是否闲置
        int count = this.checkActTimeRepeat(oaVenueActivity.getVenueId(),oaVenueActivity.getSt(),oaVenueActivity.getEt(),oaVenueActivity.getId());
        if (count!=0) throw new ZsxcBootException("当前时间该场馆已被预约");

        if (oaVenueActivity.getGetTicketEt().compareTo(oaVenueActivity.getSt()) > 0)
            throw new ZsxcBootException("购票时间段需在活动开始时间前");

        List<OaVenueActivitySeatEntity> seatList = new ArrayList<>();
        oaVenueActivity.getSeatActInfoList().forEach(seatList::addAll);

        Date now = new Date();
        if (oldAct.getGetTicketSt().compareTo(now) <= 0 ) { //抢票开始
            //不允许更改场馆
            if (!oaVenueActivity.getVenueId().equals(oldAct.getVenueId())) throw new ZsxcBootException("抢票已开始，不能编辑场馆/座位了呢亲:(");
            //不允许更改抢票开始时间/最多购票数
            if (!oaVenueActivity.getGetTicketSt().equals(oldAct.getGetTicketSt()) || !oaVenueActivity.getTicketLimit().equals(oldAct.getTicketLimit()))
                throw new ZsxcBootException("抢票已开始，不可更改抢票开始时间/最大购票数");
            //活动开始后不可更改活动时间
            if (oldAct.getSt().compareTo(now) <= 0 ) {
                if (!oaVenueActivity.getSt().equals(oldAct.getSt()) || !oaVenueActivity.getEt().equals(oldAct.getEt()))
                    throw new ZsxcBootException("活动开始后不可更改活动时间");
            }
        } else {
            //买票时间未开始，可编辑座位
            //先删除之前对应座位信息
            LambdaQueryWrapper<OaVenueActivitySeatEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(OaVenueActivitySeatEntity::getActivityId, oaVenueActivity.getId());
            wrapper.eq(OaVenueActivitySeatEntity::getVenueId, oldAct.getVenueId());
            oaVenueActivitySeatService.remove(wrapper);
            //赋值活动id
            List<OaVenueActivitySeatEntity> newSeat = new ArrayList<>();
            List<List<OaVenueActivitySeatEntity>> seatInfoList = oaVenueActivity.getSeatActInfoList();
            seatInfoList.forEach(newSeat::addAll);
            newSeat.forEach(t -> {
                t.setActivityId(oaVenueActivity.getId());
                t.setId(null);
            });
            oaVenueActivitySeatService.saveBatch(newSeat);
        }

        //0420 购票时间开始后，报名范围只增不减
        if (now.compareTo(oldAct.getGetTicketSt())>=0) {
            //0509 改为购票开始后不允许编辑
            oaVenueActivity.setDeptId(oldAct.getDeptId());
            oaVenueActivity.setNj(oldAct.getNj());
        }
        //更新落库
        return this.updateById(oaVenueActivity);
    }

    @Override
    public Result<List<Map<String, String>>> allActName() {
        Result<List<Map<String, String>>> result = new Result<>();
        LambdaQueryWrapper<OaVenueActivityEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.orderByDesc(OaVenueActivityEntity::getCreateTime);
        List<OaVenueActivityEntity> list = this.list(wrapper);
        List<Map<String, String>> mapList = new ArrayList<>();
        if ( !CollectionUtils.isEmpty(list)) {
            list.forEach(t->{
                Map<String,String> map = new HashMap<>();
                map.put("id",t.getId());
                map.put("name",t.getActivityName());
                mapList.add(map);
            });
        }
        result.setResult(mapList);
        return result;
    }

    @Override
    public IPage<OaVenueActivityEntity> mobilePageList(Page<OaVenueActivityEntity> page, OaVenueActivityEntity oaVenueActivity, Integer pageNo, Integer pageSize, Integer query) {
        IPage<OaVenueActivityEntity> actPage = new Page<>();

        //查询前先更新状态，只更新未开始和进行中的
        LambdaQueryWrapper<OaVenueActivityEntity> statusWrapper = new LambdaQueryWrapper<>();
        statusWrapper.in(OaVenueActivityEntity::getStatus,1,0);
        List<OaVenueActivityEntity> list = this.list(statusWrapper);
        Date now = new Date();
        list.forEach(t ->{
            if (now.compareTo(t.getGetTicketSt())> 0 && now.compareTo(t.getGetTicketEt()) <0) t.setStatus(0).setStart("1");
            if (now.compareTo(t.getGetTicketEt())> 0 ) t.setStatus(2).setStart("2");
            if (now.compareTo(t.getGetTicketSt())< 0 ) t.setStatus(1).setStart("0");
        });
        if (list.size()>0) {this.updateBatchById(list);}

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<OaVenueActivityEntity> pageList = this.baseMapper.queryPage(oaVenueActivity,query);
        pageList.forEach(t ->{
            if (now.compareTo(t.getGetTicketSt())> 0 && now.compareTo(t.getGetTicketEt()) <0) t.setStatus(0).setStart("1");
            if (now.compareTo(t.getGetTicketEt())> 0 ) t.setStatus(2).setStart("2");
            if (now.compareTo(t.getGetTicketSt())< 0 ) t.setStatus(1).setStart("0");
        });

        //根据活动选择的范围排除登录人不可看见的活动
        this.removeAct(pageList,sysUser.getUsername());

        //自分页
        List<OaVenueActivityEntity> activityEntityList = (List<OaVenueActivityEntity>)PseudoPagingUtil.pseudoPaging(actPage, pageList, pageNo, pageSize);
        if (activityEntityList.size()>0){
            activityEntityList.forEach(t ->{
                //23.04.11 只统计已确认的订单，如有未确认订单目前 让他直接跳转订单确认页面，有变动再改
                LambdaQueryWrapper<OaVenueActOrderEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(OaVenueActOrderEntity::getActivityId,t.getId());
                wrapper.eq(OaVenueActOrderEntity::getUserId,sysUser.getUsername());
                wrapper.eq(OaVenueActOrderEntity::getStatus,1);
                int count = oaVenueActOrderService.count(wrapper);
                t.setOwnTicket(count);

                //拼接组织方名字
                List<String> newList = new ArrayList<>();
                List<String> deptIdList = Arrays.stream(t.getBusinessDeptId().split(","))
                        .map(String::trim)
                        .collect(toList());
                Collection<SysDepart> depts = new ArrayList<>();
                for (String depId : deptIdList) {
                    SysDepart byId = sysDepartService.getById(depId);
                    if (oConvertUtils.isNotEmpty(byId)) {
                        depts.add(byId);
                        newList.add(depId);
                    }
                }
                String collect = newList.stream().collect(Collectors.joining(StringPool.COMMA));
                t.setBusinessDeptId(collect);
                if (!CollectionUtils.isEmpty(depts)) {
                    String businessDeptName = depts.stream().map(SysDepart::getDepartName).collect(Collectors.joining(StringPool.COMMA));
                    t.setBusinessDeptName(businessDeptName);
                }
            });

        }
        actPage.setRecords(activityEntityList);
        return actPage;
    }

    @Override
    public Result<buttonShowDto> getButton(String id) {
        Result<buttonShowDto> result = new Result<>();
        buttonShowDto button = new buttonShowDto();
        Date now = new Date();

        OaVenueActivityEntity oaVenueActivity = this.baseMapper.getActById(id);
        if (oConvertUtils.isEmpty(oaVenueActivity)) throw new ZsxcBootException("未找到对应活动");
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Boolean twCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
        if (now.compareTo(oaVenueActivity.getEt())>=0) {//活动结束不展示
            button.setCheckButton(false);
        }else {
            if (twCharge){//团委展示
                button.setCheckButton(true);
            }else {
                //查询托管列表
                LambdaQueryWrapper<OaVenueActivityDevolutionEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(OaVenueActivityDevolutionEntity::getActivityId,id);
                wrapper.eq(OaVenueActivityDevolutionEntity::getUserId,sysUser.getUsername());
                List<OaVenueActivityDevolutionEntity> list = oaVenueActivityDevolutionService.list(wrapper);
                if (list.size()>0){
                    button.setCheckButton(true);
                }else {
                    button.setCheckButton(false);
                }
            }
        }
        result.setResult(button);
        return result;
    }

    @Override
    public Result<mobileActDto> mobileGetAct(String id) {
        Result<mobileActDto> result = new Result<>();
        mobileActDto act = this.baseMapper.mobileAct(id);
        if (oConvertUtils.isEmpty(act)) throw new ZsxcBootException("未找到对应活动");
        Date now = new Date();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<OaVenueActivitySeatEntity> seatEntities = oaVenueActivitySeatService.mobileGetSeat(id);

        //根据订单数据，只把自己的预定订单isUse 设2 ，其他预订的2->显示3
        LambdaQueryWrapper<OaVenueActOrderEntity> orderWrapper = new LambdaQueryWrapper<>();
        orderWrapper.eq(OaVenueActOrderEntity::getStatus,0);
        orderWrapper.ne(OaVenueActOrderEntity::getUserId,sysUser.getUsername());
        List<String> otherReserveSeatList = oaVenueActOrderService.list(orderWrapper).stream().map(OaVenueActOrderEntity::getActSeatId).collect(Collectors.toList());

        seatEntities.forEach(t->{
                if (otherReserveSeatList.contains(t.getId())){
                    t.setIsUse(3);
                }
            });

        //转为二维数组
        List<List<OaVenueActivitySeatEntity>> actSeatInfo = oaVenueActivitySeatService.packSeat(seatEntities);
        act.setSeatActInfoList(actSeatInfo);
        LambdaQueryWrapper<OaVenueActOrderEntity> wrappers = new LambdaQueryWrapper<>();
        wrappers.eq(OaVenueActOrderEntity::getActivityId,id);
        wrappers.eq(OaVenueActOrderEntity::getUserId,sysUser.getUsername());
        wrappers.eq(OaVenueActOrderEntity::getStatus,1);
        int count = oaVenueActOrderService.count(wrappers);
        act.setOwnTicket(count);

        //0423 增加字段移动端判断按钮状态
        //1.统计是否有可购票
        LambdaQueryWrapper<OaVenueActivitySeatEntity> ticWrapper = new LambdaQueryWrapper<>();
        ticWrapper.eq(OaVenueActivitySeatEntity::getIsUse,0);
        ticWrapper.eq(OaVenueActivitySeatEntity::getActivityId,id);
        long ticketCount = oaVenueActivitySeatService.list(ticWrapper).size();
        if (now.compareTo(act.getGetTicketSt())<0){
            act.setButtonStatus("0");//未开始
        }else {
            if (ticketCount>0) {
                act.setButtonStatus("1");//开始且未抢完
            }else {
                act.setButtonStatus("2");//票已抢完
            }
            if (now.compareTo(act.getGetTicketEt()) >= 0) {
                act.setButtonStatus("3");//活动购票结束
            }
        }

        result.setResult(act);
        return result;
    }

}
