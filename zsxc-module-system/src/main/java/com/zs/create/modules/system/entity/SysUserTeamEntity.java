package com.zs.create.modules.system.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zs.create.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 我的社团
 * 
 * <AUTHOR>
 * @email null
 * @date 2020-09-18 15:17:17
 */
@Data
@TableName("sys_user_team")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sys_user_team对象", description="我的社团")
public class SysUserTeamEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	public static final String processDefinitionKey="user_team_audit";
	public static final Integer APPLY = 1;//申请中
	public static final Integer AUDIT_REJECT = -1;//驳回
	public static final Integer AUDIT_PASS = 10;//通过
	public static final Integer MAX_APPALY= 2;//加入社团达到最大值


	public static final Integer PROCESS_REJECT = 0;
	public static final Integer PROCESS_PASS = 1;

	//public static final Integer SIGN = 0;
	//public static final Integer OUT = 1;

	public static final String QUERY_TODO = "todo";
	public static final String QUERY_HISTORY = "history";

	@TableField(exist = false)
	@ApiModelProperty(value = "查询类型 ： todo ： 代办   history ：  已办")
	private String queryType;

	@TableField(exist = false)
	private String assignee;

	@TableField(exist = false)
	private String taskId;

	@TableField(exist = false)
	private String taskName;

	@TableField(exist = false)
	private String type;


	/**
	 * 
	 */
	@TableId
	private String id;
	/**
	 * 部门id
	 */
	@ApiModelProperty(value = "社团id")
	private String depId;
	/**
	 * 用户id
	 */
	@ApiModelProperty(value = "用户id")
	private String userId;
	/**
	 * 部门名称
	 */
	private String depName;
	/**
	 * 审核状态
	 */
	@Dict(dicCode = "stbm_status")
	private Integer status;
	/**
	 * 流程实例id
	 */
	private String processInstanceId;
	/**
	 * 入团理由
	 */
	@ApiModelProperty(value = "入团理由")
	private String remarks;
	/**
	 * 状态，0-入团申请 1-退团申请
	 */
	@ApiModelProperty(value = "状态，0-入团申请 1-退团申请")
	@Dict(dicCode = "stbm_type")
	private String auditType;
	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人
	 */
	private String updateBy;
	/**
	 * 更新时间
	 */
	private Date updateTime;

	@TableField(exist = false)
	private String statusName;
	@TableField(exist = false)
	private String auditTypeName;

}
