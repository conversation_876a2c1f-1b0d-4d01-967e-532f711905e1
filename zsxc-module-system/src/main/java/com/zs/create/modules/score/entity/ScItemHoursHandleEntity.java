package com.zs.create.modules.score.entity;

import java.io.Serializable;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;


/**
 * 项目学时处理
 *
 * <AUTHOR> @email 
 * @date 2022-04-11 09:23:06
 */
@Data
@TableName("sc_item_hours_handle")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_item_hours_handle对象", description="项目学时处理")
public class ScItemHoursHandleEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "主键")
	    private String id;
	/**
	 * 项目id
	 */
	    @ApiModelProperty(value = "项目id")
	    private String itemId;

}
