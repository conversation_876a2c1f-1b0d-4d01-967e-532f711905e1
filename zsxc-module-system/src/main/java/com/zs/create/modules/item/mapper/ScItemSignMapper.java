package com.zs.create.modules.item.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zs.create.modules.item.entity.ScItemSignEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 项目签到签退Mapper层
 * @email <EMAIL>
 * @date 2020-07-05 20:53:13
 * @Version: V1.0
 */
public interface ScItemSignMapper extends BaseMapper<ScItemSignEntity> {


    List<ScItemSignEntity> listUserItemSignHours(@Param("itemId") String itemId, @Param("userIds") List<String> userIds);

    List<String> partUsers(@Param("itemId")String itemId, @Param("ids")List<String> ids);
}
