package com.zs.create.modules.communication.callcenter.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

/**
 * @className: ChatUserDTO
 * @description: 聊天人员dto
 * @author: hy
 * @date: 2020-12-1
 **/
@Document(collection = "cp_chat_user")
@Data
@Accessors(chain = true)
@ApiModel(value="客服聊天用户对象", description="客服聊天用户对象")
public class CpChatUserEntity implements Serializable {
    /**
     * 发送者的userId
     */
    @Id
    protected String sendUserId;
    /**
     * 接收客服的id
     */
    protected String reciveUserId;

   /* *//**
     * 用户昵称
     *//*
    public String sendUserNickName;
    *//**
     * 用户头像
     *//*
    private String sendUserHeadImg;*/
    /**
     * 最新消息内容
     */
    protected String lastMessage;
    /**
     * 最新消息类型
     */
    protected String lastMessageType;


    protected Date updateTime;



}
