package com.zs.create.modules.oa.prize.service.impl;

import com.zs.create.modules.oa.prize.entity.OaPrizeJudgesEntity;
import com.zs.create.modules.oa.prize.mapper.OaPrizeJudgesMapper;
import com.zs.create.modules.oa.prize.service.OaPrizeJudgesService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description 奖项评委Service实现层
 *
 * <AUTHOR>
 * @email null
 * @date 2021-01-19 11:01:31
 * @Version: V1.0
 */
@Service
public class OaPrizeJudgesServiceImpl extends ServiceImpl<OaPrizeJudgesMapper, OaPrizeJudgesEntity> implements OaPrizeJudgesService {

}
