package com.zs.create.modules.item.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.item.entity.ScHoursDirectoryEntity;
import com.zs.create.modules.item.entity.ScHoursDirectoryVo;

/**
 * @Description 项目-学时目录Service层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-23 16:53:33
 * @Version: V1.0
 */
public interface ScHoursDirectoryService extends IService<ScHoursDirectoryEntity> {

    /**
     * 项目当前状态
     * @param itemId
     * @return
     */
    Integer getHoursDirStatus(String itemId);

    ScHoursDirectoryEntity createItemDirByItemId(String itemId);

    Page<ScHoursDirectoryVo> historyTaskPage(ScHoursDirectoryVo entity, Integer pageNo, Integer pageSize);

    Page<ScHoursDirectoryVo> todoTaskPage(ScHoursDirectoryVo entity, Integer pageNo, Integer pageSize);
}

