package com.zs.create.modules.communication.association.service.impl;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.modules.communication.association.entity.CpUserInAssociationEntity;
import com.zs.create.modules.communication.association.mapper.CpUserInAssociationMapper;
import com.zs.create.modules.communication.association.service.CpUserInAssociationService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * @Description 社团评审人员Service实现层
 *
 * <AUTHOR>
 * @email null
 * @date 2020-11-26 11:15:09
 * @Version: V1.0
 */
@Service
public class CpUserInAssociationServiceImpl extends ServiceImpl<CpUserInAssociationMapper, CpUserInAssociationEntity> implements CpUserInAssociationService {

    @Autowired
    CpUserInAssociationMapper cpUserInAssociationMapper;

    public Page<CpUserInAssociationEntity> pageList(CpUserInAssociationEntity entity, Integer pageNo, Integer pageSize) {
        Page<CpUserInAssociationEntity> page = new Page<>(pageNo, pageSize);

        page.setRecords(cpUserInAssociationMapper.pageList(page ,entity,new ArrayList<>()));

        return page;
    }

    @Override
    public List<String> queryByUser(String userId){
        return cpUserInAssociationMapper.queryByUser(userId);
    }
    @Override
    public List<CpUserInAssociationEntity> queryDeptByUser(Page<CpUserInAssociationEntity> page,String userId,String id){
        return cpUserInAssociationMapper.queryDeptByUser(page,userId,id);
    }
    @Override
    public CpUserInAssociationEntity getStatusById(String deptId,String associationId){
        return cpUserInAssociationMapper.getStatusById(deptId,associationId);
    }
}
