package com.zs.create.modules.communication.questionnaire.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.communication.questionnaire.entity.CpUserInQuestionnaireEntity;
import com.zs.create.modules.communication.questionnaire.service.CpUserInQuestionnaireService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * @Description 问卷调查人员Controller层
 *
 * <AUTHOR>
 * @email null
 * @date 2020-11-26 11:15:09
 * @Version: V1.0
 */
@Slf4j
@Api(tags="问卷调查人员")
@RestController
@RequestMapping("cpUserInQuestionnaire")
public class CpUserInQuestionnaireController {
    @Autowired
    private CpUserInQuestionnaireService cpUserInQuestionnaireService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "问卷调查人员-分页列表查询")
    @ApiOperation(value="问卷调查人员-分页列表查询", notes="问卷调查人员-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CpUserInQuestionnaireEntity>> queryPageList(CpUserInQuestionnaireEntity cpUserInQuestionnaire,
                                                                    @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                                    @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        Result<IPage<CpUserInQuestionnaireEntity>> result = new Result<>();
        IPage<CpUserInQuestionnaireEntity> pageList = cpUserInQuestionnaireService.pageList(cpUserInQuestionnaire,pageNo,pageSize);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "问卷调查人员-添加")
    @ApiOperation(value="问卷调查人员-添加", notes="问卷调查人员-添加")
    @PostMapping(value = "/add")
    public Result<CpUserInQuestionnaireEntity> add(@RequestBody CpUserInQuestionnaireEntity cpUserInQuestionnaire) {
        Result<CpUserInQuestionnaireEntity> result = new Result<>();
        try {
            cpUserInQuestionnaireService.save(cpUserInQuestionnaire);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "问卷调查人员-编辑")
    @ApiOperation(value="问卷调查人员-编辑", notes="问卷调查人员-编辑")
    @PutMapping(value = "/edit")
    public Result<CpUserInQuestionnaireEntity> edit(@RequestBody CpUserInQuestionnaireEntity cpUserInQuestionnaire) {
        Result<CpUserInQuestionnaireEntity> result = new Result<>();
        CpUserInQuestionnaireEntity cpUserInQuestionnaireEntity = cpUserInQuestionnaireService.getById(cpUserInQuestionnaire.getId());
        if(cpUserInQuestionnaireEntity==null) {
            result.error500("未找到对应实体");
        }else {
            boolean ok = cpUserInQuestionnaireService.updateById(cpUserInQuestionnaire);
            if(ok) {
                result.success("修改成功!");
            }
        }
        return result;
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "问卷调查人员-通过id删除")
    @ApiOperation(value="问卷调查人员-通过id删除", notes="问卷调查人员-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        try {
            cpUserInQuestionnaireService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败",e.getMessage());
            return Result.error("删除失败!");
        }
		return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "问卷调查人员-批量删除")
    @ApiOperation(value="问卷调查人员-批量删除", notes="问卷调查人员-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<CpUserInQuestionnaireEntity> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<CpUserInQuestionnaireEntity> result = new Result<>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.cpUserInQuestionnaireService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "问卷调查人员-通过id查询")
    @ApiOperation(value="问卷调查人员-通过id查询", notes="问卷调查人员-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<CpUserInQuestionnaireEntity> queryById(@RequestParam(name="id",required=true) String id) {
        Result<CpUserInQuestionnaireEntity> result = new Result<>();
        CpUserInQuestionnaireEntity cpUserInQuestionnaire = cpUserInQuestionnaireService.getById(id);
        if(cpUserInQuestionnaire==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(cpUserInQuestionnaire);
            result.setSuccess(true);
        }
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<CpUserInQuestionnaireEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                CpUserInQuestionnaireEntity cpUserInQuestionnaire = JSON.parseObject(deString, CpUserInQuestionnaireEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(cpUserInQuestionnaire, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<CpUserInQuestionnaireEntity> pageList = cpUserInQuestionnaireService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "问卷调查人员列表");
        mv.addObject(NormalExcelConstants.CLASS, CpUserInQuestionnaireEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("问卷调查人员列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<CpUserInQuestionnaireEntity> listCpUserInQuestionnaires = ExcelImportUtil.importExcel(file.getInputStream(), CpUserInQuestionnaireEntity.class, params);
                cpUserInQuestionnaireService.saveBatch(listCpUserInQuestionnaires);
                return Result.ok("文件导入成功！数据行数:" + listCpUserInQuestionnaires.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
