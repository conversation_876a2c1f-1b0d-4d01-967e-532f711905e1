<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <title>扫码结果</title>
    <style type="text/css">
        * {
            padding: 0;
            margin: 0;
        }

        .content {
            text-align: center;
            padding-top: 10%;
        }

        .icnPic {
            display: inline-block;
            width: 30%;
        }

        .erTxt {
            margin-top: 15px;
            font-size: 16px;
        }
    </style>
</head>
<body>
<div class="content">
    <div th:if="${result['success']}">
        <img th:src="@{${qrUrl} + '/images/icn2.png'}" class="icnPic" />
        <p class="erTxt" th:text = "${result['message']}"></p>
        <p class="erTxt" th:text = "${result['goodsName']}"></p>
        <p class="erTxt" th:text = "${result['applyNum']}"></p>
        <p class="erTxt" th:text = "${result['estimateLendTime']}"></p>
        <p class="erTxt" th:text = "${result['estimateReturnTime']}"></p>
    </div>

    <div th:if="${!result['success']}">
        <img th:src="@{${qrUrl} + '/images/icn1.png'}" class="icnPic" />
        <p class="erTxt" th:text = "${result['message']}"></p>
        <p class="erTxt" th:text = "${result['goodsName']}"></p>
        <p class="erTxt" th:text = "${result['applyNum']}"></p>
        <p class="erTxt" th:text = "${result['estimateLendTime']}"></p>
        <p class="erTxt" th:text = "${result['estimateReturnTime']}"></p>
    </div>

</div>
</body>
</html>