package com.zs.create.config;

import com.zs.create.modules.mq.constants.BrokerMessageLogConstants;
import com.zs.create.modules.mq.entity.BrokerMessageLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.connection.CorrelationData;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @createUser hy
 * @createTime 2020-4-2
 * @description rabbitmq的配置 ： 配置Exchange、Queue、及绑定交换机。
 * -----发送消息-----
 * 1、生产者和Broker[exchange + queue]建立TCP连接。
 * 2、生产者和Broker建立通道。
 * 3、生产者通过通道消息发送给Broker，由Exchange将消息进行转发。
 * 4、Exchange将消息转发到指定的Queue（队列）
 * <p>
 * ----rabbitMq的工作模式-------------
 * 1、Work queues  一个生产者，多个消费者，每个消费者获取到的消息唯一。
 * 2、Publish/Subscribe  一个生产者发送的消息会被多个消费者获取。
 * 到消息
 * 3、Routing  发送消息到交换机并且要指定路由key ，消费者将队列绑定到交换机时需要指定路由key
 * 4、Topics  将路由键和某模式进行匹配，此时队列需要绑定在一个模式上，“#”匹配一个词或多个词，“*”只匹配一个词
 * 5、Header  不使用
 * 6、RPC  不使用
 */
@Configuration
@Slf4j
public class RabbitmqConfig implements RabbitTemplate.ConfirmCallback, RabbitTemplate.ReturnCallback {

    @Autowired
    RabbitTemplate rabbitTemplate;
    @Autowired
    MongoTemplate mongoTemplate;

    /**
     * 全局前缀
     */
    public static final String  prefix= "ustcMQ_";

    /**
     * 交换机名称
     */
    public static final String EXCHANGE_ROUTING_INFORM = prefix+"exchange_routing_inform";
    public static final String EXCHANGE_FANOUT_ITEM_REGISTRATION = prefix+"exchange_fanout_item_registration";

    public static final String EXCHANGE_FANOUT_INFORM = prefix+"exchange_fanout_inform";

    public static final String EXCHANGE_FANOUT_WEBSOCKET_CLOSE = prefix+"exchange_fanout_websocket_close";

    public static final String EXCHANGE_FANOUT_WEBSOCKET_SENDMESSAGE = prefix+"exchange_fanout_websocket_sendmessage";

    public static final String EXCHANGE_ROURTING_QUESTIONAIRE = prefix+"exchange_rourting_questionaire";

    public static final String EXCHANGE_ROURTING_ASSOCIATION = prefix+"exchange_rourting_association";

    public static final String EXCHANGE_ROUTING_PUBLICITY =prefix+ "exchange_routing_publicity";

    public static final String EXCHANGE_FANOUT_WEBSOCKET_REMOVE_OLDSESSION = prefix+"exchange_fanout_websocket_remove_oldsession";

    public static final String EXCHANGE_ROUTING_PRIZE = prefix+"exchange_routing_prize";
    public static final String QUEUE_ITEM_REGISTRATION =prefix+ "queue_item_registration";

    /**
     * 队列名称 ： 精品项目库队列
     */
    public static final String QUEUE_INFORM_JP_LIBRARY = prefix+"queue_inform_jp_library";

    /**
     * 队列名称 ： 系统日志
     */
    public static final String QUEUE_INFORM_SYS_LOG = prefix+"queue_inform_sys_log";

    /**
     * 待办和消息推送
     */
    public static final String QUEUE_INFORM_TASK_MSG =prefix+ "queue_inform_task_msg";

    /**
     * 队列名称 : 项目流程队列
     */
    public static final String QUEUE_INFORM_ITEM_FLOW = prefix+"queue_inform_item_flow";

    /**
     * 队列名称 ： 成绩流程队列
     */
    public static final String QUEUE_INFORM_SCORE_FLOW =prefix+ "queue_inform_score_flow";

    /**
     * 队列名称 ： 星级计算队列
     */
    public static final String QUEUE_INFORM_STAR_CACULATE =prefix+ "queue_inform_star_caculate";

    /**
     * 队列名称 ：消息发送队列
     */
    public static final String QUEUE_INFORM_MASSAGE_SEND =prefix+ "queue_inform_message_send";

    /**
     * 队列名称：公示结束接收消息延时队列
     */
    public static final String QUEUE_INFORM_PUBLICITY_SEND =prefix+ "queue_inform_publicity_send";

    /**
     * 队列名称 ：微信消息消息发送队列
     */
    public static final String QUEUE_INFORM_WX_MASSAGE_SEND =prefix+ "queue_inform_wx_message_send";
    /**
     * 队列名称 ：通用消息发送队列
     */
    public static final String QUEUE_INFORM_COMMON_MASSAGE_SEND = prefix+"queue_inform_common_message_send";

    /**
     * 队列名称 ： kjar发布队列  发布订阅模式
     */
    public static final String QUEUE_INFORM_KJAR_PUBLISH =prefix+ "queue_inform_kjar_publish";


    /**
     * 队列名称 ： websocket close 队列  发布订阅模式
     */
    public static final String QUEUE_WEBSOCKET_CLOSE = prefix+"queue_websocket_close";


    /**
     * 队列名称 ：queue_questionaire 问卷调查消息发送
     */
    public static final String QUEUE_QUESTIONAIRE = prefix+"queue_questionaire";

    /**
     * 队列名称 ：queue_questionaire 社团评审消息发送
     */
    public static final String QUEUE_ASSOCIATION =prefix+ "queue_association";


    /**
     * 队列名称 ： websocket close 队列  发布订阅模式
     */
    public static final String QUEUE_WEBSOCKET_SENDMESSAGE =prefix+ "queue_websocket_sendmessage";

    /**
     * 队列名称 ：queue_prize 奖项审核通过数据处理
     */
    public static final String QUEUE_PRIZE = prefix+"queue_prize";
    /**
     * 路由键：公示结束延时接收路由键
     */
    public static final String PUBLICITY_SEND =prefix+ "publicity_send";

    /**
     * 路由键 ： 系统日志路由键
     */
    public static final String ROUTINGKEY_SYS_LOG =prefix+ "routingkey_sys_log";

    public static final String ROUTINGKEY_TASK_MSG = prefix+"routingkey_task_msg";

    public static final String ROUTINGKEY_COMMON_LOG =prefix+ "routingkey_common_log";


    /**
     * 路由键 ： 精品库流程路由键
     */
    public static final String ROUTINGKEY_JP_LIBRARY_FLOW = prefix+"routingkey_jp_library_flow";


    /**
     * 路由键 ： 项目流程路由键
     */
    public static final String ROUTINGKEY_ITEM_FLOW =prefix+ "routingkey_item_flow";

    /**
     * 路由键 ： 成绩流程路由键
     */
    public static final String ROUTINGKEY_SCORE_FLOW =prefix+ "routingkey_score_flow";

    /**
     * 路由键 ： 星级计算路由键
     */
    public static final String ROUTINGKEY_STAR_CACULATE =prefix+ "routingkey_star_caculate";

    /**
     * 路由键 : 所有消息发送路由键
     */
    public static final String MESSAGE_SEND = prefix+"message_send";
    /**
     * 路由键 ： 项目报名
     */
    public static final String ROUTINGKEY_ITEM_REGISTRATION = prefix+"routingkey_item_registration";
    /**
     * 路由键 : 微信消息发送路由键
     */
    public static final String WX_MESSAGE_SEND = prefix+"wx_message_send";
    /**
     * 路由键 : 通用消息发送路由键
     */
    public static final String COMMON_MESSAGE_SEND =prefix+ "common_message_send";

    /**
     * 路由键 ： 问卷调查消息路由键
     */
    public static final String ROUTINGKEY_QUESTIONAIRE =prefix+ "routingkey_questionaire";

    /**
     * 路由键 ： 社团评审消息路由键
     */
    public static final String ROUTINGKEY_ASSOCIATION =prefix+ "routingkey_association";

    /**
     * 路由键 ： 奖项审核消息路由键
     */
    public static final String ROUTINGKEY_PRIZE = prefix+"routingkey_prize";
    /**
     * 交换机：场地 活动订单交换机
     */
    public final static String ACTIVITY_ORDER_EXCHANGE =prefix+ "activity_order_exchange";
    /**
     * 死信交换机：场地 活动订单死信交换机
     */
    public final static String ACTIVITY_ORDER_DEAD_EXCHANGE =prefix+ "activity_order_dead_exchange";
    /**
     * 队列：场地 活动订单队列
     */
    public final static String ACTIVITY_ORDER_QUEUE =prefix+ "activity_order_queue";
    /**
     * 死信队列：场地 活动订单死信队列
     */
    public final static String ACTIVITY_ORDER_DEAD_QUEUE = prefix+"activity_order_dead_queue";
    /**
     * 路由键：场地 活动订单死信路由键
     */
    public final static String ACTIVITY_ORDER_KEY =prefix+ "activity_order_key";
    /**
     * 死信路由键：场地 活动订单死信路由键
     */
    public final static String ACTIVITY_ORDER_DEAD_KEY = prefix+"activity_order_dead_key";

    /**
     * 定制rabbitMQ模板
     * <p>
     * ConfirmCallback接口用于实现消息发送到RabbitMQ交换器后接收消息成功回调
     * ReturnCallback接口用于实现消息发送到RabbitMQ交换器，但无相应队列与交换器绑定时的回调  即消息发送不到任何一个队列中回调
     *
     * @return
     */
    @PostConstruct
    public void initRabbitTemplate() {
        rabbitTemplate.setConfirmCallback(this);
        rabbitTemplate.setReturnCallback(this);
    }

    /**
     * 声明订单交换机
     *
     * @return
     */
    @Bean(ACTIVITY_ORDER_EXCHANGE)
    public Exchange ACTIVITY_ORDER_EXCHANGE() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.directExchange(ACTIVITY_ORDER_EXCHANGE).durable(true).build();
    }

    /**
     * 声明订单死信交换机
     *
     * @return
     */
    @Bean(ACTIVITY_ORDER_DEAD_EXCHANGE)
    public Exchange ACTIVITY_ORDER_DEAD_EXCHANGE() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.directExchange(ACTIVITY_ORDER_DEAD_EXCHANGE).durable(true).build();
    }

    /**
     * 将订单交换机绑定死信交换机
     *
     * @return
     */
    @Bean(ACTIVITY_ORDER_QUEUE)
    public Queue ACTIVITY_ORDER_QUEUE() {
        Map arguments = new HashMap<>();
        // TTL(超时时间) 单位：s
        arguments.put("x-message-ttl", 1000 * 60 * 15);
        // 绑定死信队列和死信交换机
        arguments.put("x-dead-letter-exchange", ACTIVITY_ORDER_DEAD_EXCHANGE);
        arguments.put("x-dead-letter-routing-key", ACTIVITY_ORDER_DEAD_KEY);
        return new Queue(ACTIVITY_ORDER_QUEUE, true, false, false, arguments);
    }

    /**
     * 声明订单死信队列
     *
     * @return
     */
    @Bean(ACTIVITY_ORDER_DEAD_QUEUE)
    public Queue ACTIVITY_ORDER_DEAD_QUEUE() {
        return new Queue(ACTIVITY_ORDER_DEAD_QUEUE);
    }

    /**
     * 将订单交换机绑定订单队列
     *
     * @return
     */
    @Bean
    public Binding BINDING_ACTIVITY_ORDER(@Qualifier(ACTIVITY_ORDER_QUEUE) Queue queue,
                                          @Qualifier(ACTIVITY_ORDER_EXCHANGE) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ACTIVITY_ORDER_KEY).noargs();
    }

    /**
     * 将订单死信交换机绑定死信交队列
     *
     * @return
     */
    @Bean
    public Binding BINDING_ACTIVITY_ORDER_DEAD(@Qualifier(ACTIVITY_ORDER_DEAD_QUEUE) Queue queue,
                                               @Qualifier(ACTIVITY_ORDER_DEAD_EXCHANGE) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ACTIVITY_ORDER_DEAD_KEY).noargs();
    }

    /**
     * 交换机配置
     * ExchangeBuilder提供了fanout[订阅发布]、direct[路由key]、topic【通配符】、header交换机类型的配置
     *
     * @return the exchange
     */


    @Bean(EXCHANGE_ROUTING_INFORM)
    public Exchange EXCHANGE_ROUTING_INFORM() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.directExchange(EXCHANGE_ROUTING_INFORM).durable(true).build();
    }

    @Bean(EXCHANGE_FANOUT_INFORM)
    public Exchange EXCHANGE_FANOUT_INFORM() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.fanoutExchange(EXCHANGE_FANOUT_INFORM).durable(true).build();
    }


    @Bean(EXCHANGE_FANOUT_WEBSOCKET_CLOSE)
    public Exchange EXCHANGE_FANOUT_WEBSOCKET_CLOSE() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.fanoutExchange(EXCHANGE_FANOUT_WEBSOCKET_CLOSE).durable(true).build();
    }

    @Bean(EXCHANGE_FANOUT_WEBSOCKET_SENDMESSAGE)
    public Exchange EXCHANGE_FANOUT_WEBSOCKET_SENDMESSAGE() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.fanoutExchange(EXCHANGE_FANOUT_WEBSOCKET_SENDMESSAGE).durable(true).build();
    }

    @Bean(EXCHANGE_ROURTING_QUESTIONAIRE)
    public Exchange EXCHANGE_ROURTING_QUESTIONAIRE() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.directExchange(EXCHANGE_ROURTING_QUESTIONAIRE).durable(true).build();
    }

    @Bean(EXCHANGE_ROURTING_ASSOCIATION)
    public Exchange EXCHANGE_ROURTING_ASSOCIATION() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.directExchange(EXCHANGE_ROURTING_ASSOCIATION).durable(true).build();
    }

    @Bean(EXCHANGE_ROUTING_PUBLICITY)
    public Exchange EXCHANGE_ROUTING_PUBLICITY() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.directExchange(EXCHANGE_ROUTING_PUBLICITY).durable(true).build();
    }

    @Bean(EXCHANGE_ROUTING_PRIZE)
    public Exchange EXCHANGE_ROUTING_PRIZE() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.directExchange(EXCHANGE_ROUTING_PRIZE).durable(true).build();
    }

    @Bean(EXCHANGE_FANOUT_ITEM_REGISTRATION)
    public Exchange EXCHANGE_FANOUT_ITEM_REGISTATION() {
        // durable(true)持久化，消息队列重启后交换机仍然存在
        return ExchangeBuilder.fanoutExchange(EXCHANGE_FANOUT_ITEM_REGISTRATION).durable(true).build();
    }

    // 声明系统日志队列
    @Bean(QUEUE_INFORM_SYS_LOG)
    public Queue QUEUE_INFORM_SYS_LOG() {
        Queue queue = new Queue(QUEUE_INFORM_SYS_LOG);
        return queue;
    }

    // 声明待办和消息推送
    @Bean(QUEUE_INFORM_TASK_MSG)
    public Queue QUEUE_INFORM_TASK_MSG() {
        Queue queue = new Queue(QUEUE_INFORM_TASK_MSG);
        return queue;
    }

    // 声明精品库队列
    @Bean(QUEUE_INFORM_JP_LIBRARY)
    public Queue QUEUE_INFORM_JP_LIBRARY() {
        Queue queue = new Queue(QUEUE_INFORM_JP_LIBRARY);
        return queue;
    }

    // 声明项目流程队列
    @Bean(QUEUE_INFORM_ITEM_FLOW)
    public Queue QUEUE_INFORM_ITEM_FLOW() {
        Queue queue = new Queue(QUEUE_INFORM_ITEM_FLOW);
        return queue;
    }

    // 声明成绩流程队列
    @Bean(QUEUE_INFORM_SCORE_FLOW)
    public Queue QUEUE_INFORM_SCORE_FLOW() {
        Queue queue = new Queue(QUEUE_INFORM_SCORE_FLOW);
        return queue;
    }


    // 声明星级计算队列
    @Bean(QUEUE_INFORM_STAR_CACULATE)
    public Queue QUEUE_INFORM_STAR_CACULATE() {
        Queue queue = new Queue(QUEUE_INFORM_STAR_CACULATE);
        return queue;
    }

    @Bean(QUEUE_INFORM_MASSAGE_SEND)
    public Queue QUEUE_INFORM_MASSAGE_SEND() {
        Queue queue = new Queue(QUEUE_INFORM_MASSAGE_SEND);
        return queue;
    }

    @Bean(QUEUE_INFORM_PUBLICITY_SEND)
    public Queue QUEUE_INFORM_PUBLICITY_SEND() {
        Queue queue = new Queue(QUEUE_INFORM_PUBLICITY_SEND);
        return queue;
    }

    @Bean(QUEUE_INFORM_WX_MASSAGE_SEND)
    public Queue QUEUE_INFORM_WX_MASSAGE_SEND() {
        Queue queue = new Queue(QUEUE_INFORM_WX_MASSAGE_SEND);
        return queue;
    }

    @Bean(QUEUE_INFORM_COMMON_MASSAGE_SEND)
    public Queue QUEUE_INFORM_COMMON_MASSAGE_SEND() {
        Queue queue = new Queue(QUEUE_INFORM_COMMON_MASSAGE_SEND);
        return queue;
    }

    @Bean(QUEUE_INFORM_KJAR_PUBLISH)
    public Queue QUEUE_INFORM_KJAR_PUBLISH() {
        Queue queue = new Queue(QUEUE_INFORM_KJAR_PUBLISH);
        return queue;
    }

    @Bean(QUEUE_PRIZE)
    public Queue QUEUE_PRIZE() {
        Queue queue = new Queue(QUEUE_PRIZE);
        return queue;
    }


    /*@Bean(QUEUE_WEBSOCKET_CLOSE)
    public Queue QUEUE_WEBSOCKET_CLOSE() {
        Queue queue = new Queue(QUEUE_WEBSOCKET_CLOSE);
        return queue;
    }*/

    /*@Bean(QUEUE_WEBSOCKET_SENDMESSAGE)
    public Queue QUEUE_WEBSOCKET_SENDMESSAGE() {
        Queue queue = new Queue(QUEUE_WEBSOCKET_SENDMESSAGE);
        return queue;
    }*/

    @Bean(QUEUE_QUESTIONAIRE)
    public Queue QUEUE_QUESTIONAIRE() {
        Queue queue = new Queue(QUEUE_QUESTIONAIRE);
        return queue;
    }

    @Bean(QUEUE_ASSOCIATION)
    public Queue QUEUE_ASSOCIATION() {
        Queue queue = new Queue(QUEUE_ASSOCIATION);
        return queue;
    }

    @Bean(QUEUE_ITEM_REGISTRATION)
    public Queue QUEUE_ITEM_REGISTATION() {
        Queue queue = new Queue(QUEUE_ITEM_REGISTRATION);
        return queue;
    }

    /**
     * channel.queueBind(INFORM_QUEUE_SMS,"inform_exchange_topic","inform.#.sms.#");
     * 绑定队列到交换机 .
     *
     * @param queue    the queue
     * @param exchange the exchange
     * @return the binding
     */

    @Bean
    public Binding BINDING_QUEUE_INFORM_SYS_LOG(@Qualifier(QUEUE_INFORM_SYS_LOG) Queue queue,
                                                @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_SYS_LOG).noargs();
    }


    /**
     * 指定待办和推送的绑定队列和交换机
     *
     * @param queue
     * @param exchange
     * @return
     */

    @Bean
    public Binding BINDING_QUEUE_INFORM_TASK_MSG(@Qualifier(QUEUE_INFORM_TASK_MSG) Queue queue,
                                                 @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_TASK_MSG).noargs();
    }

    @Bean
    public Binding BINDING_QUEUE_INFORM_JP_LIBRARY(@Qualifier(QUEUE_INFORM_JP_LIBRARY) Queue queue,
                                                   @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_JP_LIBRARY_FLOW).noargs();
    }

    @Bean
    public Binding BINDING_QUEUE_INFORM_ITEM_FLOW(@Qualifier(QUEUE_INFORM_ITEM_FLOW) Queue queue,
                                                  @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_ITEM_FLOW).noargs();
    }


    @Bean
    public Binding BINDING_QUEUE_INFORM_SCORE_FLOW(@Qualifier(QUEUE_INFORM_SCORE_FLOW) Queue queue,
                                                   @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_SCORE_FLOW).noargs();
    }


    @Bean
    public Binding BINDING_QUEUE_INFORM_STAR_CACULATE(@Qualifier(QUEUE_INFORM_STAR_CACULATE) Queue queue,
                                                      @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_STAR_CACULATE).noargs();
    }

    @Bean
    public Binding BINDING_QUEUE_INFORM_MASSAGE_SEND(@Qualifier(QUEUE_INFORM_MASSAGE_SEND) Queue queue,
                                                     @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(MESSAGE_SEND).noargs();
    }

    @Bean
    public Binding BINDING_QUEUE_INFORM_PUBLICITY_SEND(@Qualifier(QUEUE_INFORM_PUBLICITY_SEND) Queue queue,
                                                       @Qualifier(EXCHANGE_ROUTING_PUBLICITY) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(PUBLICITY_SEND).noargs();
    }

    @Bean
    public Binding BINDING_QUEUE_INFORM_WX_MASSAGE_SEND(@Qualifier(QUEUE_INFORM_WX_MASSAGE_SEND) Queue queue,
                                                        @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(WX_MESSAGE_SEND).noargs();
    }


    @Bean
    public Binding BINDING_QUEUE_INFORM_COMMON_MASSAGE_SEND(@Qualifier(QUEUE_INFORM_COMMON_MASSAGE_SEND) Queue queue,
                                                            @Qualifier(EXCHANGE_ROUTING_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(COMMON_MESSAGE_SEND).noargs();
    }


    @Bean
    public Binding BINDING_QUEUE_INFORM_KJAR_PUBLISH(@Qualifier(QUEUE_INFORM_KJAR_PUBLISH) Queue queue,
                                                     @Qualifier(EXCHANGE_FANOUT_INFORM) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with("").noargs();
    }

    @Bean
    public Binding BINDING_QUEUE_INFORM_PRIZE(@Qualifier(QUEUE_PRIZE) Queue queue,
                                              @Qualifier(EXCHANGE_ROUTING_PRIZE) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_PRIZE).noargs();
    }

    /*@Bean
    public Binding BINDING_QUEUE_WEBSOCKET_CLOSE(@Qualifier(QUEUE_WEBSOCKET_CLOSE) Queue queue,
                                                     @Qualifier(EXCHANGE_FANOUT_WEBSOCKET_CLOSE) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with("").noargs();
    }


    @Bean
    public Binding BINDING_QUEUE_WEBSOCKET_SENDMESSAGE(@Qualifier(QUEUE_WEBSOCKET_SENDMESSAGE) Queue queue,
                                         @Qualifier(EXCHANGE_FANOUT_WEBSOCKET_SENDMESSAGE) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with("").noargs();
    }
*/

    @Bean
    public Binding BINDING_QUEUE_QUESTIONNAIRE(@Qualifier(QUEUE_QUESTIONAIRE) Queue queue,
                                               @Qualifier(EXCHANGE_ROURTING_QUESTIONAIRE) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_QUESTIONAIRE).noargs();
    }

    @Bean
    public Binding BINDING_QUEUE_ASSOCIATION(@Qualifier(QUEUE_ASSOCIATION) Queue queue,
                                             @Qualifier(EXCHANGE_ROURTING_ASSOCIATION) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_ASSOCIATION).noargs();
    }

    @Bean
    public Binding BINDING_ITEM_REGISTRATION(@Qualifier(QUEUE_ITEM_REGISTRATION) Queue queue,
                                             @Qualifier(EXCHANGE_FANOUT_ITEM_REGISTRATION) Exchange exchange) {
        return BindingBuilder.bind(queue).to(exchange).with(ROUTINGKEY_ITEM_REGISTRATION).noargs();
    }

    @Override
    public void confirm(CorrelationData correlationData, boolean ack, String cause) {
        if (correlationData == null || StringUtils.isEmpty(correlationData.getId())) {
            return;
        }
        String messageId = correlationData.getId();
        Query query = new Query(Criteria
                .where("messageId").is(messageId));
        BrokerMessageLog brokerMessageLog =
                mongoTemplate.findOne(query, BrokerMessageLog.class);
        // 如果confirm 返回成功 就跟新 消息状态
        if (ack) {
            brokerMessageLog.setStatus(BrokerMessageLogConstants.MESSAGE_SEND_SUCCESS)
                    .setUpdateTime(new Date());
            BrokerMessageLog save = mongoTemplate.save(brokerMessageLog);
            log.info("消息id ： {} ,ack确认成功", messageId);
        } else {
            if (brokerMessageLog.getCompensation() == BrokerMessageLogConstants.MESSAGE_COMPENSATION_CLOSE) {
                brokerMessageLog.setStatus(BrokerMessageLogConstants.MESSAGE_SEND_FAILURE)
                        .setUpdateTime(new Date());
                mongoTemplate.save(brokerMessageLog);
            } else { // 失败 可能是mq同步复制问题 ，消息队列满了 总之有问题 交给实施吧 ，我们解决的是网络闪断的问题
                log.error("消息id ： {} ,ack确认失败,定时任务做补偿", messageId);
            }
        }
    }

    @Override
    public void returnedMessage(Message message, int replyCode, String replyText, String exchange, String routingKey) {
        log.error("消息发送失败");
    }
}
