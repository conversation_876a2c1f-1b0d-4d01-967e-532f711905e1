package com.zs.create.modules.oa.aoumaAndBones.util;

import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.handler.impl.ExcelDataHandlerDefaultImpl;
import org.jeecgframework.poi.util.PoiPublicUtil;

import java.util.List;
import java.util.Map;

/**
 * @Author: lzd
 * 青马/大骨班学生成绩导入字段匹配handler
 * @Date: 2019/9/20 15:06
 */

public class scoreImportHandler extends ExcelDataHandlerDefaultImpl {
    private Map<String,Object> fieldMap;

    @Override
    public void setMapValue(Map<String, Object> map, String originKey, Object value) {
            if (value instanceof Double) {
                map.put(getRealKey(originKey), PoiPublicUtil.doubleToString((Double) value));
            } else {
                map.put(getRealKey(originKey), value != null ? value.toString() : null);
            }
    }

    private String getRealKey(String originKey){
        if(originKey.equals("姓名"))
            return "realname";
        if(originKey.equals("学号"))
            return "username";
        if(originKey.equals("分数"))
            return "score";
        if(originKey.equals("查重是否通过"))
            return "experience";
        String realKey = getValueByLabel(fieldMap, originKey);
        if(!StringUtils.isNotBlank(realKey)){
            throw new RuntimeException("表头不匹配导入失败...");
        }
        return realKey;
    }

    public scoreImportHandler(Map<String,Object> fieldMap) {
        this.fieldMap = fieldMap;
    }

    public String getValueByLabel(Map<String,Object> fieldMap, String label) {
        String value = null;
        List<String> fieldLogoList = (List<String>) fieldMap.get("fieldLogoList");
        List<String> fieldNameList = (List<String>) fieldMap.get("fieldNameList");
        Integer nameIndex = fieldNameList.indexOf(label);
        if (nameIndex != -1){
            value = fieldLogoList.get(nameIndex);
        }
        return value;
    }
}
