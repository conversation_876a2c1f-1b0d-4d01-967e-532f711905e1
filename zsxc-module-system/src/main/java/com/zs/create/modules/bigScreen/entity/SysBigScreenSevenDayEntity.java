package com.zs.create.modules.bigScreen.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 大屏
 *
 * <AUTHOR> @email 
 * @date 2022-10-10 14:27:36
 */
@Data
@TableName("sys_big_screen_seven_day")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sys_big_screen_seven_day对象", description="大屏")
public class SysBigScreenSevenDayEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "主键")
	    private String id;
	/**
	 * 待审核
	 */
	    @ApiModelProperty(value = "待审核")
	    private Integer examine;
	/**
	 * 进行中
	 */
	    @ApiModelProperty(value = "进行中")
	    private Integer afoot;
	/**
	 * 访问量
	 */
	    @ApiModelProperty(value = "访问量")
	    private Integer visits;
	/**
	 * 总访问量
	 */
	@ApiModelProperty(value = "访问量")
	private Integer visitsAll;
	/**
	 * 创建时间
	 */
	    @ApiModelProperty(value = "创建时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date createTime;
	/**
	 * 更新时间
	 */
	    @ApiModelProperty(value = "更新时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date updateTime;

}
