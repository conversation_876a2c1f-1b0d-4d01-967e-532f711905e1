package com.zs.create.config;

import com.zs.create.modules.shiro.authc.ShiroRealm;
import com.zs.create.modules.shiro.authc.aop.JwtFilter;
import org.apache.shiro.mgt.DefaultSessionStorageEvaluator;
import org.apache.shiro.mgt.DefaultSubjectDAO;
import org.apache.shiro.mgt.SecurityManager;
import org.apache.shiro.spring.LifecycleBeanPostProcessor;
import org.apache.shiro.spring.security.interceptor.AuthorizationAttributeSourceAdvisor;
import org.apache.shiro.spring.web.ShiroFilterFactoryBean;
import org.apache.shiro.web.mgt.DefaultWebSecurityManager;
import org.springframework.aop.framework.autoproxy.DefaultAdvisorAutoProxyCreator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import javax.servlet.Filter;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @author: lingrui
 * @date: 2018/2/7
 * @description: shiro 配置类
 */

@Configuration
public class ShiroConfig {

    /**
     * Filter Chain定义说明
     * <p>
     * 1、一个URL可以配置多个Filter，使用逗号分隔
     * 2、当设置多个过滤器时，全部验证通过，才视为通过
     * 3、部分过滤器可指定参数，如perms，roles
     */
    @Bean("shiroFilter")
    public ShiroFilterFactoryBean shiroFilter(SecurityManager securityManager) {
        ShiroFilterFactoryBean shiroFilterFactoryBean = new ShiroFilterFactoryBean();
        shiroFilterFactoryBean.setSecurityManager(securityManager);
        // 拦截器
        Map<String, String> filterChainDefinitionMap = new LinkedHashMap<String, String>();


        //单点登录验证登录
        filterChainDefinitionMap.put("/cas/client/**", "anon");
        filterChainDefinitionMap.put("/oauth2/client/**", "anon");

        // 配置不会被拦截的链接 顺序判断
        //数据大屏地址
        filterChainDefinitionMap.put("/screenPC", "anon");

        filterChainDefinitionMap.put("/bigScreen/**", "anon");
        filterChainDefinitionMap.put("/sysBigscreenCollegerate/**", "anon");


        //登录接口排除
        filterChainDefinitionMap.put("/sys/login", "anon");
        filterChainDefinitionMap.put("/api/wx/score/getCert", "anon");
        filterChainDefinitionMap.put("/gen_ts/genTest/list", "anon");
        filterChainDefinitionMap.put("/h5Cas/client/**", "anon");
        //获取加密串
        filterChainDefinitionMap.put("/sys/getEncryptedString", "anon");
        //短信验证码
        filterChainDefinitionMap.put("/sys/sms", "anon");
        //手机登录
        filterChainDefinitionMap.put("/sys/phoneLogin", "anon");
        //审批单扫描
        filterChainDefinitionMap.put("/item/scItem/getApprovalformById", "anon");

        filterChainDefinitionMap.put("/mobile/login/wxLogin/*", "anon");
        filterChainDefinitionMap.put("/mobile/login/checkingPasswordCode", "anon");
        filterChainDefinitionMap.put("/mobile/login/phone", "anon");

        //成绩单接口放行
        filterChainDefinitionMap.put("/score/scScoreReport/queryByUserId", "anon");
        filterChainDefinitionMap.put("/score/scScoreReport/getModuleScoreAndHours", "anon");
        filterChainDefinitionMap.put("/score/scScoreReport/userScoreHours", "anon");
        filterChainDefinitionMap.put("/score/scScoreReport/queryScoreExperience", "anon");
        //我的社团放行
        filterChainDefinitionMap.put("/mobile/team/wx/*", "anon");
        filterChainDefinitionMap.put("/mobile/team/signUpTeam", "anon");
        filterChainDefinitionMap.put("/mobile/team/queryById", "anon");

        //统计项目信息放行
        filterChainDefinitionMap.put("/item/scItem/pc/*", "anon");

        //切片上传
        filterChainDefinitionMap.put("/sys/file/checkFile", "anon");
        filterChainDefinitionMap.put("/sys/file/shardingUpload", "anon");
        filterChainDefinitionMap.put("/sys/file/merge", "anon");
        filterChainDefinitionMap.put("/sys/file/checkChunk", "anon");

        filterChainDefinitionMap.put("/MP_verify_yfEfk9hAG6JfzfXH.txt", "anon");

        filterChainDefinitionMap.put("/MP_verify_IEBKIkBZt5mIJ19M.txt", "anon");

        //青年大学习接口
        filterChainDefinitionMap.put("/leran/scYouthLearning/add", "anon");
        //学时预警提供学工系统数据
        filterChainDefinitionMap.put("/statistic/scItemHoursWarningDetail/dataList/**", "anon");

        filterChainDefinitionMap.put("/item/sign/wx/**", "anon");
        filterChainDefinitionMap.put("/goodsManage/oaGoodsBorrow/wx/**", "anon");

        filterChainDefinitionMap.put("/item/scItem/wx/**", "anon");

        //嘉宾点击邮箱
        filterChainDefinitionMap.put("/proposal/audit/guestAudit", "anon");
        filterChainDefinitionMap.put("/salon/cpSalonProposal/queryByEmailToken", "anon");

        //校验用户是否存在
        filterChainDefinitionMap.put("/sys/user/checkOnlyUser", "anon");
        //用户注册
//        filterChainDefinitionMap.put("/sys/user/register", "anon");
        //根据手机号获取用户信息
        filterChainDefinitionMap.put("/sys/user/querySysUser", "anon");
        //用户忘记密码验证手机号
        filterChainDefinitionMap.put("/sys/user/phoneVerification", "anon");
        //用户更改密码
//        filterChainDefinitionMap.put("/sys/user/passwordChange", "anon");
        //登录验证码
        filterChainDefinitionMap.put("/auth/2step-code", "anon");
        //图片预览不限制token
        filterChainDefinitionMap.put("/sys/common/view/**", "anon");
        //文件下载不限制token
        filterChainDefinitionMap.put("/sys/common/download/**", "anon");
        //文件下载不限制token
        filterChainDefinitionMap.put("/item/scItemWorks/downloadWorksZip/**", "anon");
        //文件下载不限制token
        filterChainDefinitionMap.put("/score/import/template/**", "anon");
        //移动端表单接口放开
        filterChainDefinitionMap.put("/mobile/form/**", "anon");
        //pdf预览
        filterChainDefinitionMap.put("/sys/common/pdf/**", "anon");
        //文件上传预览
//        filterChainDefinitionMap.put("/sys/common/upload", "anon");

        filterChainDefinitionMap.put("/sys/annountCement/sysAnnouncemenByMessageId", "anon");
        //志愿服务时长接口放行
        filterChainDefinitionMap.put("/scVolunteerService/scVolunteerService/add/**", "anon");
        //pdf预览需要文件
        filterChainDefinitionMap.put("/generic/**", "anon");
        filterChainDefinitionMap.put("/", "anon");
        filterChainDefinitionMap.put("/doc.html", "anon");
        filterChainDefinitionMap.put("/**/*.js", "anon");
        filterChainDefinitionMap.put("/**/*.css", "anon");
        filterChainDefinitionMap.put("/**/*.html", "anon");
        filterChainDefinitionMap.put("/**/*.svg", "anon");
        filterChainDefinitionMap.put("/**/*.jpg", "anon");
        filterChainDefinitionMap.put("/**/*.png", "anon");
        filterChainDefinitionMap.put("/**/*.ico", "anon");
        filterChainDefinitionMap.put("/druid/**", "anon");
        filterChainDefinitionMap.put("/swagger-ui.html", "anon");
        filterChainDefinitionMap.put("/csrf", "anon");
        filterChainDefinitionMap.put("/swagger**/**", "anon");
        filterChainDefinitionMap.put("/webjars/**", "anon");
        filterChainDefinitionMap.put("/v2/**", "anon");
        //流程设计器
        filterChainDefinitionMap.put("/modeler*/**", "anon");
        filterChainDefinitionMap.put("/diagram-viewer/**", "anon");
        filterChainDefinitionMap.put("/editor-app/**", "anon");

        //流程图片预览
        filterChainDefinitionMap.put("/viewProcess/**", "anon");

        //websocket
        filterChainDefinitionMap.put("/websocket/**", "anon");

        //邮件发送
        filterChainDefinitionMap.put("/score/scScoreCollect/senddata/**", "anon");
        filterChainDefinitionMap.put("/score/scScoreCollect/scoreQuerySendEmail/**", "anon");
        filterChainDefinitionMap.put("/score/scScoreCollect/createcode/**", "anon");

        //系统消息跳转
        filterChainDefinitionMap.put("/system/messages/**", "anon");

        // 微信测试
        filterChainDefinitionMap.put("/wx/reply.do", "anon");
        filterChainDefinitionMap.put("/weixin/yesandno", "anon");
        filterChainDefinitionMap.put("/handel/oldData", "anon");

        // 静态成绩单
        filterChainDefinitionMap.put("/score/scReportCard/queryWithoutVerify/**", "anon");
        filterChainDefinitionMap.put("/score/scReportCard/qrCode/**", "anon");

        //导出申报表单
        filterChainDefinitionMap.put("/prize/oaDeclareUser/exportBMToWord/**", "anon");

        // 添加自己的过滤器并且取名为jwt
        Map<String, Filter> filterMap = new HashMap<>(1);
        filterMap.put("jwt", new JwtFilter());
        shiroFilterFactoryBean.setFilters(filterMap);
        // <!-- 过滤链定义，从上向下顺序执行，一般将/**放在最为下边
        filterChainDefinitionMap.put("/**", "jwt");

        // 未授权界面返回JSON
        shiroFilterFactoryBean.setUnauthorizedUrl("/sys/common/403");
        shiroFilterFactoryBean.setLoginUrl("/sys/common/403");
        shiroFilterFactoryBean.setFilterChainDefinitionMap(filterChainDefinitionMap);
        return shiroFilterFactoryBean;
    }

    @Bean("securityManager")
    public DefaultWebSecurityManager securityManager(ShiroRealm myRealm) {
        DefaultWebSecurityManager securityManager = new DefaultWebSecurityManager();
        securityManager.setRealm(myRealm);

        /*
         * 关闭shiro自带的session，详情见文档
         * http://shiro.apache.org/session-management.html#SessionManagement-
         * StatelessApplications%28Sessionless%29
         */
        DefaultSubjectDAO subjectDAO = new DefaultSubjectDAO();
        DefaultSessionStorageEvaluator defaultSessionStorageEvaluator = new DefaultSessionStorageEvaluator();
        defaultSessionStorageEvaluator.setSessionStorageEnabled(false);
        subjectDAO.setSessionStorageEvaluator(defaultSessionStorageEvaluator);
        securityManager.setSubjectDAO(subjectDAO);

        return securityManager;
    }

    /**
     * 下面的代码是添加注解支持
     *
     * @return
     */
    @Bean
    @DependsOn("lifecycleBeanPostProcessor")
    public DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator() {
        DefaultAdvisorAutoProxyCreator defaultAdvisorAutoProxyCreator = new DefaultAdvisorAutoProxyCreator();
        defaultAdvisorAutoProxyCreator.setProxyTargetClass(true);
        return defaultAdvisorAutoProxyCreator;
    }

    @Bean
    public LifecycleBeanPostProcessor lifecycleBeanPostProcessor() {
        return new LifecycleBeanPostProcessor();
    }

    @Bean
    public AuthorizationAttributeSourceAdvisor authorizationAttributeSourceAdvisor(DefaultWebSecurityManager securityManager) {
        AuthorizationAttributeSourceAdvisor advisor = new AuthorizationAttributeSourceAdvisor();
        advisor.setSecurityManager(securityManager);
        return advisor;
    }

}
