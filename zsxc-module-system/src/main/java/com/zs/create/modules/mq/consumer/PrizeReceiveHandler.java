package com.zs.create.modules.mq.consumer;

import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.common.util.FastJsonConvert;
import com.zs.create.config.RabbitmqConfig;

import com.zs.create.modules.item.entity.ScItemHoursEntity;
import com.zs.create.modules.item.enums.HoursAuditStatusEnum;
import com.zs.create.modules.item.mapper.ScItemHoursMapper;
import com.zs.create.modules.score.service.ScScoreReportService;
import lombok.extern.slf4j.Slf4j;

import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: yc
 * @Date: 2022/02/28/14:02
 * @Description:
 */
@Component
@Slf4j
public class PrizeReceiveHandler {

    @Autowired
    private ScItemHoursMapper scItemHoursMapper;
    @Autowired
    private ScScoreReportService scScoreReportService;

    @RabbitListener(queues = {RabbitmqConfig.QUEUE_PRIZE})
    public void receiveScoreCaculate(String msg) {
        Map<String , Object> map = FastJsonConvert.convertJSONToObject(msg, Map.class);
        List<ScItemHoursEntity> list = scItemHoursMapper.getItemAndModuleList(map.get("prizeId").toString(), ScItemHoursEntity.GIVEN_YES, DelFlagEnum.NO_DEL.getCode(), HoursAuditStatusEnum.AUDIT_PASS.getCode());
        for(ScItemHoursEntity scItemHoursEntity : list){
            scScoreReportService.saveUserModuleScore(scItemHoursEntity.getUserId() , map.get("module").toString(),scItemHoursEntity.getGrade());
        }
    }

}
