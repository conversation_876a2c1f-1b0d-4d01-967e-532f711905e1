package com.zs.create.modules.score.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;


import java.io.Serializable;
import java.math.BigDecimal;


/**
 * 项目模块
 *
 * <AUTHOR> @email 
 * @date 2022-04-11 10:20:15
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ScItemModuleDto implements Serializable {

	@ApiModelProperty(value = "主键")
	private Long id;

	@Excel(name = "项目名称", width = 50)
	@ApiModelProperty(value = "项目名称")
	private String itemName;

	@Excel(name = "模块", width = 5)
	@ApiModelProperty(value = "模块")
	private String module;

	@Excel(name = "项目形式", width = 9)
	@ApiModelProperty(value = "项目形式")
	private String form;

	@Excel(name = "项目级别", width = 9)
	@ApiModelProperty(value = "项目级别")
	private String activityLevel;

	@Excel(name = "参与人数", width = 9,type = 4)
	@ApiModelProperty(value = "参与人数")
	private Integer partakeNum;

	@Excel(name = "组织方", width = 20)
	@ApiModelProperty(value = "组织方")
	private String businessDeptName;

	@Excel(name = "联系人", width = 7)
	@ApiModelProperty(value = "联系人")
	private String linkMan;

	@Excel(name = "所获学时", width = 7,type = 4)
	@ApiModelProperty(value = "总学时")
	private BigDecimal sumHours;

	@Excel(name = "状态", width = 7)
	@ApiModelProperty(value = "状态")
	private String status;

	@ApiModelProperty(value = "总学时")
	private String sumHour;
}
