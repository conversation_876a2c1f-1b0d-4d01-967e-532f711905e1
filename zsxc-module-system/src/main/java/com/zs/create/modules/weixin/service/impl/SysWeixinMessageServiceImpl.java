package com.zs.create.modules.weixin.service.impl;

import com.zs.create.modules.weixin.entity.SysWeixinMessageEntity;
import com.zs.create.modules.weixin.mapper.SysWeixinMessageMapper;
import com.zs.create.modules.weixin.service.SysWeixinMessageService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description 微信管理Service实现层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-04-22 11:45:26
 * @Version: V1.0
 */
@Service
public class SysWeixinMessageServiceImpl extends ServiceImpl<SysWeixinMessageMapper, SysWeixinMessageEntity> implements SysWeixinMessageService {

}
