package com.zs.create.modules.mq.constants;

/**
 * @createUser hy
 * @createTime 2020-4-4
 * @description
 */
public final class BrokerMessageLogConstants {

        public static final Integer MESSAGE_SENDING = 0; //发送中

        public static final Integer MESSAGE_SEND_SUCCESS = 1; //成功

        public static final Integer MESSAGE_SEND_FAILURE = 2; //失败

        public static final Integer MESSAGE_COMPENSATION_CLOSE = 0; //消息关闭补偿机制

        public static final Integer MESSAGE_COMPENSATION_OPEN = 1; //消息开启补偿机制

        public static final Integer MESSAGE_TIMEOUT = 5; /*分钟超时单位：min*/
}
