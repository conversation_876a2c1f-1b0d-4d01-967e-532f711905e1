package com.zs.create.modules.workflow.common.engine;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngineConfiguration;
import org.activiti.engine.impl.cfg.StandaloneProcessEngineConfiguration;

/**
 * <AUTHOR> guodl
 * @Date: 2019/8/7 17:26
 * @Description:流程引擎，用于测试创建流程表使用
 */
@Data
@NoArgsConstructor
public class WorkFlowEngine {
    /**
     * 流程引擎
     */
    public ProcessEngine processEngine;
    /**
     * 数据库连接url
     */
    public String jdbcURL;
    /**
     * 数据库用户名
     */
    private String username;
    /**
     * 数据库密码
     */
    private String password;
    /**
     * 驱动程序
     */
    private String jdbcDriver;

    /**
     * 是否创建数据库
     * DB_SCHEMA_UPDATE_FALSE:不重新创建表
     * DB_SCHEMA_UPDATE_CREATE_DROP:先删除表 后创建
     * DB_SCHEMA_UPDATE_TRUE:存在表则忽略 表不存在则创建
     */
    private String dbSchemaUpdate;

    //DB_SCHEMA_UPDATE

    /**
     * 流程引擎实例化
     * 主要用于单元测试或者通过jdbc去实例化建表
     * 如果引入spring，则交由spring去管理
     */
    public void init() {
        ProcessEngineConfiguration cfg = new StandaloneProcessEngineConfiguration().setJdbcUrl(this.jdbcURL)
                .setJdbcUsername(this.username).setJdbcPassword(this.password).setJdbcDriver(this.jdbcDriver)
                .setDatabaseSchemaUpdate(dbSchemaUpdate);
        processEngine = cfg.buildProcessEngine();
    }



    public WorkFlowEngine(ProcessEngine processEngine) {
        super();
        this.processEngine = processEngine;
    }


    public WorkFlowEngine(String jdbcURL, String username, String password,
                          String jdbcDriver) {
        super();
        /**
         * 默认自动创建
         */
        this.dbSchemaUpdate = ProcessEngineConfiguration.DB_SCHEMA_UPDATE_TRUE;
        this.jdbcURL = jdbcURL;
        this.username = username;
        this.password = password;
        this.jdbcDriver = jdbcDriver;
        init();
    }

    public WorkFlowEngine(String jdbcURL, String username, String password,
                          String jdbcDriver, String dbSchemaUpdate) {
        super();
        this.jdbcURL = jdbcURL;
        this.username = username;
        this.password = password;
        this.jdbcDriver = jdbcDriver;
        this.dbSchemaUpdate = dbSchemaUpdate;
        init();
    }
}

