package com.zs.create.modules.communication.callcenter.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;

/**
 * @className: CpMessageEntity
 * @description: 客服消息
 * @author: hy
 * @date: 2020-11-28
 **/
@Document(collection = "cp_chat_message")
@Data
@Accessors(chain = true)
@ApiModel(value="客服消息", description="客服消息")
public class CpChatMessageEntity implements Serializable {

    @Id
    public String id;
    /**
     * 发送者的userId
     */
    @NotBlank(message = "发送人id不能为空")
    @ApiModelProperty(value = "消息发送者userId")
    public String sendUserId;

    /**
     * 接受人的userId 当direction== 0 可为空
     */
    @ApiModelProperty(value = "消息接收人id")
    public String reciveUserId;

    /**
     * 发送内容
     */
    @ApiModelProperty(value = "消息发送内容")
    public String content;
    /**
     * 消息时间
     */
    @ApiModelProperty(value = "消息发送时间")
    public Date sendTime;

    /**
     * 消息传输类型  0 客服给用户发送的消息  1 用户给客服发送的消息
     */
    @ApiModelProperty(value = "消息传输类型：0 客服给用户发送的消息  1 用户给客服发送的消息")
    public String  msgDirectionType;

    /**
     * 消息类型 0 在线消息 1 离线消息
     */
    @ApiModelProperty(value = "消息类型 0 在线消息 1 离线消息")
    public String messageType;

    /**
     * 消息 ：0未读 1 已读
     */
    @ApiModelProperty(value = "消息已读/未读 ：0未读 1 已读")
    public String read;

    public static final String MESSAGE_UNREAD = "0";
    public static final String MESSAGE_READ = "1";

    /**
     *  消息传递方向  :客服给用户发送的消息
     */
    public static final String DIRECTION_TO_USER = "0";

    /**
     * 消息传递方向  : 用户给客服发送的消息
     */
    public static final String DIRECTION_TO_KF = "1";

    /**
     * 在线消息
     */

    public static final String MESSAGE_TYPE_ONLINE = "0";

    /**
     * 离线消息
     */
    public static final String MESSAGE_TYPE_OFFLINE = "1";


}
