package com.zs.create.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory;
import org.springframework.data.redis.listener.PatternTopic;
import org.springframework.data.redis.listener.RedisMessageListenerContainer;
import org.springframework.data.redis.listener.adapter.MessageListenerAdapter;

/**
 * @className: RedisPublushConfig
 * @description: redis 发布订阅配置
 * @author: hy
 * @date: 2020-11-27
 **/
@Configuration
public class RedisPublushConfig {

    public static final String CHANNEL_PATTERN = "kf_channel";

    public static final String CHANNEL_PATTERN_CLOSE_WEBSOCKET = "kf_websocket_close";

    @Autowired
    LettuceConnectionFactory lettuceConnectionFactory;

    @Bean
    RedisMessageListenerContainer container(MessageListenerAdapter listenerAdapter) {
        RedisMessageListenerContainer container = new RedisMessageListenerContainer();
        container.setConnectionFactory(lettuceConnectionFactory);
        //此处那个通道  是监听者配置的通道，要与发送者相同
        //可以添加多个 messageListener，配置不同的通道
        container.addMessageListener(listenerAdapter, new PatternTopic(CHANNEL_PATTERN_CLOSE_WEBSOCKET));
        container.addMessageListener(listenerAdapter, new PatternTopic(CHANNEL_PATTERN));
        return container;
    }
    @Bean
    MessageListenerAdapter listenerAdapter(RedisReceiver redisReceiver) {
        return new MessageListenerAdapter(redisReceiver, "receiveMessage");
    }


    @Bean
    MessageListenerAdapter webSocketCloseListenerAdapter(RedisReceiver redisReceiver) {
        return new MessageListenerAdapter(redisReceiver, "receiveMessage");
    }
}
