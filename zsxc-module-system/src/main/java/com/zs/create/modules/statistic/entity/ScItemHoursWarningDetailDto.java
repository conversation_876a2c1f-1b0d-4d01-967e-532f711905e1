package com.zs.create.modules.statistic.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.annotation.JsonFormat;

import java.io.Serializable;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="学时提醒详情", description="学时提醒详情")
public class ScItemHoursWarningDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    @Excel(name = "姓名", width = 15)
    private String userName;

    /**
     * 学号
     */
    @ApiModelProperty(value = "学号")
    @Excel(name = "学号", width = 15)
    private String userId;

    /**
     * 班级
     */
    @ApiModelProperty(value = "班级")
    @Excel(name = "班级", width = 15)
    private String classes;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date start;

    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date end;

    /**
     * 角色
     */
    @ApiModelProperty(value = "角色")
    private String role;

    /**
     * 年级
     */
    @ApiModelProperty(value = "年级")
    private String grade;

    /**
     * 消息id
     */
    @ApiModelProperty(value = "消息id")
    private String msgId;
}

