package com.zs.create.modules.oa.credit.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
@ApiModel(value="学分申请返回可选择项目", description="学分申请返回可选择项目")
public class OaCreditApplyItemVo implements Serializable {
    private static final long serialVersionUID = 2350446356146457724L;

    /**
     * 用户id
     */
    private String userId;
    /**
     * 用户姓名
     */
    private String userName;
    /**
     * 学院
     */
    private String college;
    /**
     * 班级
     */
    private String classes;
    /**
     * 德模块学分
     */
    private BigDecimal dModuleCredit;
    /**
     * 德模块申请时间
     */
    private Date dModuleTime;
    /**
     * 智模块学分
     */
    private BigDecimal zModuleCredit;
    /**
     * 智模块申请时间
     */
    private Date zModuleTime;
    /**
     * 体模块学分
     */
    private BigDecimal tModuleCredit;
    /**
     * 体模块申请时间
     */
    private Date tModuleTime;
    /**
     * 美模块学分
     */
    private BigDecimal mModuleCredit;
    /**
     * 美模块申请时间
     */
    private Date mModuleTime;
    /**
     * 劳模块学分
     */
    private BigDecimal lModuleCredit;
    /**
     * 劳模块申请时间
     */
    private Date lModuleTime;
    /**
     * 总学分
     */
    private BigDecimal sumCredit;
}
