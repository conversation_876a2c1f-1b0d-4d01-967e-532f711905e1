package com.zs.create.modules.bigScreen.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zs.create.config.SerializerBigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 基础
 *
 * <AUTHOR> @email
 * @date 2022-10-10 14:20:32
 */
@Data
@TableName("sys_big_screen_basics")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sys_big_screen_basics对象", description="基础")
public class SysBigScreenBasicsEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.ID_WORKER_STR)
	@ApiModelProperty(value = "主键")
	private String id;
	/**
	 * 学生总人数
	 */
	@ApiModelProperty(value = "学生总人数")
	private Integer studentTotal;
	/**
	 * 总参与人次
	 */
	@ApiModelProperty(value = "总参与人次")
	private Integer participateNumber;
	/**
	 * 总赋予学时
	 */
	@ApiModelProperty(value = "总赋予学时")
	@JsonFormat(pattern = "0.0",shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	private BigDecimal hoursTotal;
	/**
	 * 学生学时平均值
	 */
	@ApiModelProperty(value = "学生学时平均值")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.00",shape = JsonFormat.Shape.STRING)
	private BigDecimal avgHours;

	@ApiModelProperty(value = "学生科气值平均值")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.00",shape = JsonFormat.Shape.STRING)
	private BigDecimal avgScientificqiValue;

	/**
	 * 活动总参与率
	 */
	@ApiModelProperty(value = "活动总参与率")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.00",shape = JsonFormat.Shape.STRING)
	private BigDecimal participationRate;
	/**
	 * 项目活动学时数
	 */
	@ApiModelProperty(value = "项目活动学时数")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.0",shape = JsonFormat.Shape.STRING)
	private BigDecimal itemHours;
	/**
	 * 荣誉申报学时数
	 */
	@ApiModelProperty(value = "荣誉申报学时数")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.0",shape = JsonFormat.Shape.STRING)
	private BigDecimal honorHours;
	/**
	 * 任职履职学时数
	 */
	@ApiModelProperty(value = "任职履职学时数")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.0",shape = JsonFormat.Shape.STRING)
	private BigDecimal jobHours;

	/**
	 * 任职履职学时数
	 */
	@ApiModelProperty(value = "青年大学习学时数")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.0",shape = JsonFormat.Shape.STRING)
	private BigDecimal qxtHours;

	/**
	 * 任职履职学时数
	 */
	@ApiModelProperty(value = "志愿汇学时数")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.0",shape = JsonFormat.Shape.STRING)
	private BigDecimal zyhHours;

	/**
	 * 任职履职学时数
	 */
	@ApiModelProperty(value = "社会实践学时数")
	@JsonSerialize(using = SerializerBigDecimal.class)
	@JsonFormat(pattern = "0.0",shape = JsonFormat.Shape.STRING)
	private BigDecimal practiceHours;

	/**
	 * 总项目数
	 */
	@ApiModelProperty(value = "总项目数")
	private Integer itemCount;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

}
