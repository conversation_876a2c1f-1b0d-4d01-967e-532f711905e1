package com.zs.create.score;

import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.ZsxcSystemApplication;
import com.zs.create.common.util.PasswordUtil;
import com.zs.create.common.util.StringKit;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.score.entity.ModuleHoursDTO;
import com.zs.create.modules.score.service.ScoreCaculateService;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.service.ISysUserService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * @createUser hy
 * @createTime 2020-7-11
 * @description
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = ZsxcSystemApplication.class, webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
public class ScoreCaculateServiceTest {

    @Autowired
    ScoreCaculateService scoreCaculateService;

    @Autowired
    ISysUserService sysUserService;

    @Test
    public void cacaculateModuleStarByUserIdTest() {
        String module = "z112222";
        String userId = "e9ca23d68d884d4ebb19d07889727dae";
        scoreCaculateService.queryUserModuleHours("", userId);
    }

    @Test
    public void cacaculateModuleStarTest() {
        String module = "z";
        String userId = "e9ca23d68d884d4ebb19d07889727dae";
        ModuleHoursDTO moduleHoursDTO = scoreCaculateService.cacaculateModuleStar(module, userId);
        System.out.println(JSONUtil.toJsonPrettyStr(moduleHoursDTO));
    }


    /**
     * 批量修改密码
     */
    @Test
    public void changePassword() {
        QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag","0");
        List<SysUser> list = sysUserService.list(queryWrapper);
        List<SysUser> sysUserList = new ArrayList<>();
        for (SysUser sysUser : list) {
            String salt = oConvertUtils.randomGen(8);
            sysUser.setSalt(salt);
            String passwordEncode = PasswordUtil.encrypt(sysUser.getUsername(), "Zs#%011040201#PQ", salt);
            sysUser.setPassword(passwordEncode);
            sysUserList.add(sysUser);
        }
        sysUserService.updateBatchById(sysUserList);
    }

    /**
     * 修改个人密码
     */
    @Test
    public void changePasswordOne() {
       SysUser sysUser = new SysUser();
       String salt = oConvertUtils.randomGen(8);
        sysUser.setSalt(salt);
        String passwordEncode = PasswordUtil.encrypt("9202013997", "9202013997", salt);
        sysUser.setPassword(passwordEncode);
        System.out.println("个人盐："+salt);
        System.out.println("个人加密码："+passwordEncode);
        // sysUserService.updateById(sysUser);
    }


    /**
     * 修改个人密码2
     */
    @Test
    public void changePasswordOn2e() {
        SysUser sysUser = new SysUser();
        String salt = oConvertUtils.randomGen(8);
        sysUser.setSalt(salt);
        String passwordEncode = PasswordUtil.encrypt("9202013997", "9202013997", salt);
        sysUser.setPassword(passwordEncode);
        System.out.println("个人盐："+salt);
        System.out.println("个人加密码："+passwordEncode);
        // sysUserService.updateById(sysUser);
    }




    public static void main(String[] args) {
        SysUser sysUser = new SysUser();
        String salt = oConvertUtils.randomGen(8);
        sysUser.setSalt(salt);
        String passwordEncode = PasswordUtil.encrypt("PB23010315", "PB23010315", salt);
        sysUser.setPassword(passwordEncode);
        System.out.println("个人盐："+salt);
        System.out.println("个人加密码："+passwordEncode);

    }
}
