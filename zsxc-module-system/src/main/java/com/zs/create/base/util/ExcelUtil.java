package com.zs.create.base.util;


import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/7/28 20:08
 */
@Slf4j
public class ExcelUtil {
    /**
     * 导出Excel
     * @param sheetName sheet名称
     * @param title 标题
     * @param values 内容
     * @param wb HSSFWorkbook对象
     * @return
     */
    public static XSSFWorkbook getHSSFWorkbook(String sheetName, String []title, String [][]values, XSSFWorkbook wb){

        // 第一步，创建一个HSSFWorkbook，对应一个Excel文件
        if(wb == null){
            wb = new XSSFWorkbook();
        }

        // 第二步，在workbook中添加一个sheet,对应Excel文件中的sheet
        XSSFSheet sheet = wb.createSheet(sheetName);

        // 第三步，在sheet中添加表头第0行,注意老版本poi对Excel的行数列数有限制
        XSSFRow row = sheet.createRow(0);

        // 第四步，创建单元格，并设置值表头 设置表头居中
        XSSFCellStyle style = wb.createCellStyle();
        //style.setAlignment(HSSFCellStyle.ALIGN_CENTER); // 创建一个居中格式

        //声明列对象
        XSSFCell cell = null;

        //创建标题
        for(int i=0;i<title.length;i++){
            cell = row.createCell(i);
            cell.setCellValue(title[i]);
            cell.setCellStyle(style);
        }

        //创建内容
        for(int i=0;i<values.length;i++){
            row = sheet.createRow(i + 1);
            for(int j=0;j<values[i].length;j++){
                //将内容按顺序赋给对应的列对象
                row.createCell(j).setCellValue(values[i][j]);
            }
        }
        return wb;
    }

    //发送响应流方法
    public static void setResponseHeader(HttpServletResponse response, String fileName) {
        try {

            response.setContentType("application/octet-stream;charset=UTF-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8"));
            response.addHeader("Pargam", "no-cache");
            response.addHeader("Cache-Control", "no-cache");
        } catch (Exception ex) {
            ex.printStackTrace();
        }
    }

    /**
     * 解析excel
     *
     * @param xlsFile
     * @return
     */
    public static Map<Integer, List<List<String>>> analysisExcel(MultipartFile xlsFile) {
        Map<Integer, List<List<String>>> res = new HashMap<>();
        try {
            Workbook workbook = WorkbookFactory.create(xlsFile.getInputStream());
            // 获取工作簿
            int sheetCount = workbook.getNumberOfSheets();
            List<List<String>> sheetRes = null;
            for (int sheetIndex = 0; sheetIndex < sheetCount; sheetIndex++) {
                log.info("======解析第{}个工作簿======", sheetIndex);
                Sheet sheet = workbook.getSheetAt(sheetIndex);
                int rowCount = sheet.getLastRowNum() + 1;

                // 跳过表头
                if (rowCount <= 1) {
                    log.info("======>行数为：{}", rowCount);
                    continue;
                }
                Row row = sheet.getRow(1);
                int cellCount = row.getPhysicalNumberOfCells();

                sheetRes = new ArrayList<>();
                List<String> rowRes = null;
                for (int rowIndex = 1; rowIndex < rowCount; rowIndex++) {
                    log.info("======解析第{}行数据======", rowIndex);
                    row = sheet.getRow(rowIndex);
                    if (row == null) {
                        continue;
                    }
                    rowRes = new ArrayList<>();
                    for (int cellIndex = 0; cellIndex < cellCount; cellIndex++) {
                        Cell cell = row.getCell(cellIndex);
                        cell.setCellType(CellType.STRING);
                        rowRes.add(cell.getStringCellValue());
                    }
                    sheetRes.add(rowRes);
                }
                res.put(sheetIndex, sheetRes);
            }
            workbook.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return res;
    }
}
