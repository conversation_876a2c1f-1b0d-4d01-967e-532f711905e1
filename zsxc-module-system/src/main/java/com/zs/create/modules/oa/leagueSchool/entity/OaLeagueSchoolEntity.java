package com.zs.create.modules.oa.leagueSchool.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zs.create.config.SerializerBigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.math.BigDecimal;


/**
 * 网络团校
 *
 * <AUTHOR> @email 
 * @date 2022-12-07 14:07:02
 */
@Data
@TableName("oa_league_school")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oa_league_school对象", description="网络团校")
public class OaLeagueSchoolEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "id")
	    private String id;
	/**
	 * 学号
	 */
	    @ApiModelProperty(value = "学号")
	    private String userCode;
	/**
	 * 学生姓名
	 */
	    @ApiModelProperty(value = "学生姓名")
	    private String userName;
	/**
	 * 学院id
	 */
	    @ApiModelProperty(value = "学院id")
	    private String collegeId;
	/**
	 * 学院名称
	 */
	    @ApiModelProperty(value = "学院名称")
	    private String collegeName;
	/**
	 * 班级id
	 */
	    @ApiModelProperty(value = "班级id")
	    private String classId;
	/**
	 * 班级名称
	 */
	    @ApiModelProperty(value = "班级名称")
	    private String className;
	/**
	 * 学期名称
	 */
	    @ApiModelProperty(value = "学期名称")
	    private String semesterName;
	/**
	 * 学期名称
	 */
		@ApiModelProperty(value = "学习状态，0为结业，默认为0")
		private Integer studyStatus;
	/**
	 * 导入的学时
	 */
		@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
		@JsonSerialize(using = SerializerBigDecimal.class)
		@JsonProperty("hours")
	    @ApiModelProperty(value = "导入的学时")
	    private BigDecimal hours=new BigDecimal("0.0");
	/**
	 * 转化进学时表的学时
	 */
		@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
		@JsonSerialize(using = SerializerBigDecimal.class)
		@JsonProperty("conversionHours")
	    @ApiModelProperty(value = "转化进学时表的学时")
	    private BigDecimal conversionHours=new BigDecimal("0.0");
	/**
	 * 转化进学时表的学时
	 */
		@ApiModelProperty(value = "转化标识，0为未转化，1为已转化，默认为0")
		private Integer convertType;
	/**
	 * 数据类型，0为院级团校，1为网络团校
	 */
	    @ApiModelProperty(value = "数据类型，0为院级团校，1为网络团校")
	    private Integer type;
	/**
	 * 删除标识，0为正常，1为删除，默认为0
	 */
		@ApiModelProperty(value = "删除标识，0为正常，1为删除，默认为0")
		private Integer delFlag;
	/**
	 * 创建时间
	 */
		@ApiModelProperty(value = "创建时间")
		private Date createTime;
	/**
	 * 创建人
	 */
		@ApiModelProperty(value = "创建人")
		private String createBy;
	/**
	 * 更新时间，作为导入时间
	 */
		@ApiModelProperty(value = "更新时间，作为导入时间")
		private Date updateTime;
	/**
	 * 创建人
	 */
		@ApiModelProperty(value = "创建人")
		private String updateBy;

	/**
	 * excel内顺序
	 */
		@ApiModelProperty(value = "excel内顺序")
		private Integer excelOrder;
	/**
	 * 总导入顺序
	 */
		@ApiModelProperty(value = "总导入顺序")
		private Integer allOrder;
	/**
	 * 机构id
	 */
	@TableField(exist = false)
		@ApiModelProperty(value = "机构id")
		private String depId;


}
