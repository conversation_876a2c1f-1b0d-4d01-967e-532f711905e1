package com.zs.create.modules.paramdesign.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 兴趣标签
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-24 10:33:56
 */
@Data
@TableName("sc_label")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_label对象", description="兴趣标签")
public class ScLabelEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.UUID)
	private String id;
	/**
	 * 标签名称
	 */
	private String name;
	/**
	 * 启用状态（0-启用  1-禁用）
	 */
	private Integer enable;
	/**
	 * 删除标记
	 */
	private Integer delFlag;
	/**
	 * 创建人id
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

}
