package com.zs.create.modules.communication.callcenter.service;

import com.zs.create.modules.communication.callcenter.entity.CpChatMessageEntity;
import com.zs.create.modules.communication.callcenter.entity.dto.ChatMsgDTO;

/**
 * @className: ISendMessageService
 * @description: websocket 消息服务类
 * @author: hy
 * @date: 2020-11-28
 **/
public interface IWebsocketMessageService {

    /**
     * websocket 关闭消息通知消息
     * @param sessionId
     */
    void sendWebSocketClosedMessage(String sessionId);

    /**
     * 发送websocket消息
     * @param chatMsgDTO
     */
    void sendWebSocketMessage(ChatMsgDTO chatMsgDTO);


    /**
     * 保存客服消息
     * @param cpChatMessageEntity
     * @return
     */
    CpChatMessageEntity saveCpKfMessage(CpChatMessageEntity cpChatMessageEntity);

    /**
     * 发送离线消息
     * @param chatMsgDTO
     */
    void saveOffLineMessage(ChatMsgDTO chatMsgDTO);

    void sendOpenRemoveOldSessionMessage(String sessionId);
}
