package com.zs.create.modules.communication.association.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.communication.association.entity.CpUserInAssociationEntity;
import com.zs.create.modules.communication.association.service.CpAssociationService;
import com.zs.create.modules.system.entity.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.text.ParseException;
import java.util.List;
import java.util.Map;

@Slf4j
@Api(tags="社团评审")
@RestController
@RequestMapping("/association")
public class CpAssociationController {

    @Autowired
    CpAssociationService cpAssociationService;

    @AutoLog(value = "社团评审添加")
    @ApiOperation(value="社团评审添加", notes="社团评审添加")
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    public Result<SysUser> add(@RequestBody Map map) {
        Result<SysUser> result = new Result<>();
        cpAssociationService.add(map);
        result.success("添加成功！");
        return result;
    }

    @AutoLog(value = "社团评审分页列表")
    @ApiOperation(value="社团评审分页列表", notes="社团评审分页列表")
    @GetMapping("list")
    public Result<?> queryList(@RequestParam(name="formName",required = false) String formName,
                               @RequestParam(name="status",required = false) String status,
                              @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                              @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) throws ParseException {
        Page<Map> page = cpAssociationService.queryList(formName,status,pageNo ,pageSize);
        return Result.ok(page);
    }


    @AutoLog(value = "社团评审通过id查询")
    @ApiOperation(value="社团评审通过id查询", notes="社团评审通过id查询")
    @GetMapping(value = "/queryById")
    public Result<Map<String, Object>> queryById(@RequestParam(name="id") String id) throws ParseException {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> map = cpAssociationService.getById(id);
        result.setResult(map);
        return result;
    }


    @AutoLog(value = "社团评审-通过id删除")
    @ApiOperation(value="社团评审-通过id删除", notes="社团评审-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        cpAssociationService.remove(id);
        return Result.ok("删除成功!");
    }


    /**
     * 编辑
     */
    @AutoLog(value = "社团评审-编辑")
    @ApiOperation(value="社团评审-编辑", notes="社团评审-编辑")
    @PutMapping(value = "/edit")
    public Result<?> edit(@RequestBody Map map) {
        cpAssociationService.updateById(map);
        return Result.ok("编辑成功!");
    }



    /**
     * 发布
     */
    @AutoLog(value = "社团评审-发布")
    @ApiOperation(value="社团评审-发布", notes="社团评审-发布")
    @PutMapping(value = "/release")
    public Result<?> release(@RequestBody Map map) throws ParseException {
        cpAssociationService.release(map);
        return Result.ok("发布成功!");
    }


    /**
     * 调查结果
     */
    @AutoLog(value = "社团评审-调查结果")
    @ApiOperation(value="社团评审-调查结果", notes="社团评审-调查结果")
    @GetMapping(value = "/surveyResults")
    public Result<List<Map<String,Object>>> surveyResults(@RequestParam(name="id",required=true) String id) throws ParseException {
        Result<List<Map<String,Object>>> result=new Result<>();
        List<Map<String,Object>> list=cpAssociationService.surveyResults(id);
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }

    @AutoLog(value = "社团评审文本统计分页列表")
    @ApiOperation(value="社团评审文本统计分页列表", notes="社团评审文本统计分页列表")
    @GetMapping("surveyTextList")
    public Result<?> surveyTextList(@RequestParam(name="questionnaireId",required = true) String questionnaireId,
                                    @RequestParam(name="formItemKey",required = true) String formItemKey,
                                    @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                               @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) throws ParseException {
        Map<String,Object> page = cpAssociationService.surveyTextList(questionnaireId,formItemKey,pageNo ,pageSize);
        return Result.ok(page);
    }


    @AutoLog(value = "小程序社团评审分页列表")
    @ApiOperation(value="小程序社团评审分页列表", notes="小程序社团评审分页列表")
    @GetMapping("queryUserList")
    public Result<?> queryUserList(@RequestParam String id,
                                    @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                    @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) throws ParseException {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<CpUserInAssociationEntity> page = cpAssociationService.queryUserList(id,sysUser,pageNo ,pageSize);
        return Result.ok(page);
    }

}
