package com.zs.create.modules.communication.callcenter.constant;

import java.net.InetAddress;

/**
 * @className: KfConstants
 * @description: 客服常量类
 * @author: hy
 * @date: 2020-11-28
 **/
public class KfConstants {

    /**
     * 在线客服 redis set
     */
    public static final String KF_USER_ONLINE_SET = "kf:user:online";

    /**
     * websocket 在线用户 redis set
     */
    public static final String WEBSOCKET_USER_ONLINE_SET = "websocket:user:online";

    /**
     * 用户最新session 的服务器ip map
     */
    public static final String WEBSOCKET_USER_CURRENT_SESSIONID_SERVERIP = "websocket:user:current_sessionid_serverip";

    /**
     * 客服在线用户数
     */
    public static final String KF_ONLINE_COUNT_KEY = "kf:online:count";
    /**
     * websocket在线用户数
     */
    public static final String WEBSOCKET_ONLINE_COUNT_KEY = "websocket:online:count";


    /**
     * 客户端绑定的客服前缀
     */
    public static final String BIND_KF_USER_CLIENT_SUBFFIX = "bind:kfuser:client:";


    public static final String CLIENT_OFFLINE_STATUS_SUBFFIX = "client::offline:status";


    /**
     * 推送类型
     */
    public static final String PUSH_TYPE = "pushType";
    /**
     * 聊天用户列表
     */
    public static final String PUSH_TYPE_CHAT_USERLIST = "chatUserList";


    /**
     *
     */
    public static final String PUSH_TYPE_CHAT_RECORD= "chatRecord";

    /**
     * websocket 客户端身份
     */
    public static final String CHAT_CLINET_INDENTY = "clientIdentity";

    /**
     * 咨询者身份标识
     */
    public static final String USER_INDENTY = "user";


    /**
     * 客服身份标识
     */
    public static final String KF_INDENTY = "kf";


    public static String getServerIp() {
        try {
            InetAddress ia = InetAddress.getLocalHost();
            String localip = ia.getHostAddress();
            return localip;
        } catch (Exception e) {
            e.printStackTrace();
            return "500";
        }
    }


}
