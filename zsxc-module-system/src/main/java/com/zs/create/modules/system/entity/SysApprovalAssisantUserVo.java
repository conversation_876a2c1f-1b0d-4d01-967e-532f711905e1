package com.zs.create.modules.system.entity;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

@Data
@Accessors(chain = true)
public class SysApprovalAssisantUserVo implements Serializable {
    /**
     * 主键
     */
    private String id;
    /**
     * 用户id
     */
    private String assistantId;
    /**
     * 用户勾选部门id
     */
    private String deptId;
    private String realname;
    private String username;
    private String phone;
    private String email;
    private String createBy;
    private Date createTime;
    /**
     * 部门名称
     */
    private String deptName;
}
