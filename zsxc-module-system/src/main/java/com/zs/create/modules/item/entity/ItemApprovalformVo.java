package com.zs.create.modules.item.entity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zs.create.config.SerializerBigDecimal;
import com.zs.create.modules.workflow.entity.CommentEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="项目审批单Vo", description="项目审批单Vo")
public class ItemApprovalformVo implements Serializable {
    /**
     * 项目名称
     */
    @ApiModelProperty("项目名称")
    private String itemName;

    /**
     * 项目组织方
     */
    @ApiModelProperty(value = "组织方名称")
    private String businessDeptName;

    /**
     * 申报时间
     */
    @ApiModelProperty(value = "申报时间")
    private Date declareTime;
    /**
     * 项目负责（项目创建人）姓名
     */
    @ApiModelProperty(value = "项目负责（项目创建人）姓名")
    private String managerName;

    /**
     * 项目负责（项目创建人）学号
     */
    @ApiModelProperty(value = "项目负责（项目创建人）学号")
    private String managerCode;

    /**
     * 项目负责（项目创建人）学院
     */
    @ApiModelProperty(value = "项目负责（项目创建人）学院")
    private String managerCollege;

    /**
     * 项目负责（项目创建人）所在组织任职
     */
    @ApiModelProperty(value = "项目负责（项目创建人）所在组织任职")
    private String managerOffice;

    /**
     * 项目负责（项目创建人）手机号
     */
    @ApiModelProperty(value = "项目负责（项目创建人）手机号")
    private String managerTel;

    /**
     * 项目负责（项目创建人）邮箱
     */
    @ApiModelProperty(value = "项目负责（项目创建人）邮箱")
    private String managerEmail;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String module;

    /**
     * 项目学时
     */
    @JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
    @JsonSerialize(using = SerializerBigDecimal.class)
    @ApiModelProperty(value = "项目学时")
    private BigDecimal hours;

    /**
     * 有效参与时长
     */
    @ApiModelProperty(value = "有效参与时长")
    private BigDecimal validHours;

    /**
     * 举办开始时间
     */
    @ApiModelProperty(value = "举办开始时间")
    private Date runStTime;

    /**
     * 举办结束时间
     */
    @ApiModelProperty(value = "举办结束时间")
    private Date runEtTime;

    /**
     * 举办形式
     */
    @ApiModelProperty(value = "举办形式")
    private String form;

    /**
     * 举办形式名称
     */
    @ApiModelProperty(value = "举办形式名称")
    private String formName;

    /**
     * 举办地点
     */
    @ApiModelProperty(value = "举办地点")
    private List<ScItemPlaceEntity> places;

    /**
     * 组织方式构想
     */
    @ApiModelProperty(value = "组织方式构想")
    private String conceive;

    /**
     * 经费金额
     */
    @ApiModelProperty(value = "经费金额")
    private BigDecimal outlayMoney;

    /**
     * 经费明细
     */
    @ApiModelProperty(value = "经费明细")
    private String outlayDetail;

    /**
     * 审核记录
     */
    @ApiModelProperty(value = "审核记录")
    private List<CommentEntity> comments;

    /**
     * 项目参与人数（有学时有成绩的）
     */
    @ApiModelProperty(value = "项目参与人数")
    private Integer partakeNum=0;

    /**
     * 项目报名人数
     */
    @ApiModelProperty(value = "项目报名人数")
    private Integer registrationNum=0;

    /**
     * 结项状态
     */
    @ApiModelProperty(value = "结项状态")
    private String conclusionStatus;

    /**
     * 历史项目标识 1：是；0：否
     */
    @ApiModelProperty(value="历史项目标识 1：是；0：否")
    private String historyType;

    @ApiModelProperty(value = "项目预算信息list")
    private List<ScItemBudgetEntity> budgetList;
}
