package com.zs.create.modules.third.scYouthLearning.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 青年大数据向校团委推送结果
 *
 * <AUTHOR> @email 
 * @date 2022-11-24 09:12:50
 */
@Data
@TableName("sc_youth_learning_push")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_youth_learning_push对象", description="青年大数据向校团委推送结果")
public class ScYouthLearningPushEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "主键")
	    private String id;
	/**
	 * 青年大学习期数
	 */
	    @ApiModelProperty(value = "青年大学习期数")
	    private String qishu;
	/**
	 * 0为未推送，1为推送
	 */
	    @ApiModelProperty(value = "0为未推送，1为推送")
	    private Integer type;
	/**
	 * 推送结果
	 */
	    @ApiModelProperty(value = "推送结果")
	    private String pushResult;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**
	 * 更新时间
	 */
	@ApiModelProperty(value = "更新时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;
	/**
	 * 更新人
	 */
	@ApiModelProperty(value = "更新人")
	private String updateBy;

}
