package com.zs.create.modules.communication.vote.entity.vo;

import com.zs.create.modules.communication.vote.entity.CpVoteInfoEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @className: CpVoteInfoVo
 * @description: 投票基本信息vo
 * @author: hy
 * @date: 2020-11-18
 **/
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class CpVoteInfoVo extends CpVoteInfoEntity {

    /**
     * 投票规则
     */
    private List<String> voteRules;
}
