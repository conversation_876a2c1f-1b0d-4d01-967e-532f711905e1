package com.zs.create.modules.quartz.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.modules.delayqueue.constants.ApplyStatusQueueConstants;
import com.zs.create.modules.delayqueue.core.DelayQueue;
import com.zs.create.modules.delayqueue.core.DelayQueueJob;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.enums.ItemAuditStatusEnum;
import com.zs.create.modules.item.service.ScItemService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * @className: ModifyItemStatusJob
 * @description: 修改项目状态定时任务
 * @author: hy
 * @date: 2020-8-6
 **/
public class ModifyItemStatusJob implements Job {
    @Autowired
    ScItemService scItemService;

    @Override
    @Transactional
    public void execute(JobExecutionContext context) {
            DelayQueueJob delayQueueJob = DelayQueue.pop(ApplyStatusQueueConstants.APPLY_START_TOPIC);
        if (null != delayQueueJob) {
            String itemId = delayQueueJob.getMessage();
            ScItemEntity item = scItemService.getById(itemId);
            //当前项目不为空
            if (null != item) {
                scItemService.updateItemApplyStatus(itemId, ItemAuditStatusEnum.APPLYING.getCode());
                //查询父项目
                modifySerialItemStatus(item.getPid());
            }
            DelayQueue.finish(delayQueueJob.getId());
        }

        DelayQueueJob delayQueueJob2 = DelayQueue.pop(ApplyStatusQueueConstants.APPLY_END_TOPIC);
        if(null != delayQueueJob2) {
            String itemId = delayQueueJob2.getMessage();
            ScItemEntity item = scItemService.getById(itemId);
            //当前项目不为空
            if (null != item) {
                scItemService.updateItemApplyStatus(itemId, ItemAuditStatusEnum.END.getCode());
                //查询父项目
                modifySerialItemStatus(item.getPid());
            }
            DelayQueue.finish(delayQueueJob2.getId());
        }



    }


    /**
     * 修改系列项目状态
     * @param serialItemId
     */
    private void modifySerialItemStatus(String serialItemId){
        QueryWrapper<ScItemEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("id", serialItemId);
        ScItemEntity parentItem = scItemService.getOne(queryWrapper);
        // 父项目不为空
        if (parentItem != null) {
            // 查询父项目的子项目
            QueryWrapper<ScItemEntity> childWrapper = new QueryWrapper<>();
            childWrapper.eq("pid", parentItem.getId());
            List<ScItemEntity> childList = scItemService.list(childWrapper);
            // 子项目不为空
            if (!CollectionUtils.isEmpty(childList)) {
                Integer parentItemStatus = scItemService.getParentItemStatus(childList);
                scItemService.updateItemApplyStatus(parentItem.getId() , parentItemStatus);
            }
        }
    }



}
