package com.zs.create.modules.item.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.service.ItemAuditUserService;
import com.zs.create.modules.system.entity.*;
import com.zs.create.modules.system.service.*;
import com.zs.create.modules.system.util.DictUtils;
import jodd.util.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @createUser hy
 * @createTime 2020-6-24
 * @description  项目审核人员
 */
@Service(value = "itemAuditUserService")
@Slf4j
public class ItemAuditUserServiceImpl implements ItemAuditUserService {
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    ISysRoleService sysRoleService;
    @Autowired
    ISysUserRoleService sysUserRoleService;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ISysApprovalAssistantService sysApprovalAssistantService;
    @Autowired
    ISysUserDepartService sysUserDepartService;
    @Autowired
    ItemAuditUserService itemAuditUserService;



    /**
     * 获取指导老师
     * @param deptId
     * @return
     */
    @Override
    public String getGuideTeachers(String deptId) {
        SysDepart sysDepart = sysDepartService.getById(deptId);
        if(null == sysDepart)
            throw new ZsxcBootException("流程配置异常:归属单位不存在");
        if(StringUtils.isEmpty(sysDepart.getGuideTeachers()))
            throw new ZsxcBootException("流程配置异常:指导老师无配置");
        return assistantWapper(sysDepart.getGuideTeachers());
    }

    /**
     * 获取机构秘书
     * @param businessDeptId
     * @return
     */
    @Override
    public String getDeptSecretaries(String businessDeptId) {
        SysDepart sysDepart = sysDepartService.getById(businessDeptId);
        if(null == sysDepart)
            throw new ZsxcBootException("流程配置异常:归属单位不存在");
        if(StringUtils.isEmpty(sysDepart.getSecretaries()))
            throw new ZsxcBootException("流程配置异常:秘书无配置");
        return assistantWapper(sysDepart.getSecretaries());
    }


    /**
     * 获取指导单位负责人
     * @param businessDeptId
     * @return
     */
    @Override
    public String getGuideDeptPrincipal(String businessDeptId) {
        SysDepart sysDepart = sysDepartService.getById(businessDeptId);
        if(null == sysDepart)
            throw new ZsxcBootException("流程配置异常:归属单位不存在");
        if(StringUtils.isEmpty(sysDepart.getGuideDeptId()))
            throw new ZsxcBootException("流程配置异常:归属单位指导部门无配置");
        String guideDeptPrincipal = this.getDeptUsersByRoleCodes("role_zddw_fzr", businessDeptId);
        return assistantWapper(guideDeptPrincipal);
    }


    /**
     *
     * @param roleCodes
     * @param businessDeptId
     * @param filterOrgType
     * @return
     */
    @Override
    public String getRoleUserByPDeptOrgTypeFilter(String roleCodes ,String businessDeptId , String filterOrgType){
        SysDepart sysDepart = sysDepartService.getById(businessDeptId);
        if(null == sysDepart)
            throw new ZsxcBootException("流程配置异常:归属单位不存在");

        //获取pids
        String pids = sysDepart.getPids();
        if(StringUtils.isEmpty(pids)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在");

        List<String> pidList = Arrays.stream(pids.split(",")).collect(Collectors.toList());
        Collection<SysDepart> sysDeparts = sysDepartService.listByIds(pidList);
        if(CollectionUtils.isEmpty(sysDeparts)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在");

        String filterDeptIds = sysDeparts.stream()
                .filter(dept -> dept.getOrgType().equals(filterOrgType))
                .map(depart -> depart.getId())
                .collect(Collectors.joining(StringPool.COMMA));

        if(StringUtils.isEmpty(filterDeptIds)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在机构类型："
                + DictUtils.getValueByCodeAndKey("org_type" ,filterOrgType , "defaultVal" ));

        return getDeptUsersByRoleCodes(roleCodes, filterDeptIds);


    }

    /**
     * 获取用户
     * @param roleCodes 角色编码 ,逗号分割
     * @param deptIds  部门id
     * @return
     */
    public String getDeptUsersByRoleCodes(String roleCodes , String deptIds){
        List<String> deptIdList = new ArrayList<>();
        if(!StringUtils.isEmpty(deptIds)){
            deptIdList = Arrays.stream(deptIds.split(",")).collect(Collectors.toList());
        }
        Collection<SysUser> rolesUser = this.getRolesUser(roleCodes);
        List<String> userIds = rolesUser.stream().map(SysUser::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(userIds)) {
            log.error("当前机构id：{},角色：{} 无审核人"  ,deptIds , roleCodes);
            throw new ZsxcBootException("流程配置异常");
        }
        List<SysUser> userByDeptId = sysUserService.getUserByDeptIds(deptIdList , userIds);
        if(CollectionUtils.isEmpty(userByDeptId)){
            log.error("当前机构id：{},角色：{} 无审核人"  ,deptIds , roleCodes);
            throw new ZsxcBootException("流程配置异常");
        }
        String deptUsers =
                userByDeptId.stream().map(SysUser::getId)
                        .collect(Collectors.joining(StringPool.COMMA));

        return assistantWapper(deptUsers);
    }

    /**
     * 根据角色获取用户
     * @param roleCodes
     * @return String
     */
    @Override
    public String getAllUsersByRoleCodes(String roleCodes) {
        Collection<SysUser> users = this.getRolesUser(roleCodes);
        String userIds =
                users.stream().map(SysUser::getId)
                        .collect(Collectors.joining(StringPool.COMMA));
        return assistantWapper(userIds);
    }

    /**
     * 根据角色获取用户
     * @param roleCodes 角色编码
     * @return Collection<SysUserRole>
     */
    private Collection<SysUser> getRolesUser(String roleCodes){
        if(StringUtils.isEmpty(roleCodes)) throw new ZsxcBootException("流程配置异常:角色不能为空");
        final List<String> roleCodeList = oConvertUtils.str2List(roleCodes, ",");
        QueryWrapper<SysRole> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("role_code" , roleCodeList);
        List<SysRole> roleList = sysRoleService.list(queryWrapper);
        if(CollectionUtils.isEmpty(roleList))
            throw new ZsxcBootException("流程配置异常:" + roleCodes+ "角色不存在");
        List<String> roleIdList = new ArrayList<>();
        roleList.forEach(role-> roleIdList.add(role.getId()));
        QueryWrapper<SysUserRole> roleUserQueryWrapper = new QueryWrapper<>();
        roleUserQueryWrapper.in("role_id" , roleIdList);
        List<SysUserRole> userRoles = sysUserRoleService.list(roleUserQueryWrapper);
        if(CollectionUtils.isEmpty(userRoles))
            throw new ZsxcBootException("流程配置异常:" + roleCodes + "角色下无用户");

        Set<String> userIdList = userRoles.stream().map(SysUserRole::getUserId)
                .collect(Collectors.toSet());

        if(CollectionUtils.isEmpty(userIdList))
            throw new ZsxcBootException("流程配置异常:" + roleCodes + "角色下无用户");

        Collection<SysUser> sysUsers = sysUserService.listByIds(userIdList);

        if(CollectionUtils.isEmpty(sysUsers)){
            throw new ZsxcBootException("流程配置异常:" + roleCodes + "角色下无用户");
        }

        return sysUsers;
    }


    /**
     * 助手包装类
     * @param approvalManIds
     * @return
     */
    @Override
    public String assistantWapper(String approvalManIds){
        if(StringUtils.isEmpty(approvalManIds)) throw new ZsxcBootException("流程配置异常：审核组织无对应负责人");
        final List<String> approvalManIdList = oConvertUtils.str2List(approvalManIds, ",");
        QueryWrapper<SysApprovalAssistant> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("create_by" , approvalManIdList);
        Collection<SysApprovalAssistant> sysApprovalAssistants = sysApprovalAssistantService.list(queryWrapper);

        if(CollectionUtils.isEmpty(sysApprovalAssistants)){
            return approvalManIds;
        }else{
            Set<String> approvalManWrapperSet = new HashSet<>(approvalManIdList);
            final List<String> assistants = sysApprovalAssistants.stream()
                    .map(SysApprovalAssistant::getAssistantId)
                    .collect(Collectors.toList());
            approvalManWrapperSet.addAll(assistants);
            return String.join(StringPool.COMMA, approvalManWrapperSet);
        }
    }


    @Override
    public String getAllHonorAndScoreCodes(String roleCodes , String deptIds,String filterOrgType,String defaultDeptId){
        String deptId ="";
        if (!StringUtils.isEmpty(deptIds)){
            List<String> pidList = Arrays.stream(deptIds.split(",")).collect(Collectors.toList());
            QueryWrapper<SysDepart> queryWrapper=new QueryWrapper<>();
            queryWrapper.in("id",pidList);
            queryWrapper.eq("org_type",filterOrgType);
            List<SysDepart> list=sysDepartService.list(queryWrapper);
            if (list.isEmpty()==false){
                for (SysDepart s:list){
                    deptId+=s.getId() + ",";
                }
                deptId=deptId.substring(0, deptId.lastIndexOf(","));
            }
        }
        return getDeptUsersByRoleCodesHonrsAndScore(roleCodes,deptId,defaultDeptId);
    }

    /*
      荣誉成绩审核
     */
    public String getDeptUsersByRoleCodesHonrsAndScore(String roleCodes , String deptIds,String defaultDeptId){
        List<String> deptIdList = new ArrayList<>();
        if(!StringUtils.isEmpty(deptIds)){
            deptIdList = Arrays.stream(deptIds.split(",")).collect(Collectors.toList());
        }
        Collection<SysUser> rolesUser = this.getRolesUser(roleCodes);
        List<String> userIds = rolesUser.stream().map(SysUser::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(userIds)) {
            throw new ZsxcBootException("当前机构无审核人");
        }
        List<SysUser> userByDeptId=new ArrayList<>();
        userByDeptId = sysUserService.getUserByDeptIds(deptIdList , userIds);
        if (userByDeptId.isEmpty()){
            List<String> defaultDeptIdList = new ArrayList<>();
            if(!StringUtils.isEmpty(defaultDeptId)){
                defaultDeptIdList = Arrays.stream(defaultDeptId.split(",")).collect(Collectors.toList());
            }
            Collection<SysUser> defaultDeptIddListRolesUser = this.getRolesUser(roleCodes);
            List<String> defaultDeptIddListUserIds = defaultDeptIddListRolesUser.stream().map(SysUser::getId).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(defaultDeptIddListUserIds)) {
                throw new ZsxcBootException("当前机构无审核人");
            }
            userByDeptId = sysUserService.getUserByDeptIds(defaultDeptIdList , defaultDeptIddListUserIds);
        }
        if(CollectionUtils.isEmpty(userByDeptId)){
            throw new ZsxcBootException("当前机构无审核人");
        }
        String deptUsers =
                userByDeptId.stream().map(SysUser::getId)
                        .collect(Collectors.joining(StringPool.COMMA));

        return assistantWapper(deptUsers);
    }


    @Override
    public String getUserIdBydeptId(String businessDeptId,Integer type) {  //1--指导老师  2--负责人 3--班主任  4--会长团
        SysDepart sysDepart = sysDepartService.getById(businessDeptId);
        if(null == sysDepart) throw new ZsxcBootException("流程配置异常:归属单位不存在");
        String userIds="";
        if(type==1){
            userIds= sysDepart.getGuideTeachers();
            if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:指导老师无配置");
        }
        if(type==2){
            userIds= sysDepart.getPersonInCharge();
            if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:负责人无配置");
        }
        if(type==3){
            userIds= sysDepart.getHeadmaster();
            if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:班主任无配置");
        }
        if(type==4){
            userIds= sysDepart.getPresidium();
            if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:会长团无配置");
        }
        if(type==5){
            userIds= sysDepart.getBureau();
            if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:主席团无配置");
        }
        return itemAuditUserService.assistantWapper(userIds);
    }

    @Override
    //指导单位负责人
    public String getZDUserIdBydeptId(String businessDeptId) {
        SysDepart sysDepart = sysDepartService.getById(businessDeptId);
        if(null == sysDepart) throw new ZsxcBootException("流程配置异常:归属单位不存在");
        String deptId= sysDepart.getGuideDeptId();
        if(StringUtils.isEmpty(deptId)) throw new ZsxcBootException("流程配置异常:业务指导单位不存在");
        SysDepart depart = sysDepartService.getById(deptId);
        String userIds = depart.getPersonInCharge();
        if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:负责人无配置");
        return itemAuditUserService.assistantWapper(userIds);
    }

    @Override
    //先选level=2 院系，院系不存在再找机关   机构类型不同
    public String getRysbUserIdBydeptIds(String businessDeptId,String level) {
        if (!StringUtils.isEmpty(businessDeptId)){
            List<String> pidList = Arrays.stream(businessDeptId.split(",")).collect(Collectors.toList());
            //先找院系
            QueryWrapper<SysDepart> queryWrapper=new QueryWrapper<>();
            queryWrapper.in("id",pidList);
            queryWrapper.eq("org_type",2);
            List<SysDepart> list=sysDepartService.list(queryWrapper);
            //9月21日添加荣誉申报一个人对应多个部门，则上级很多父级部门，全部查出来返回
            if(!CollectionUtils.isEmpty(list)){
                String assistant = getAssistant(list, level);
                if(StringUtils.isNotBlank(assistant))
                    return assistant;
            }

            //院系没有的话找机关社团
            QueryWrapper<SysDepart> departWrapper=new QueryWrapper<>();
            departWrapper.in("id",pidList);
            departWrapper.eq("org_type",14);
            List<SysDepart> sysDeparts=sysDepartService.list(departWrapper);
            if(!CollectionUtils.isEmpty(sysDeparts)){
                String assistant = getAssistant(sysDeparts, level);
                if(StringUtils.isNotBlank(assistant))
                    return assistant;
            }
        }
        throw new ZsxcBootException("流程配置异常:归属部门上级单位负责人无配置");
    }

    public String getAssistant(List<SysDepart> sysDeparts,String level){
        ArrayList<String> assistantList = new ArrayList<>();
        String deptId ="";
        String ass="";
        for (SysDepart depart:sysDeparts) {
            if(depart.getLevel().equals(level)){
                deptId=depart.getId();
            }
            if(!StringUtils.isEmpty(deptId)){
                SysDepart sysDepart = sysDepartService.getById(deptId);
                String userIds= sysDepart.getPersonInCharge();
                if(!StringUtils.isEmpty(userIds)){
                    assistantList.add(itemAuditUserService.assistantWapper(userIds));
                }
            }
        }
        if(!CollectionUtils.isEmpty(assistantList)){
            String assistant = assistantList.stream().collect(Collectors.joining(StringPool.COMMA));
            List<String> strings = Arrays.asList(assistant.split(","));
            Set<String> collect = strings.stream().collect(Collectors.toSet());
            ass= collect.stream().collect(Collectors.joining(StringPool.COMMA));
        }
        return ass;
    }

    @Override
    public String getUpDeptIdBydeptId(String businessDeptId, String level) {
        SysDepart sysDepart = sysDepartService.getById(businessDeptId);
        if(null == sysDepart) throw new ZsxcBootException("流程配置异常:归属单位不存在");
        //获取pids
        String pids = sysDepart.getPids();
        if(StringUtils.isEmpty(pids)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在");

        List<String> pidList = Arrays.stream(pids.split(",")).collect(Collectors.toList());
        Collection<SysDepart> sysDeparts = sysDepartService.listByIds(pidList);
        if(org.apache.shiro.util.CollectionUtils.isEmpty(sysDeparts)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在");
        String deptId ="";
        for (SysDepart depart:sysDeparts) {
            if(oConvertUtils.null2String(depart.getLevel()).equals(level)){
                deptId=depart.getId();
                break;
            }
        }
        if(StringUtils.isEmpty(deptId)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在机构类型");
        SysDepart depart = sysDepartService.getById(deptId);
        String userIds= depart.getPersonInCharge();
        if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:归属单位上级单位负责人无配置");
        return itemAuditUserService.assistantWapper(userIds);
    }

    @Override
    public String getPersonInChargeUser(String personInCharge) {
        String userIds= personInCharge;
        if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:当前社团负责人无配置");
        return itemAuditUserService.assistantWapper(userIds);
    }

    /**
     * 六有专属院团委审核查询
     * @param depId
     * @return
     */
    @Override
    public String sixHaveYTWByDepId(String depId ,String level) {
        SysDepart sysDepart = sysDepartService.getById(depId);
        if(null == sysDepart)
            throw new ZsxcBootException("流程配置异常:归属单位不存在");
        //获取pids
        String pids = sysDepart.getPids();
        if(StringUtils.isEmpty(pids)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在");

        List<String> pidList = Arrays.stream(pids.split(",")).collect(Collectors.toList());
        Collection<SysDepart> sysDeparts = sysDepartService.listByIds(pidList);
        if(org.apache.shiro.util.CollectionUtils.isEmpty(sysDeparts)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在");
        String deptId ="";
        for (SysDepart depart:sysDeparts) {
            if(oConvertUtils.null2String(depart.getLevel()).equals(level)){
                deptId=depart.getId();
                break;
            }
        }
        if(StringUtils.isEmpty(deptId)) throw new ZsxcBootException("流程配置异常:归属单位上级单位不存在机构类型");
        SysDepart depart = sysDepartService.getById(deptId);
        String userIds= depart.getPersonInCharge();
        if(StringUtils.isEmpty(userIds)) throw new ZsxcBootException("流程配置异常:归属单位上级单位负责人无配置");
        return itemAuditUserService.assistantWapper(userIds);
    }

    /**
     * 获取二课领导小组
     * @param roleCode
     * @return
     */
    @Override
    public String getXtwsj(String roleCode) {
        List<String> xtwUserList = sysUserService.qryUserByRole(roleCode).stream().map(SysUser::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(xtwUserList))
            throw new ZsxcBootException("流程配置异常:二课领导小组无人员配置");
        return assistantWapper(String.join(StringPool.COMMA, xtwUserList));
    }
}
