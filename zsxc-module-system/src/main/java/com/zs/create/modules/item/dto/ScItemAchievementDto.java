package com.zs.create.modules.item.dto;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;


/**
 * Created with IntelliJ IDEA.
 *
 * @Author: yc
 * @Date: 2022/03/28/13:17
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ScItemAchievementDto implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty(value = "学年")
    private String xn;



    @ApiModelProperty(value = "项目选择项id")
    private ScItemIdsDto itemsIds;

    @ApiModelProperty(value = "项目选择项集合")
    private ScItemYearDto itemsRecords;






}
