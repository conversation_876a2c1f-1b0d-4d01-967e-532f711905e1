package com.zs.create.modules.lib.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.lib.entity.ScItemLibraryEntity;
import com.zs.create.modules.score.dto.UserScoreDto;

import java.util.Date;
import java.util.List;
import java.util.Map;


/**
 * @Description 项目库Service层
 *
 * <AUTHOR>
 * @email null
 * @date 2020-06-24 10:44:41
 * @Version: V1.0
 */
public interface ScItemLibraryService extends IService<ScItemLibraryEntity> {

    IPage<ScItemLibraryEntity> listIds(IPage<ScItemLibraryEntity> page,String ids);

    List<ScItemLibraryEntity> queryList(String ids);

    Result<?> jpitemCollect(String jpitemId,String type);

    IPage<ScItemLibraryEntity> pageIsCollectLib(IPage<ScItemLibraryEntity> page);

    Result<ScItemLibraryEntity> queryById(String id);

    Map<String,Object> getLibAggregate(String id);

    ScItemLibraryEntity getPublishAndEva(String id, String ks, String js);
}

