package com.zs.create.modules.oa.budget.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.modules.oa.budget.entity.OaBudgetDeptEntity;
import com.zs.create.modules.oa.budget.entity.OaBudgetItemEntity;
import com.zs.create.modules.oa.budget.entity.OaBudgetProjectEntity;
import com.zs.create.modules.oa.budget.service.OaBudgetDeptService;
import com.zs.create.modules.oa.budget.service.OaBudgetItemService;
import com.zs.create.modules.oa.budget.service.OaBudgetProjectService;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.system.service.ISysDictService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * @Description 预算Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-18 10:10:04
 * @Version: V1.0
 */
@Slf4j
@Api(tags="预算项目")
@RestController
@RequestMapping("/oa/budget/oaBudgetProject")
public class OaBudgetProjectController {
    @Autowired
    OaBudgetProjectService oaBudgetProjectService;
    @Autowired
    OaBudgetItemService oaBudgetItemService;
    @Autowired
    OaBudgetDeptService oaBudgetDeptService;
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    ISysDictService sysDictService;

    /**
      * 通过id查询
     */
    @AutoLog(value = "预算-通过预算项目id查询")
    @GetMapping(value = "/queryById")
    @ApiOperation(value="预算-根据oa预算项目id查询", notes="预算-根据oa预算项目id查询")
    public Result<OaBudgetProjectEntity> queryById(@RequestParam(name="projectId") String projectId) {
        Result<OaBudgetProjectEntity> result = new Result<>();
        OaBudgetProjectEntity oaBudgetProject = oaBudgetProjectService.getById(projectId);
        if(oaBudgetProject==null) {
            result.error500("未找到对应实体");
        }else {
            //处理project form 和module
            if(oaBudgetProject.getForm()!=null)
                oaBudgetProject.setForm_text( sysDictService.queryDictTextByKey("item_form", oaBudgetProject.getForm()));
            if(oaBudgetProject.getModule()!=null)
                oaBudgetProject.setModule_text( sysDictService.queryDictTextByKey("item_module", oaBudgetProject.getModule()));
            QueryWrapper<OaBudgetItemEntity> queryWrapper =
                    new QueryWrapper<OaBudgetItemEntity>().eq("project_id" , projectId)
                    .eq("del_flag" , 0 );
            List<OaBudgetItemEntity> budgetItemEntityList = oaBudgetItemService.list(queryWrapper);
            oaBudgetProject.setBudgetItemList(budgetItemEntityList);
            Long budgetId = oaBudgetProject.getBudgetId();
            OaBudgetDeptEntity budgetDeptEntity = oaBudgetDeptService.getById(budgetId);
            if(null != budgetDeptEntity) {
                SysDepart dept = sysDepartService.getById(budgetDeptEntity.getDeptId());
                budgetDeptEntity.setDepartName(dept==null ? null : dept.getDepartName());
            }
            oaBudgetProject.setOaBudgetDept(budgetDeptEntity);
            result.setResult(oaBudgetProject);
            result.setSuccess(true);
        }
        return result;
    }



}
