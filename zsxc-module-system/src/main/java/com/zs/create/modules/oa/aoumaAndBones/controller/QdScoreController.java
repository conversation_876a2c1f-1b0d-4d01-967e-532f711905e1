package com.zs.create.modules.oa.aoumaAndBones.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.DateUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.utils.FileUtil;
import com.zs.create.modules.oa.aoumaAndBones.dto.QdScoreDto;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdClassEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdScoreEntity;
import com.zs.create.modules.oa.aoumaAndBones.service.QdClassService;
import com.zs.create.modules.oa.aoumaAndBones.service.QdScoreService;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.MapExcelConstants;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgMapExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

/**
 * <AUTHOR> @Description 青马大骨班Controller层 成绩
 * @email
 * @date 2022-11-03 10:43:25
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "青马大骨班成绩")
@RestController
@RequestMapping("/aoumaAndBones/qdScore")
public class QdScoreController {
    @Autowired
    private QdScoreService qdScoreService;

    @Autowired
    private QdClassService qdClassService;

    @Resource
    private SysDepartMapper sysDepartMapper;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "青马大骨班成绩-分页列表查询")
    @ApiOperation(value = "青马大骨班成绩-分页列表查询", notes = "青马大骨班成绩-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<QdScoreEntity>> queryPageList(QdScoreEntity qdScore,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        Result<IPage<QdScoreEntity>> result = new Result<>();
        /*QueryWrapper<QdScoreEntity> queryWrapper = new QueryWrapper();
        queryWrapper.lambda().eq(QdScoreEntity::getClassId,qdScore.getClassId());
        if (oConvertUtils.isNotEmpty(qdScore.getUserId())){
            queryWrapper.lambda().and(i->i.like(QdScoreEntity::getUserId, qdScore.getUserId())
                    .or()
                    .like(QdScoreEntity::getUserName, qdScore.getUserId()));
        }*/
        //自行构造查询参数
        Page<QdScoreEntity> page = new Page<>(pageNo, pageSize);
        //IPage<QdScoreEntity> pageList = qdScoreService.page(page, queryWrapper);

        IPage<QdScoreEntity> pageList = qdScoreService.queryPageList(qdScore,page);

        List<QdScoreEntity> records = pageList.getRecords();
        for (QdScoreEntity record : records) {
            if (oConvertUtils.isNotEmpty(record.getSelectionResult())) {
                record.setForm(record.getSelectionResult());
            }
        }
        pageList.setRecords(records);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    @AutoLog(value = "青马班大骨班成绩导出表格")
    @ApiOperation(value = "青马班大骨班成绩导出表格", notes = "青马班大骨班成绩导出表格")
    @GetMapping(value = "/export/excel")
    public ModelAndView exportServiceExcl(QdScoreEntity qdScore){
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        //获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ExcelExportEntity> entityList = new ArrayList<>();
        ExcelExportEntity entity = new ExcelExportEntity("姓名", "name");
        entity.setWidth(20);
        entityList.add(entity);
        entity = new ExcelExportEntity("学号", "userId");
        entity.setWidth(20);
        entityList.add(entity);
        entity = new ExcelExportEntity("成绩", "score");
        entity.setWidth(20);
        entityList.add(entity);
        entity = new ExcelExportEntity("查重是否通过", "experience");
        entity.setWidth(20);
        entityList.add(entity);
//        entity = new ExcelExportEntity("出勤率", "attendance");
//        entity.setWidth(20);
//        entityList.add(entity);
        entity = new ExcelExportEntity("总学时", "hours");
        entity.setWidth(20);
        entityList.add(entity);
        entity = new ExcelExportEntity("结业与评优建议", "suggest");
        entity.setWidth(20);
        entityList.add(entity);

        List<QdScoreEntity> list = qdScoreService.queryList(qdScore);

        //判断是否是团委
        Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getUsername());
        if (twPersonInCharge) {
            entity = new ExcelExportEntity("结业与评优结果", "selectionResult");
            entity.setWidth(20);
            entityList.add(entity);
        }

        List<Map<String, Object>> dataList = new ArrayList<>();

        for (QdScoreEntity record : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("name", oConvertUtils.null2String(record.getUserName()));
            map.put("userId", oConvertUtils.null2String(record.getUserId()));

            if (oConvertUtils.isNotEmpty(record.getScore())) {
                map.put("score", record.getScore());
            }else {
                map.put("score", "未导入");
            }

            if (oConvertUtils.isNotEmpty(record.getExperience())) {
                String experience = record.getExperience().toString();
                if (experience.equals("1")){
                    map.put("experience", "是");
                }else if(experience.equals("0")){
                    map.put("experience", "否");
                }
            }else {
                map.put("experience", "未导入");
            }

//            if (oConvertUtils.isNotEmpty(record.getAttendance())){
//                map.put("attendance",record.getAttendance()+"%");
//            }else {
//                map.put("attendance","");
//            }
            map.put("hours", oConvertUtils.null2String(record.getHours()));

            if (oConvertUtils.isNotEmpty(record.getSuggest())){
                String suggest = record.getSuggest().toString();
                if (suggest.equals("2")){
                    map.put("suggest", "优秀");
                }else if(suggest.equals("1")){
                    map.put("suggest", "结业");
                }else {
                    map.put("suggest", "无");
                }
            } else {
                map.put("suggest", "");
            }

            if (twPersonInCharge) {
                if (oConvertUtils.isNotEmpty(record.getSelectionResult())) {
                    String selectionResult = record.getSelectionResult().toString();
                    if (selectionResult.equals("2")) {
                        map.put("selectionResult", "优秀");
                    } else if (selectionResult.equals("1")) {
                        map.put("selectionResult", "结业");
                    } else {
                        map.put("selectionResult", "无");
                    }
                } else {
                    map.put("selectionResult", "");
                }
            }
            dataList.add(map);
        }

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("青马大骨班成绩导出表格");
        exportParams.setSecondTitle("导出人：" + sysUser.getRealname() + "，导出时间：" + DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss") + "【 导出说明： 当导出数据量过大时系统将会把数据分成多个Sheet（工作表）导出,默认Sheet从0开始依次递增 】");
        exportParams.setSheetName("Sheet0");
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "青马大骨班成绩导出表格");
        mv.addObject(MapExcelConstants.ENTITY_LIST, entityList);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        return mv;
    }

    /**
     * 添加
     */
    @AutoLog(value = "青马大骨班-添加")
    @ApiOperation(value = "青马大骨班-添加", notes = "青马大骨班-添加")
    @PostMapping(value = "/add")
    public Result<QdScoreEntity> add(@RequestBody QdScoreEntity qdScore) {
        Result<QdScoreEntity> result = new Result<QdScoreEntity>();
        qdScoreService.save(qdScore);
        result.success("添加成功！");
        return result;
    }

    /**
     * 编辑
     */
    @AutoLog(value = "青马大骨班-编辑")
    @ApiOperation(value = "青马大骨班-编辑", notes = "青马大骨班-编辑")
    @PutMapping(value = "/edit")
    public Result<QdScoreEntity> edit(@RequestBody QdScoreEntity qdScore) {
        Result<QdScoreEntity> result = new Result<QdScoreEntity>();
        QdScoreEntity qdScoreEntity = qdScoreService.getById(qdScore.getId());
        if (qdScoreEntity == null) {
            return result.error500("未找到对应成绩");
        } else {
            if (qdScore.getStatus() == 1){
                throw new ZsxcBootException("成绩已确认，不可修改");
            }
            Integer selectionResult = qdScore.getSelectionResult();
            qdScore.setSuggest(selectionResult);
            boolean ok = qdScoreService.updateById(qdScore);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "青马大骨班-通过id删除")
    @ApiOperation(value = "青马大骨班-通过id删除", notes = "青马大骨班-通过id删除")
    @PostMapping(value = "/delete/{id}")
    public Result<?> delete(@PathVariable(name = "id") String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.error("请选择要删除的数据!");
        }
        qdScoreService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "青马大骨班-批量删除")
    @ApiOperation(value = "青马大骨班-批量删除", notes = "青马大骨班-批量删除")
    @PostMapping(value = "/deleteBatch/{ids}")
    public Result<QdScoreEntity> deleteBatch(@PathVariable(name = "ids") String ids) {
        Result<QdScoreEntity> result = new Result<QdScoreEntity>();
        if (ids == null || StringUtils.isEmpty(ids)) {
            result.error500("参数不识别！");
        } else {
            this.qdScoreService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "青马大骨班-通过id查询")
    @ApiOperation(value = "青马大骨班-通过id查询", notes = "青马大骨班-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<QdScoreEntity> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<QdScoreEntity> result = new Result<QdScoreEntity>();
        QdScoreEntity qdScore = qdScoreService.getById(id);
        if (qdScore == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(qdScore);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 自定义导入报名模板下载
     *
     * @param
     * @param
     */
    @AutoLog(value = "自定义导入报名模板下载")
    @ApiOperation(value = "用户导出", notes = "自定义导入报名模板下载")
    @GetMapping(value = "/exportUserExcel")
    public void exportUserExcel(HttpServletResponse response) {
         qdScoreService.exportUserExcel(response);
    }

    /**
     * 通过excel导入数据
     */
    @AutoLog(value = "青马大骨班-成绩导入")
    @ApiOperation(value = "青马大骨班-成绩导入", notes = "青马大骨班-成绩导入")
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(MultipartFile file, String classId) {
        LambdaQueryWrapper<QdScoreEntity> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(QdScoreEntity::getStatus,"1").eq(QdScoreEntity::getClassId,classId);
        int count = qdScoreService.count(queryWrapper);
        if (count>0){
            throw new ZsxcBootException("成绩存在已确认，无法再次导入成绩");
        }
        Result<?> result = new Result<>();
        if (null == file) return result.error500("上传文件为空");
        String str = file.getOriginalFilename().
                substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!"xls".equals(str) && !"xlsx".equals(str)) {
            return result.error500("文件的格式不正确,请使用所下载的模板上传数据！！");
        }
        InputStream in = null;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return qdScoreService.importUserExcel(in, classId);
    }


    @AutoLog(value = "青马大骨班-评优建议筛选")
    @ApiOperation(value = "青马大骨班-评优建议筛选", notes = "青马大骨班-评优建议筛选")
    @RequestMapping(value = "/screeningSuggest", method = RequestMethod.GET)
    public Result<?> screeningSuggest(@RequestParam(name = "classId") String classId) {
        LambdaQueryWrapper<QdScoreEntity> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(QdScoreEntity::getStatus,"1").eq(QdScoreEntity::getClassId,classId);
        int count = qdScoreService.count(queryWrapper);
        if (count>0){
            throw new ZsxcBootException("成绩已公示，无法再次筛选");
        }
        Result<?> result = new Result<>();
        Boolean aBoolean = qdScoreService.screeningSuggest(classId);
        if (aBoolean){
            result.success("筛选成功");
        }else {
            result.error500("筛选失败");
        }
        return result;
    }

    @AutoLog(value = "青马大骨班-模板下载（有数据）")
    @ApiOperation(value="青马大骨班-模板下载（有数据）", notes="青马大骨班-模板下载（有数据）")
    @GetMapping(value = "/exportXls")
    public void exportXls(String classId,HttpServletResponse response) {
        QdClassEntity classEntity = qdClassService.getById(classId);
        if (classEntity==null) throw new ZsxcBootException("当前班级不存在");
        List<QdScoreDto> pageList = new ArrayList<QdScoreDto>();

        QdScoreEntity qdScoreEntity=new QdScoreEntity();
        qdScoreEntity.setClassId(classId);
        List<QdScoreEntity> list = qdScoreService.queryList(qdScoreEntity);
        if (!list.isEmpty()){
            for (QdScoreEntity scoreEntity : list) {
                QdScoreDto scoreDto=new QdScoreDto();
                scoreDto.setUserId(scoreEntity.getUserId());
                scoreDto.setUserName(scoreEntity.getUserName());
                scoreDto.setScore(scoreEntity.getScore());
                scoreDto.setExperience("是");
                pageList.add(scoreDto);
            }
        }
        FileUtil.exportExcel(pageList,"成绩导入","报名人员",QdScoreDto.class,"导入模板.xls",response);
    }

    @AutoLog(value = "青马大骨班-成绩查看")
    @ApiOperation(value="青马大骨班-成绩查看", notes="青马大骨班-成绩查看")
    @GetMapping(value = "/getScoreById")
    public QdScoreEntity getScoreById(String calssId,String userId) {
        LambdaQueryWrapper<QdScoreEntity> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(QdScoreEntity::getClassId,calssId);
        queryWrapper.eq(QdScoreEntity::getUserId,userId);
        QdScoreEntity one = qdScoreService.getOne(queryWrapper);
        return one;
    }

    @AutoLog(value = "青马大骨班-评优结果确认")
    @ApiOperation(value = "青马大骨班-评优结果确认", notes = "青马大骨班-评优结果确认")
    @GetMapping(value = "/confirm")
    public Result<?> confirm(@RequestParam(name="classId")String classId) {
        Result result = qdScoreService.confirm(classId);
        return result;
    }

    @AutoLog(value = "青马大骨班-更新出勤率和总学时")
    @ApiOperation(value = "青马大骨班-更新出勤率和总学时", notes = "青马大骨班-更新出勤率和总学时")
    @GetMapping(value = "/updateScore")
    public void updateScore(@RequestParam(name="classId")String classId) {
        qdScoreService.updateScore(classId);
    }


    @AutoLog(value = "修改结业评优结果模板下载（带数据）")
    @ApiOperation(value = "修改结业评优结果模板下载", notes = "修改结业评优结果模板下载")
    @GetMapping(value = "/editScoreExcelModule")
    public void editScoreExcelModule(String classId,HttpServletResponse response){

        qdScoreService.editScoreExcelModule(classId,response);

    }

    @AutoLog(value = "导入批量修改结业评优结果")
    @ApiOperation(value = "导入批量修改结业评优结果", notes = "导入批量修改结业评优结果")
    @PostMapping("/importExcelEditScore")
    public Result<?> importExcelEditScore(MultipartFile file, String classId){

        //校验：是否可以修改
        int count = qdScoreService.count(new LambdaQueryWrapper<QdScoreEntity>().eq(QdScoreEntity::getStatus, "1")
                .eq(QdScoreEntity::getClassId, classId));
        if (count>0) throw new ZsxcBootException("成绩已确认无法再次导入修改结果");

        Result<?> result = new Result<>();
        if (null == file) return result.error500("上传文件为空");
        String str = file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!"xls".equals(str) && !"xlsx".equals(str)){
            return result.error500("上传文件为空");
        }
        InputStream in = null;
        try {
            in = file.getInputStream();
        } catch (IOException e) {
            e.printStackTrace();
        }

        return qdScoreService.importExcelEditScore(in,classId);
    }

}
