<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.communication.salon.mapper.CpProposalKnotMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.communication.salon.entity.CpProposalKnotEntity" id="cpProposalKnotMap">
        <result property="id" column="id"/>
        <result property="proposalId" column="proposal_id"/>
        <result property="newsHref" column="news_href"/>
        <result property="pics" column="pics"/>
        <result property="remark" column="remark"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id = "todoTaskPage" resultType="com.zs.create.modules.communication.salon.entity.CpSalonProposalEntity">
        SELECT b.*,task.ID_ as task_id from act_ru_task task
        inner join act_ru_identitylink i on i.TASK_ID_ = task.ID_
        INNER JOIN cp_proposal_knot cp on task.PROC_INST_ID_=cp.process_instance_id
        INNER JOIN cp_salon_proposal b on b.id=cp.proposal_id
        <where>
            and `task`.`SUSPENSION_STATE_` = 1
            <if test="taskQueryDTO.assignee != null and taskQueryDTO.assignee != ''">
                and (
                `task`.`ASSIGNEE_` = #{taskQueryDTO.assignee}
                or (
                `task`.`ASSIGNEE_` is null and `i`.TYPE_ = 'candidate' and (`i`.USER_ID_ = #{taskQueryDTO.assignee})
                )
                )
            </if>

            <if test="taskQueryDTO.title != null and taskQueryDTO.title != ''">
                and `b`.title  like CONCAT('%',#{taskQueryDTO.title},'%')
            </if>
        </where>
        order by task.create_time_ desc
    </select>


    <select id = "historyTaskPage" resultType="com.zs.create.modules.communication.salon.entity.CpSalonProposalEntity">
        SELECT a.* FROM cp_salon_proposal a left join cp_proposal_knot b on a.id=b.proposal_id
        <where>
            <if test="taskQueryDTO.assignee != null and taskQueryDTO.assignee != ''">
                b.process_instance_id
                in (select  proc_inst_id_ from act_hi_taskinst taskinst  where  taskinst.ASSIGNEE_ = #{taskQueryDTO.assignee})
            </if>
            <if test="taskQueryDTO.title != null and taskQueryDTO.title != ''">
                and a.title like CONCAT('%',#{taskQueryDTO.title},'%')
            </if>
        </where>
        order by a.update_time desc
    </select>
</mapper>