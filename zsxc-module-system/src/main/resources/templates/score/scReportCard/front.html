<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>正面</title>
    <style>
        body {
            margin: 0;
        }

        .bg {
            height: 776px;
            width: 1080px;
            margin: 0 auto;
            position: relative;
            background-color: #fff;
            background-image: url([[${qrUrl} + 'images/scReportCard/background.png' ]]);
            background-size: 100%;
            font-family: Microsoft YaHei;
        }

        .front_left_area {
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            right: 0;
            padding: 20px;
            z-index: 2;
            margin: 30px;
        }

        .front_left_content {
            width: 50%;
            height: 100%;
            float: left;
        }

        .front_title_area {
            width: 70%;
            margin: 0 auto;
            height: 10%;
        }

        .front_left_top_img {
            width: 100%;
            height: 100%;
        }

        .front_photo_area {
            height: 180px;
            margin: 0 auto;
            text-align: center;
            margin-top: 45px;
            position: relative;
        }

        .avater_img {
            height: 120px;
            width: 120px;
            border: 1px solid #000;
        }

        .badge_img {
            position: absolute;
            top: 75px;
            right: 125px;
            height: 95px;
            width: 95px;
        }

        .front_info_area {
            padding: 10px 10px;

        }

        .front_info_detail {
            padding-bottom: 20px;

        }

        .info_detail_span {
            font-size: 18px;
            font-weight: bold;
            display: inline-block;
            width: 230px;
        }

        .front_code_area {
            margin-left: 150px;
        }

        .front_code {
            height: 150px;
            width: 150px;
            border: 1px solid #000;
        }

        .front_right_area {
            /*position: absolute;*/
            /*left: 0;*/
            /*top: 0;*/
            /*bottom: 0;*/
            /*right: 0;*/
            /*padding: 20px;*/
            /*z-index: 2;*/
            float: right;
        }

        .front_right_content {
            /*float: right;*/
        }

        .front_rigth_title_area {
            width: 70%;
            margin: 0 auto;
        }

        .front_rigth_title_img {
            width: 100%;
            height: 90%;
        }

        .front_right_start_area {
            margin: 0 auto;
            text-align: center;
            margin-top: 20px;
            position: relative;
        }

        .front_right_start_span {
            font-size: 18px;
            font-weight: bold;
        }

        .sum_star {
            display: inline-block;
            vertical-align: text-top;
            height: 20px;
            width: 20px;
            margin-bottom: 10px;
        }

        .front_right_sumStar_span {
            font-size: 18px;
            font-weight: bold;
            margin-left: 10px;
        }

        .front_right_sumHour_span {
            font-size: 18px;
            font-weight: bold;
            margin-left: 10px;
        }

        .front_module_area {
            width: 487px;
            height: 181px;
            margin: 25px auto 0;
        }

        .front_module_item {
            float: left;
            width: 146px;
            height: 181px;
            border-right: 1px solid #4c86da;
            border-bottom: 1px solid #4c86da;
        }

        .front_module_item:first-child {
            border-left: 1px solid #4c86da;
        }

        .module_item_top {
            background-color: #4c86da;
            color: #fff;
            font-size: 16px;
            text-align: center;
        }

        .module_item_bottom {
            height: 24px
        }

        .module_item {
            height: 32px;
            font-size: 16px;
            width: 100%;
            text-align: center;
            display: inline-block;
            line-height: 32px;
            color: black;
        }

        .module_star {
            display: inline-block;
            vertical-align: text-top;
            height: 18px;
            width: 18px;
            margin-right: 2px;
        }

        .star_num {
            margin: 5px 0 0 5px;
        }

        .front_bottom_area {
            height: 150px;
            width: 487px;
            border-bottom: 1px solid #4c86da;
            border-right: 1px solid #4c86da;
            border-left: 1px solid #4c86da;
            margin-top: 20px;
        }

        .front_left_top {
            height: 32px;
            color: #fff;
            text-align: center;
            line-height: 32px;
            background-color: #4c86da;
        }

        .front_bottom_item {
            padding: 9px;
        }

        .front_bottom_item_div {
            display: inline-block;
            width: 79%;
        }

        .front_bottom_item_div2 {
            display: inline-block;
            width: 60%;
        }

        .front_bottom_item_div_last {
            display: inline-block;
            width: 19%;
            text-align: right;
        }

        .front_bottom_item_div_last2 {
            display: inline-block;
            width: 38%;
            text-align: right;
        }

        .front_bottom_area_honor {
            height: 150px;
            width: 487px;
            border-bottom: 1px solid #4c86da;
            border-right: 1px solid #4c86da;
            border-left: 1px solid #4c86da;
            margin-top: 20px;
        }

        .year_text {
            text-align: center;
            margin-top: 20px;
            color: #024099;
            font-size: 17px;
            font-weight: bold;
            width: 100%
        }
    </style>
</head>
<body>
<div class="bg">
    <div class="front_left_area">
        <div class="front_left_content">
            <div class="front_title_area">
                <img alt="" class="front_left_top_img" src="./images/front_logo.png"
                     th:src="${qrUrl} + 'images/scReportCard/logo.png'"/>
            </div>

            <div class="front_photo_area">
                <img alt="" class="avater_img" src="" th:src="${avatar}"/>
                <!--头像 -->
                <img alt="" class="badge_img" src="./images/badge.png"
                     th:src="${qrUrl} + 'images/scReportCard/badge.png'"/>
            </div>

            <div class="front_info_area">
                <p class="front_info_detail">
                    <span class="info_detail_span">姓名：<span th:text="${realname}">无无无</span></span>
                    <span class="info_detail_span">学号：<span th:text="${username}">PB202020</span></span>
                </p>
                <p class="front_info_detail">
                    <span class="info_detail_span">性别：<span
                            th:text="${sex == 1 ? '男' :  (sex == 2 ? '女' : '')}">男</span></span>
                    <span class="info_detail_span">院系：<span th:text="${college}">中国科学技术大学</span></span>
                </p>
                <p class="front_info_detail">
                    <span class="info_detail_span">出生年月：
                        <span th:text="${birthday == null ? '暂无' : #dates.format(birthday, 'yyyy年MM月')}">2022年3月31日</span>
                    </span>
                    <span class="info_detail_span">政治面貌：<span th:text="${politics}?: '暂无'">暂无</span></span>
                </p>
            </div>

            <div class="front_code_area">
                <div class="front_code">
                    <img alt="" src="" style="width: 150px;height: 150px;"
                         th:src="${qrUrl} + 'score/scReportCard/qrCode/' + ${key}"/>
                </div>
            </div>
        </div>

        <div class="front_right_area">
            <div class="front_right_content">
                <div class="front_rigth_title_area">
                    <img alt="" class="front_rigth_title_img" src="./images/front_title.png"
                         th:src="${qrUrl} + 'images/scReportCard/front_title.png'"/>
                </div>

                <div class="year_text">
                    <span th:text="${T(com.zs.create.modules.score.util.ReportCardUtils).getXn(xn)}">2021-2022学年</span>
                </div>

                <div class="front_right_start_area">
                    <span class="front_right_start_span">总星级：</span>
                    <img alt="" class="sum_star" src="./images/full_star.png"
                         th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(sumScore, 0)} + '_star.png'"/>
                    <img alt="" class="sum_star" src="./images/full_star.png"
                         th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(sumScore, 1)} + '_star.png'"/>
                    <img alt="" class="sum_star" src="./images/full_star.png"
                         th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(sumScore, 2)} + '_star.png'"/>
                    <img alt="" class="sum_star" src="./images/full_star.png"
                         th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(sumScore, 3)} + '_star.png'"/>
                    <img alt="" class="sum_star" src="./images/full_star.png"
                         th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(sumScore, 4)} + '_star.png'"/>
                    <span class="front_right_sumStar_span"
                          th:text="${#numbers.formatDecimal(sumScore, 1, 2)}">3.3</span>
                    <span class="front_right_sumHour_span">总学时：<span
                            th:text="${#numbers.formatDecimal(sumHours, 1, 1)}">100</span></span>
                </div>

                <div class="front_module_area">
                    <div class="front_module_item">
                        <div class="module_item_top">模块</div>
                        <div class="module_item_bottom">
                            <span class="module_item">德（<span
                                    th:text="${#numbers.formatDecimal(hours.d, 1, 1)}">10</span>学时）</span>
                            <span class="module_item">智（<span
                                    th:text="${#numbers.formatDecimal(hours.z, 1, 1)}">10</span>学时）</span>
                            <span class="module_item">体（<span
                                    th:text="${#numbers.formatDecimal(hours.t, 1, 1)}">10</span>学时）</span>
                            <span class="module_item">美（<span
                                    th:text="${#numbers.formatDecimal(hours.m, 1, 1)}">10</span>学时）</span>
                            <span class="module_item">劳（<span
                                    th:text="${#numbers.formatDecimal(hours.l, 1, 1)}">10</span>学时）</span>
                        </div>
                    </div>

                    <div class="front_module_item" style="width: 191px;">
                        <div class="module_item_top">星级</div>
                        <div class="module_item_bottom">
                            <span class="module_item"
                                  th:each="module : ${T(com.zs.create.modules.score.util.ReportCardUtils).getModuleList()}">
                                <img alt="" class="module_star" src="./images/full_star.png"
                                     th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(score.get(module), 0)} + '_star.png'"/>
                                <img alt="" class="module_star" src="./images/full_star.png"
                                     th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(score.get(module), 1)} + '_star.png'"/>
                                <img alt="" class="module_star" src="./images/full_star.png"
                                     th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(score.get(module), 2)} + '_star.png'"/>
                                <img alt="" class="module_star" src="./images/full_star.png"
                                     th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(score.get(module), 3)} + '_star.png'"/>
                                <img alt="" class="module_star" src="./images/full_star.png"
                                     th:src="${qrUrl} + 'images/scReportCard/' + ${T(com.zs.create.modules.score.util.ReportCardUtils).star(score.get(module), 4)} + '_star.png'"/>
                               <span class="star_num"
                                     th:text="${#numbers.formatDecimal(score.get(module), 1, 2)}">5.0</span>
                            </span>
                        </div>
                    </div>

                    <div class="front_module_item">
                        <div class="module_item_top">同年级活跃度</div>
                        <div class="module_item_bottom">
                            <span class="module_item" th:text="${activity.d}">5%~10%</span>
                            <span class="module_item" th:text="${activity.z}">5%~10%</span>
                            <span class="module_item" th:text="${activity.t}">5%~10%</span>
                            <span class="module_item" th:text="${activity.m}">5%~10%</span>
                            <span class="module_item" th:text="${activity.l}">5%~10%</span>
                        </div>
                    </div>
                </div>

                <div class="front_bottom_area">
                    <div class="front_bottom_left">
                        <div class="front_left_top">
                            <span>主要荣誉</span>
                        </div>
                        <div th:each="record : ${honor}" th:remove="tag">
                            <div class="front_left_bottom"
                                 th:style="${record.bonusPenaltyName.length() > 16} ? 'font-size: 12px;'">
                                <div class="front_bottom_item">
                                    <div class="front_bottom_item_div" th:text="${record.bonusPenaltyName}">
                                        主要荣誉名称主要荣誉名称
                                    </div>
                                    <div class="front_bottom_item_div_last"
                                         th:text="${#dates.format(record.createTime, 'yyyy/MM/dd')?: '--'}">
                                        2020-03-21~2020-03-21
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="front_bottom_area_honor">
                    <div class="front_bottom_left">
                        <div class="front_left_top">
                            <span>主要履职</span>
                        </div>
                        <div th:each="record : ${work}" th:remove="tag">
                            <div class="front_left_bottom" th:style="${record.job.length() > 16} ? 'font-size: 12px;'">
                                <div class="front_bottom_item">
                                    <div class="front_bottom_item_div2" th:text="${record.job}">
                                        主要履职名称主要履职名称
                                    </div>
                                    <div class="front_bottom_item_div_last2"
                                         th:text="${record.startTime == null ? '--' : (#dates.format(record.startTime, 'yyyy/MM/dd') + '~' + #dates.format(record.endTime, 'yyyy/MM/dd'))}">
                                        2020-03-21~2020-03-21
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>
