package com.zs.create.modules.paramdesign.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.constant.CommonConstant;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.paramdesign.entity.ScLabelEntity;
import com.zs.create.modules.paramdesign.service.ScLabelService;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * @Description 兴趣标签Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-24 10:33:56
 * @Version: V1.0
 */
@Slf4j
@Api(tags="兴趣标签")
@RestController
@RequestMapping("/paramdesign/scLabel")
public class ScLabelController {
    @Autowired
    private ScLabelService scLabelService;
    @Autowired
    private ISysUserService sysUserService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "兴趣标签-分页列表查询")
    @ApiOperation(value="兴趣标签-分页列表查询", notes="兴趣标签-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ScLabelEntity>> queryPageList(ScLabelEntity scLabel,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<ScLabelEntity>> result = new Result<>();
        String labelName=scLabel.getName();
        QueryWrapper<ScLabelEntity> queryWrapper;
        if(StringUtils.isNotEmpty(labelName)){
            scLabel.setName(null);
        }
        queryWrapper = QueryGenerator.initQueryWrapper(scLabel, req.getParameterMap());
        if (StringUtils.isNotEmpty(labelName)){
            queryWrapper.like("name",labelName);
        }
        Page<ScLabelEntity> page = new Page<>(pageNo, pageSize);
        IPage<ScLabelEntity> pageList = scLabelService.page(page, queryWrapper);
        if(null != page.getRecords()){
            pageList.getRecords().stream().forEach(scLabelEntity -> {
                if(StringUtils.isNotEmpty(scLabelEntity.getUpdateBy())){
                    SysUser user = sysUserService.getUserById(scLabelEntity.getUpdateBy());
                    if(null!= user)
                    scLabelEntity.setUpdateBy(user.getRealname());
                }
            });
        }

        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "兴趣标签-添加")
    @ApiOperation(value="兴趣标签-添加", notes="兴趣标签-添加")
    @PostMapping(value = "/add")
    public Result<ScLabelEntity> add(@RequestBody ScLabelEntity scLabel) {
            Result<ScLabelEntity> result = new Result<>();
            scLabelService.save(scLabel);
            result.success("添加成功！");
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "兴趣标签-编辑")
    @ApiOperation(value="兴趣标签-编辑", notes="兴趣标签-编辑")
    @PutMapping(value = "/edit")
    public Result<ScLabelEntity> edit(@RequestBody ScLabelEntity scLabel) {
        Result<ScLabelEntity> result = new Result<>();
        ScLabelEntity scLabelEntity = scLabelService.getById(scLabel.getId());
        if(scLabelEntity==null) {
           return result.error500("未找到对应实体");
        }else {
            boolean ok = scLabelService.updateById(scLabel);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "兴趣标签-通过id删除")
    @ApiOperation(value="兴趣标签-通过id删除", notes="兴趣标签-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id") String id) {
            scLabelService.removeById(id);
		    return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "兴趣标签-批量删除")
    @ApiOperation(value="兴趣标签-批量删除", notes="兴趣标签-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<ScLabelEntity> deleteBatch(@RequestParam(name="ids") String ids) {
        Result<ScLabelEntity> result = new Result<>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.scLabelService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "兴趣标签-通过id查询")
    @ApiOperation(value="兴趣标签-通过id查询", notes="兴趣标签-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScLabelEntity> queryById(@RequestParam(name="id") String id) {
        Result<ScLabelEntity> result = new Result<>();
        ScLabelEntity scLabel = scLabelService.getById(id);
        if(scLabel==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(scLabel);
            result.setSuccess(true);
        }
        return result;
    }


    @AutoLog(value = "获取所有标签")
    @ApiOperation(value="获取所有标签", notes="获取所有标签")
    @GetMapping(value = "/queryListLabel")
    public Result<?> queryListLabel(){
        Result<List<ScLabelEntity>> result = new Result<>();
        QueryWrapper<ScLabelEntity> scLabelQueryWrapper = new QueryWrapper<>();
        scLabelQueryWrapper.eq("enable", CommonConstant.DEL_FLAG_0);
        scLabelQueryWrapper.eq("del_flag", CommonConstant.DEL_FLAG_0);
        List<ScLabelEntity> label=scLabelService.list(scLabelQueryWrapper);
        result.setSuccess(true);
        result.setResult(label);
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request) {
        // Step.1 组装查询条件
        QueryWrapper<ScLabelEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                ScLabelEntity scLabel = JSON.parseObject(deString, ScLabelEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(scLabel, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<ScLabelEntity> pageList = scLabelService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "兴趣标签列表");
        mv.addObject(NormalExcelConstants.CLASS, ScLabelEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("兴趣标签列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ScLabelEntity> listScLabels = ExcelImportUtil.importExcel(file.getInputStream(), ScLabelEntity.class, params);
                scLabelService.saveBatch(listScLabels);
                return Result.ok("文件导入成功！数据行数:" + listScLabels.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }

}
