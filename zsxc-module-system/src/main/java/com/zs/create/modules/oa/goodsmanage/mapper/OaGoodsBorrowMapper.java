package com.zs.create.modules.oa.goodsmanage.mapper;

import java.util.List;
import java.util.Map;

import com.zs.create.modules.oa.goodsmanage.entity.OaGoodsBorrowEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description 物资管理Mapper层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-18 09:56:34
 * @Version: V1.0
 */
public interface OaGoodsBorrowMapper extends BaseMapper<OaGoodsBorrowEntity> {
    OaGoodsBorrowEntity  getSurplusNumById(String goodsId);
    OaGoodsBorrowEntity  getBorrowByGoodsId(String goodsId);
    List<Map<String,Object>> getDictList();
    List<OaGoodsBorrowEntity> getInvalidGoodsList(String time);
}
