package com.zs.create.modules.weixin.service.impl;

import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.modules.weixin.entity.SysWeixinMessageEntity;
import com.zs.create.modules.weixin.entity.SysWeixinUserEntity;
import com.zs.create.modules.weixin.entity.TextMessage;
import com.zs.create.modules.weixin.mapper.SysWeixinMessageMapper;
import com.zs.create.modules.weixin.mapper.SysWeixinUserMapper;
import com.zs.create.modules.weixin.service.SysWeixinYesAndNoService;
import com.zs.create.modules.weixin.util.MessageUtil;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.PrintWriter;
import java.util.Date;
import java.util.Map;

@Service
public class SysWeixinYesAndNoImpl implements SysWeixinYesAndNoService{
    @Autowired
    private SysWeixinUserMapper sysWeixinUserMapper;
    @Autowired
    private SysWeixinMessageMapper sysWeixinMessageMapper;
    @Autowired
    WxMpService wxMpService;
    @Value("${gzhAppid}")
    private String appid;
    @Override
    public String newMessageRequest(HttpServletRequest request, HttpServletResponse response) {
        try {
            request.setCharacterEncoding("UTF-8");
            //1.将微信回调xml数据转换成map
            Map<String, String> requestMap = MessageUtil.xmlToMap(request);
            //2.获取用户的openId
            String openId = requestMap.get("FromUserName");
            //自动回复消息
            String respMessage = "";
            String toUserName = requestMap.get("ToUserName");
            // 消息类型
            String msgType = requestMap.get("MsgType");
            //3.判断消息是否事事件消息
            if (requestMap.get("MsgType").equals("event")) {
                //获取事件类型
                String eventType = requestMap.get("Event");
                //判断事件类型
                if ("subscribe".equals(eventType)) {
                    String lang = "zh_CN"; //语言
                    WxMpUser user = null;
                    try {
                        user = wxMpService.getUserService().userInfo(openId,lang);
                    } catch (WxErrorException e) {
                        e.printStackTrace();
                    }
                    SysWeixinUserEntity sysWeixinUserEntity =new SysWeixinUserEntity();
                    sysWeixinUserEntity.setId(SnowIdUtils.uniqueLongHex()).setOpenId(user.getOpenId())
                            .setAppId(appid).setNickname(user.getNickname()).setSex(user.getSexDesc())
                            .setLanguage(user.getLanguage()).setHeadimgurl(user.getHeadImgUrl())
                            .setUnionId(user.getUnionId());
                    sysWeixinUserMapper.insert(sysWeixinUserEntity);
                }

                if ("unsubscribe".equals(eventType)) {
                    sysWeixinUserMapper.deleteOpenid(openId);
                }
            }

            SysWeixinMessageEntity sysWeixinMessageEntity = sysWeixinMessageMapper.getNewByCt();
            if(sysWeixinMessageEntity!=null){
                // 文本消息
                if (msgType.equals(MessageUtil.REQ_MESSAGE_TYPE_TEXT)) {
                    //自动回复
                    TextMessage text = new TextMessage();
                    text.setContent(sysWeixinMessageEntity.getAutoReply());
                    text.setToUserName(openId);
                    text.setFromUserName(toUserName);
                    text.setCreateTime(new Date().getTime());
                    text.setMsgType(msgType);
                    respMessage = MessageUtil.textMessageToXml(text);
                }
                // 事件推送
                else if (msgType.equals(MessageUtil.REQ_MESSAGE_TYPE_EVENT)) {
                    String evenType = requestMap.get("Event");// 事件类型
                    // 订阅
                    if (evenType.equals(MessageUtil.EVENT_TYPE_SUBSCRIBE)) {
                        //文本消息
                        TextMessage text = new TextMessage();
                        text.setContent(sysWeixinMessageEntity.getFocusReply());
                        text.setToUserName(openId);
                        text.setFromUserName(toUserName);
                        text.setCreateTime(new Date().getTime());
                        text.setMsgType(MessageUtil.REQ_MESSAGE_TYPE_TEXT);
                        respMessage = MessageUtil.textMessageToXml(text);
                    }
                    // TODO 取消订阅后用户再收不到公众号发送的消息，因此不需要回复消息
                    else if (evenType.equals(MessageUtil.EVENT_TYPE_UNSUBSCRIBE)) {// 取消订阅
                    }
                }
            }
            // 响应消息
            PrintWriter out = null;
            out = response.getWriter();
            out.print(respMessage);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return "";
    }

}
