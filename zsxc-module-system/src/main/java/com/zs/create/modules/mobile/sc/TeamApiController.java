package com.zs.create.modules.mobile.sc;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.backbone.utils.UnsensitiveUtils;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.entity.SysUserDepart;
import com.zs.create.modules.system.entity.SysUserTeamEntity;
import com.zs.create.modules.system.service.*;
import com.zs.create.modules.system.sqlAspect.SqlLog;
import com.zs.create.modules.system.vo.UserTeamAuditDTO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.TaskService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.UnsupportedEncodingException;
import java.util.*;
import java.util.stream.Collectors;

import static cn.hutool.core.util.StrUtil.replace;

@Slf4j
@Api(tags = "手机端-我的社团api")
@RestController
@RequestMapping("/mobile/team")
public class TeamApiController {


    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private ISysUserDepartService sysUserDepartService;
    @Autowired
    private SysUserTeamService sysUserTeamService;
    @Autowired
    private ISysDictService sysDictService;
    @Autowired
    private ISysUserRoleService userRoleService;
    @Autowired
    private UnsensitiveUtils unsensitiveUtils;
    @Autowired
    private CampusAppService campusAppService;
    @Autowired
    TaskService taskService;
    @Value("${qrUrl}")
    private String qrUrl;
    @Value("${qdUrl}")
    private String qdUrl;


    /**
     * 查出我的部门,并以树结构数据格式响应给前端
     *
     * @return
     */
    @ApiOperation(value = "我的社团", notes = "我的社团")
    @GetMapping(value = "/myTeamList")
    public Result<List<SysDepart>> myTeamList(String orgType) {
        Result<List<SysDepart>> result = new Result<>();
        List<SysDepart> list = sysDepartService.myTeamList(orgType);
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }


    //我的社团详情
    @ApiOperation(value = "我的社团详情", notes = "我的社团详情")
    @GetMapping(value = "/myTeamById")
    public Result<SysDepart> myTeamById(String id) {
        Result<SysDepart> result = new Result<>();
        SysDepart sysDepart = sysDepartService.getById(id);
        //代转化部门负责人等
        if (!sysDepart.getParentId().isEmpty()) {
            SysDepart deptById = sysDepartService.getById(sysDepart.getParentId());
            if (null != deptById) {
                sysDepart.setParentName(deptById.getDepartName());
            }

        }

        if (!sysDepart.getGuideDeptId().isEmpty()) {
            List<String> guideDeptIds = Arrays.asList(sysDepart.getGuideDeptId().split(","))
                    .stream().sorted().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(guideDeptIds)) {
                Collection<SysDepart> sysDeparts = sysDepartService.listByIds(guideDeptIds);
                if (!CollectionUtils.isEmpty(sysDeparts)) {
                    String guideDeptName = sysDeparts.stream().map(SysDepart::getDepartName)
                            .collect(Collectors.joining(","));
                    sysDepart.setGuideDeptName(guideDeptName);
                }
            }

        }

        sysDepart.setOrgTypeName(sysDictService.queryDictTextByKey("org_type", sysDepart.getOrgType()));
        SysDepart depart = sysDepartService.getSubmitName(id);
        if (!StringUtils.isEmpty(depart.getGuideTeachersName())) {
            sysDepart.setGuideTeachersName(depart.getGuideTeachersName());
        }

        String guideTeachers = depart.getGuideTeachers();
        if (StringUtils.isNotBlank(guideTeachers)) {
            List<String> guideTeacherIds = Arrays.asList(guideTeachers.split(","))
                    .stream().sorted().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(guideTeacherIds)) {
                sysDepart.setGuideTeachersList(sysUserService.listByIds(guideTeacherIds));
            }

        }

        String personInChargeIds = depart.getPersonInCharge();
        if (StringUtils.isNotBlank(personInChargeIds)) {
            List<String> personInChargeIdList = Arrays.asList(personInChargeIds.split(","))
                    .stream().sorted().collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(personInChargeIdList)) {
                sysDepart.setPersonInChargeList(sysUserService.listByIds(personInChargeIdList));
            }

        }

        if (!StringUtils.isEmpty(depart.getPersonInChargeName())) {
            sysDepart.setPersonInChargeName(depart.getPersonInChargeName());
        }
        result.setResult(sysDepart);
        result.setSuccess(true);
        return result;
    }


    @GetMapping("isNotPersonInCharge")
    @AutoLog(value = "是否是社团负责人")
    @ApiOperation(value = "是否是社团负责人", notes = "是否是社团负责人")
    public Result<Boolean> isNotPersonInCharge(@RequestParam String id) {
        Result<Boolean> result = new Result<>();
        SysDepart depart = sysDepartService.getById(id);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (depart == null) throw new ZsxcBootException("当前参数不正确");
        Boolean boo = sysDepartService.getMaxDepart(depart, sysUser);
        result.setSuccess(true);
        result.setResult(boo);
        return result;
    }


    //我的社团详情
    @ApiOperation(value = "我的社团人员详情", notes = "我的社团人员详情")
    @GetMapping(value = "/myTeamUserById")
    public Result<SysUser> myTeamUserById(String userId) {
        Result<SysUser> result = new Result<>();
        SysUser user = sysUserService.getById(userId);
        result.setResult(user);
        result.setSuccess(true);
        return result;
    }


    //我的社团人员
    @ApiOperation(value = "我的社团人员", notes = "我的社团人员")
    @GetMapping(value = "/myTeamListUser")
    public Result<IPage<SysUser>> myTeamListUser(String id, String username, @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                 @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize, HttpServletRequest req) {
        Result<IPage<SysUser>> result = new Result<>();
        Page<SysUser> page = new Page<>(pageNo, pageSize);
        IPage<SysUser> pageList = sysUserService.myTeamListUser(page, id, username);
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        boolean contains = unsensitiveUtils.isUnsensitive(loginUser.getUsername());
        if (pageList.getTotal() > 0) {
            if (!contains) {
                for (SysUser sysUser : pageList.getRecords()) {
                    String phone = sysUser.getPhone();
                    String email = sysUser.getEmail();
                    if (oConvertUtils.isNotEmpty(phone)) {
                        sysUser.setPhone(replace(phone, 3, 7, '*'));
                    }
                    if (oConvertUtils.isNotEmpty(email)) {
                        sysUser.setEmail(replace(email, 1, email.length() - 3, '*'));
                    }
                }
            }
        }
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 编辑数据 编辑部门的部分数据,并保存到数据库
     *
     * @param sysDepart
     * @return
     */
    @ApiOperation(value = "我的社团编辑", notes = "我的社团编辑")
    @PostMapping(value = "/edit")
    public Result<SysDepart> edit(@RequestBody @Valid SysDepart sysDepart, HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        sysDepart.setUpdateBy(sysUser.getUsername());
        Result<SysDepart> result = new Result<SysDepart>();
        SysDepart sysDepartEntity = sysDepartService.getById(sysDepart.getId());

        boolean boo = sysDepartService.getMaxDepart(sysDepartEntity, sysUser);
        if (boo == false) throw new ZsxcBootException("您不是当前部门负责人，暂无权限");

        if (sysDepartEntity == null) {
            result.error500("未找到对应实体");
        } else {
            boolean ok = sysDepartService.updateDepartDataById(sysDepart, sysUser.getUsername());
            if (ok) {
                result.success("修改成功!");
            }
        }
        return result;
    }

    @ApiOperation(value = "我的社团人员编辑", notes = "我的社团人员编辑")
    @PostMapping(value = "/myTeamUserEdit")
    public Result<SysUser> myTeamUserEdit(@RequestBody JSONObject jsonObject) {
        Result<SysUser> result = new Result<>();
        try {
            String departs = jsonObject.getString("selecteddeparts");
            LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            SysDepart depart = sysDepartService.getById(departs);
            boolean boo = sysDepartService.getMaxDepart(depart, sysUser);
            if (boo == false) throw new ZsxcBootException("您不是当前部门负责人，暂无权限");
            SysUser ser = sysUserService.getById(jsonObject.getString("id"));
            if (ser == null) {
                result.error500("未找到对应实体");
            } else {
                SysUser user = JSON.parseObject(jsonObject.toJSONString(), SysUser.class);
                user.setUpdateTime(new Date());
                //String passwordEncode = PasswordUtil.encrypt(user.getUsername(), user.getPassword(), sysUser.getSalt());
                user.setPassword(ser.getPassword());
                String roles = jsonObject.getString("selectedroles");
                sysUserService.editUserWithRole(user, roles);
                sysUserService.editUserWithDepart(user, departs);
                result.success("修改成功!");
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500(e.getMessage());
        }
        return result;
    }


    //删除用户与社团关系
    @ApiOperation(value = "我的社团删除用户与社团关系", notes = "我的社团删除用户与社团关系")
    @PostMapping(value = "/myTeamUserDelete")
    @SqlLog(value = "删除用户与社团关系", logType = 4)
    public Result<?> myTeamUserDelete(@RequestBody JSONObject jsonObject) {
        Result<?> result = new Result<>();

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysDepart depart = sysDepartService.getById(jsonObject.getString("depId"));
        boolean boo = sysDepartService.getMaxDepart(depart, sysUser);
        if (boo == false) throw new ZsxcBootException("您不是当前部门负责人，暂无权限");

        QueryWrapper<SysUserDepart> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("dep_id", jsonObject.getString("depId"));
        queryWrapper.eq("user_id", jsonObject.getString("userId"));
        sysUserDepartService.remove(queryWrapper);
        result.success("解除成功");
        return result;
    }


    //添加用户与社团关系
    @ApiOperation(value = "我的社团添加用户与社团关系", notes = "我的社团添加用户与社团关系")
    @PostMapping(value = "/myTeamUserSave")
    public Result<?> myTeamUserSave(@RequestBody JSONObject jsonObject) {
        Result<?> result = new Result<>();

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysDepart depart = sysDepartService.getById(jsonObject.getString("depId"));
        boolean boo = sysDepartService.getMaxDepart(depart, sysUser);
        if (boo == false) throw new ZsxcBootException("您不是当前部门负责人，暂无权限");

        List<SysUserDepart> userDeparts = new ArrayList<>();
        List<String> list = Arrays.asList(jsonObject.getString("userIds").split(","));
        //错误数据记录集合
        List<String> errors = new LinkedList<>();
        int successCnt = 0;
        if (list.size() <= 0) {
            throw new ZsxcBootException("请先选择用户");
        } else {
            for (int i = 0; i < list.size(); i++) {
                QueryWrapper<SysUserDepart> userDepartQueryWrapper = new QueryWrapper<>();
                userDepartQueryWrapper.eq("user_id", list.get(i));
                userDepartQueryWrapper.eq("dep_id", jsonObject.getString("depId"));
                Integer deptCnt = sysUserDepartService.count(userDepartQueryWrapper);
                if (deptCnt > 0) {
                    errors.add("第" + (i + 1) + "条数据，学生已添加此社团");
                    continue;
                }
                SysUserDepart sysUserDepart = new SysUserDepart(null, list.get(i), jsonObject.getString("depId"));
                userDeparts.add(sysUserDepart);
                successCnt++;
            }
            sysUserDepartService.saveBatch(userDeparts);

        }

        if (CollectionUtils.isEmpty(errors)) {
            return Result.ok("成功添加：" + successCnt + "条");
        } else {
            String tips = "成功：" + successCnt + "条,失败："
                    + errors.size() + "条,失败原因：" + errors.size() + "位用户已添加此社团";
            return Result.error(tips);
        }

    }


    @AutoLog(value = "获取静态二维码")
    @ApiOperation(value = "获取静态二维码", notes = "获取静态二维码")
    @GetMapping(value = "/staticQrcode/{depId}")
    public Result<?> getStaticQrcode(@PathVariable String depId) throws UnsupportedEncodingException {
        Map<String, Object> res = sysUserTeamService.getStaticQrCode(depId);
        return Result.ok(res);
    }


    @AutoLog(value = "社团报名")
    @ApiOperation(value = "社团报名", notes = "社团报名")
    @GetMapping(value = "/wx/{depId}")
    public ModelAndView joinTeam(@PathVariable String depId) throws UnsupportedEncodingException {
        SysDepart sysDepart = sysDepartService.getById(depId);
        if (sysDepart == null) {
            throw new ZsxcBootException("deptId参数不正确");
        }

        ModelAndView mv = new ModelAndView("sign/joinClub");
        mv.addObject("qrUrl", qrUrl);
        mv.addObject("dept", sysDepart);
        mv.addObject("appId", "");

        return mv;
//        SysDepart joinDept = sysDepartService.getById(depId);
//        if(null == joinDept) throw new ZsxcBootException("deptId参数不正确");
//        String userId="";
//        //String message = "";
//        String deptName = URLEncoder.encode(joinDept.getDepartName(),"UTF-8");
//        try{
//            userId= sysUserTeamService.joinTeam(depId,code);//返回user_id
//        }catch(ZsxcBootException ze){
//            ModelAndView errorMv = new ModelAndView("sign/sign_callback");
//            String message = ze.getMessage();
//            Map<String , Object> resultMap = new HashMap<>(2);
//            resultMap.put("success" , false );
//            resultMap.put("message" , message );
//            errorMv.addObject("qrUrl" , qrUrl);
//            errorMv.addObject("result" , resultMap);
//            return errorMv;
//        }
//
//        String teamUrl = qdUrl+"?deptId=" + depId + "&deptName=" + deptName + "&userId="+userId;
//        ModelAndView mv = new ModelAndView("redirect:" + teamUrl);
//        return mv;
    }


    @ApiOperation(value = "提交报名", notes = "提交报名")
    @PostMapping(value = "/signUpTeam")
    //@NoRepeatSubmit(expireSeconds = 3)
    public Result<?> signUpTeam(@RequestBody SysUserTeamEntity teamEntity) {
        Result<?> result = new Result<>();
        String depId = teamEntity.getDepId();
        SysDepart sysDepart = sysDepartService.getById(depId);
        if (teamEntity.getAuditType().equals("0")) {
            QueryWrapper<SysUserDepart> userDepartQueryWrapper = new QueryWrapper<>();
            userDepartQueryWrapper.eq("user_id", teamEntity.getUserId());
            userDepartQueryWrapper.eq("dep_id", teamEntity.getDepId());
            Integer joinedDeptCnt = sysUserDepartService.count(userDepartQueryWrapper);
            if (joinedDeptCnt > 0) throw new ZsxcBootException("您已加入此社团 ，请勿重复提交!");
            QueryWrapper<SysUserTeamEntity> teamEntityQueryWrapper = new QueryWrapper<>();
            teamEntityQueryWrapper.eq("user_id", teamEntity.getUserId());
            teamEntityQueryWrapper.eq("dep_id", teamEntity.getDepId());
            userDepartQueryWrapper.eq("audit_type", DelFlagEnum.NO_DEL.getCode());
            teamEntityQueryWrapper.eq("status", SysUserTeamEntity.APPLY);
            Integer toDoJoinDeptCnt = sysUserTeamService.count(teamEntityQueryWrapper);
            if (toDoJoinDeptCnt > 0) throw new ZsxcBootException("您已申请加入此社团 ，请勿重复提交!");
//            QueryWrapper<SysUserTeamEntity> queryWrapper=new QueryWrapper<>();
//            queryWrapper.eq("user_id",teamEntity.getUserId());
//            queryWrapper.eq("audit_type", DelFlagEnum.NO_DEL.getCode());
//            queryWrapper.eq("status",SysUserTeamEntity.AUDIT_PASS);
//            Integer joinDeptCnt=sysUserTeamService.count(queryWrapper);
//            if (joinDeptCnt>=2) throw new ZsxcBootException("最多加入两个社团,请先退出");
            Integer joinTeamDepCnt = sysUserTeamService.getJoinTeamDepCnt(teamEntity.getUserId());
            if (joinTeamDepCnt >= 2) throw new ZsxcBootException("最多加入两个社团,请先退出");

            boolean res = sysUserTeamService.signUpTeam(teamEntity);
            if (res) {
                result.success("申请成功，请等待管理员审核");
                //申请加入社团成功，给社团审核人发送消息提醒
                //查找社团审核人
                if (sysDepart != null) {
                    List<String> reciveUserIds = Arrays.asList(sysDepart.getPersonInCharge().split(","));
                    for (String reciveUserId : reciveUserIds) {
                        sysUserTeamService.sendTodoTaskMes2candidateUser(teamEntity, reciveUserId);
                    }
                }

                //给当前申请人发送加入申请消息提醒
                sysUserTeamService.sendTodoTaskMesCurrentUser(teamEntity, teamEntity.getUserId());
            } else {
                result.error500("申请失败");
            }
        } else {
            QueryWrapper<SysUserTeamEntity> teamEntityQueryWrapper = new QueryWrapper<>();
            teamEntityQueryWrapper.eq("user_id", teamEntity.getUserId());
            teamEntityQueryWrapper.eq("dep_id", teamEntity.getDepId());
            teamEntityQueryWrapper.eq("audit_type", DelFlagEnum.DEL.getCode());
            teamEntityQueryWrapper.eq("status", SysUserTeamEntity.APPLY);
            Integer toDoJoinDeptCnt = sysUserTeamService.count(teamEntityQueryWrapper);
            if (toDoJoinDeptCnt > 0) throw new ZsxcBootException("您已申请退出此社团 ，请勿重复提交!");
            boolean res = sysUserTeamService.signOutTeam(teamEntity);
            if (res) {
                //申请退出社团成功，给社团审核人发送消息提醒
                //查找社团审核人
                if (sysDepart != null) {
                    List<String> reciveUserIds = Arrays.asList(sysDepart.getPersonInCharge().split(","));
                    for (String reciveUserId : reciveUserIds) {
                        sysUserTeamService.sendTodoTaskMes2candidateUser(teamEntity, reciveUserId);
                    }

                    //给当前申请人发送退出申请消息提醒
                    sysUserTeamService.sendTodoTaskMes2CurrentUser(teamEntity, teamEntity.getUserId());
                }
                result.success("申请成功,请等待管理员审核");
            } else {
                result.error500("申请失败");
            }
        }

        return result;
    }


    @PostMapping("auditTeam")
    @AutoLog(value = "社团报名审核")
    @ApiOperation(value = "社团报名审核", notes = "社团报名审核")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> auditHonor(@RequestBody @Valid UserTeamAuditDTO userTeamAuditDTO) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, Object> variables = new HashMap<>();
        variables.put("status", userTeamAuditDTO.getStatus());
        String processInstanceId = "";
        if (userTeamAuditDTO.getAuditType().equals("0")) {   //加入
            processInstanceId = sysUserTeamService.auditHonorProcess(userTeamAuditDTO.getTaskId(),
                    userTeamAuditDTO.getAuditNote(), sysUser.getId(), variables);
        } else {     //退出
            processInstanceId = sysUserTeamService.auditOutProcess(userTeamAuditDTO.getTaskId(),
                    userTeamAuditDTO.getAuditNote(), sysUser.getId(), variables);
        }
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
        return Result.ok("审核成功");
    }


    @GetMapping("teamById")
    @AutoLog(value = "社团审核详情")
    @ApiOperation(value = "社团审核详情", notes = "社团审核详情")
    public Result<SysUserTeamEntity> teamById(@RequestParam String id) {
        Result<SysUserTeamEntity> result = new Result<>();
        SysUserTeamEntity teamEntity = sysUserTeamService.getById(id);
        teamEntity.setStatusName(sysDictService.queryDictTextByKey("stbm_status", String.valueOf(teamEntity.getStatus())));
        teamEntity.setAuditTypeName(sysDictService.queryDictTextByKey("stbm_type", teamEntity.getAuditType()));
        result.setResult(teamEntity);
        result.setSuccess(true);
        return result;
    }


    /**
     * 社团审核待办任务
     *
     * @return
     */
    @GetMapping("taskList")
    @AutoLog(value = "社团审核待办任务")
    @ApiOperation(value = "社团审核待办任务", notes = "社团审核待办任务")
    public Result<?> taskList(SysUserTeamEntity entity,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<SysUserTeamEntity> page = sysUserTeamService.taskPage(entity, pageNo, pageSize);
        return Result.ok(page);
    }

    @GetMapping(value = "/queryById")
    public Result<SysDepart> queryById(String id) {
        Result<SysDepart> result = new Result<>();
        SysDepart sysDepart = sysDepartService.getById(id);
        result.setResult(sysDepart);
        return result;
    }

    @GetMapping("isNotApply")
    @AutoLog(value = "是否达到报名上限")
    @ApiOperation(value = "是否达到报名上限", notes = "是否达到报名上限")
    public Result<Boolean> isNotApply(@RequestParam String userId) {
        Result<Boolean> result = new Result<>();
//        QueryWrapper<SysUserTeamEntity> queryWrapper=new QueryWrapper<>();
//        queryWrapper.eq("user_id",userId);
//        queryWrapper.eq("audit_type", DelFlagEnum.NO_DEL.getCode());
//        queryWrapper.eq("status",SysUserTeamEntity.AUDIT_PASS);
//        Integer joinDeptCnt=sysUserTeamService.count(queryWrapper);
        Integer joinTeamDepCnt = sysUserTeamService.getJoinTeamDepCnt(userId);
        Boolean boo;
        if (joinTeamDepCnt >= 2) {
            boo = true;
        } else {
            boo = false;
        }
        ;
        result.setSuccess(true);
        result.setResult(boo);
        return result;
    }


    @PostMapping("maxApplySubmit")
    @AutoLog(value = "报名上限处理")
    @ApiOperation(value = "报名上限处理", notes = "报名上限处理")
    public Result<?> maxApplySubmit(@RequestBody SysUserTeamEntity teamEntity) {
        Result<?> result = new Result<>();
        SysUserTeamEntity entity = sysUserTeamService.getById(teamEntity.getId());
        entity.setStatus(SysUserTeamEntity.MAX_APPALY);
        sysUserTeamService.updateById(entity);
        result.setSuccess(true);
        return result;
    }


}
