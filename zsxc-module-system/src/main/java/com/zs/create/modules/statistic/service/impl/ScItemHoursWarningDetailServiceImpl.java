package com.zs.create.modules.statistic.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zs.create.base.util.SchoolBussinessUtil;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.mapper.ScItemHoursMapper;
import com.zs.create.modules.item.mapper.ScItemRegistrationMapper;
import com.zs.create.modules.item.service.ScItemHoursService;
import com.zs.create.modules.item.service.ScItemRegistrationService;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.oa.aoumaAndBones.mapper.QdParticipateMapper;
import com.zs.create.modules.oa.aoumaAndBones.service.QdParticipateService;
import com.zs.create.modules.paramdesign.entity.ScWeeksEntity;
import com.zs.create.modules.paramdesign.service.ScWeeksService;
import com.zs.create.modules.statistic.entity.*;
import com.zs.create.modules.statistic.mapper.ScItemHoursWarningDetailMapper;
import com.zs.create.modules.statistic.service.ScItemHoursWarningDetailService;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.entity.SysUserDepart;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import com.zs.create.modules.system.service.*;
import com.zs.create.modules.system.util.DocUtils;
import com.zs.create.modules.system.util.ExcelStyleUtil;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import org.apache.commons.codec.net.URLCodec;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.apache.poi.ss.usermodel.*;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.temporal.ChronoUnit;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 学时预警Service实现层
 * @date 2022-04-13 15:05:40
 * @Version: V1.0
 */
@Service
public class ScItemHoursWarningDetailServiceImpl extends ServiceImpl<ScItemHoursWarningDetailMapper, ScItemHoursWarningDetailEntity> implements ScItemHoursWarningDetailService {

    @Value("${gzhAppid}")
    private String appid;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysDepartService departService;
    @Autowired
    private ISysUserDepartService userDepartService;
    @Autowired
    private SysWeixinUserService sysWeixinUserService;
    @Autowired
    private ScItemRegistrationService scItemRegistrationService;
    @Autowired
    private ScItemHoursService scItemHoursService;
    @Autowired
    private ScWeeksService scWeeksService;
    @Autowired
    private WxMessageSender wxMessageSender;
    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ISysUserRoleService sysUserRoleService;

    @Autowired
    private ISysRoleService sysRoleService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Resource
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private QdParticipateService qdParticipateService;

    @Resource
    private ScItemHoursWarningDetailMapper scItemHoursWarningDetailMapper;

    /**
     * 学时预警任务
     */
    @XxlJob("itemHoursWarningTask")
    public void itemHoursWarningTask() {
        StopWatch sw = StopWatch.createStarted();

        ItemHoursWarningTaskParam param = new ItemHoursWarningTaskParam();

        String jobParam = XxlJobHelper.getJobParam();
        if (!StringUtils.isEmpty(jobParam)) {
            param = JSON.parseObject(jobParam, ItemHoursWarningTaskParam.class);
        }

        if (skip(param)) {
            sw.stop();
            XxlJobHelper.handleSuccess("没到执行周期，跳过本次调度。");
            return;
        }

        Date start = Date.from(param.getToday().minusWeeks(param.getInterval()).atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date end = Date.from(param.getToday().minusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 1. 获取所有报名的用户id
        List<String> itemRegistrationUserIdList = ((ScItemRegistrationMapper) scItemRegistrationService.getBaseMapper()).queryUserId(start, DateUtils.addDays(end, 1));

        // 2. 获取所有获得项目学时的用户id
        List<String> itemHoursUserIdList = ((ScItemHoursMapper) scItemHoursService.getBaseMapper()).queryUserIdByItemHour(start, DateUtils.addDays(end, 1));

        //22.11.09 参加青马/大骨班获得学时也计入统计，不被计入学时预警
        List<String> qdClassUserIdList = ((QdParticipateMapper)qdParticipateService.getBaseMapper()).queryUserId(start,DateUtils.addDays(end, 1));

        // 3. 获取所有用户id
        ScWeeksEntity currentWeek = scWeeksService.getWeekMessage(start);
        int currentYear = SchoolBussinessUtil.getCurrentYear(currentWeek);
        int year = Math.max(currentYear - 3, 2020);
        Wrapper<SysUser> userWrapper = Wrappers.<SysUser>lambdaQuery()
                .eq(SysUser::getType, "S")
                .apply("username NOT LIKE 'YK%'")
                .ge(SysUser::getGrade, year)
                .eq(SysUser::getDelFlag, 0)
                .select(SysUser::getId);
        List<SysUser> userList = userService.list(userWrapper);
        List<String> userIdList = userList.stream()
                .map(SysUser::getId)
                .collect(Collectors.toList());

        // 4. 差集
        userIdList.removeAll(itemRegistrationUserIdList);
        userIdList.removeAll(itemHoursUserIdList);

        userIdList.removeAll(qdClassUserIdList);

        // 5. 学时预警
        List<ScItemHoursWarningDetailEntity> list = new LinkedList<>();
        for (String userId : userIdList) {
            ScItemHoursWarningDetailEntity entity = new ScItemHoursWarningDetailEntity();
            entity.setUserId(userId)
                    .setStartTime(start)
                    .setEndTime(end);
            list.add(entity);
        }
        this.saveBatch(list);

        sw.stop();
        XxlJobHelper.handleSuccess("学时预警定时任务完成，耗时 " + sw.getTime() + "ms");
    }

    /**
     * 学时预警发送消息任务
     */
    @XxlJob("itemHoursWarningSendTask")
    public void itemHoursWarningSendTask() {
        StopWatch sw = StopWatch.createStarted();

        ItemHoursWarningTaskParam param = new ItemHoursWarningTaskParam();

        String jobParam = XxlJobHelper.getJobParam();
        if (!StringUtils.isEmpty(jobParam)) {
            param = JSON.parseObject(jobParam, ItemHoursWarningTaskParam.class);
        }

        if (skip(param)) {
            sw.stop();
            XxlJobHelper.handleSuccess("没到执行周期，跳过本次调度。");
            return;
        }

        LocalDate start = param.getToday().minusWeeks(param.getInterval());
        LocalDate end = param.getToday();
        SimpleDateFormat sf = new SimpleDateFormat("MM月dd日");
        Instant startDate = start.atStartOfDay(ZoneId.systemDefault()).toInstant();
        Instant endDate = end.atStartOfDay(ZoneId.systemDefault()).toInstant();
        String startNY = sf.format(Date.from(startDate));
        String endNY = sf.format(Date.from(endDate));

        // 1. 预警学生
        Wrapper<ScItemHoursWarningDetailEntity> itemHoursWarningWrapper = Wrappers.<ScItemHoursWarningDetailEntity>lambdaQuery()
                .ge(ScItemHoursWarningDetailEntity::getStartTime, start)
                .lt(ScItemHoursWarningDetailEntity::getEndTime, end)
                .select(ScItemHoursWarningDetailEntity::getUserId);
        List<ScItemHoursWarningDetailEntity> list = this.list(itemHoursWarningWrapper);
        List<String> studentIdList = list.stream()
                .map(ScItemHoursWarningDetailEntity::getUserId)
                .distinct()
                .collect(Collectors.toList());

        // 2. 学院负责人
        Map<String, List<String>> collegePersonInChargeMap = new HashMap<>();
        if (!studentIdList.isEmpty()) {
            Collection<SysUser> studentUserList = userService.listByIds(studentIdList);
            Map<String, List<SysUser>> collegeDepUserMap = studentUserList.stream()
                    .filter(user -> !StringUtils.isEmpty(user.getCollegeId()))
                    .collect(Collectors.groupingBy(SysUser::getCollegeId));
            List<String> collegeIdList = studentUserList.stream()
                    .map(SysUser::getCollegeId)
                    .collect(Collectors.toList());
            Collection<SysDepart> collegeDepList = Collections.emptyList();
            if (!collegeIdList.isEmpty()) {
                collegeDepList = departService.listByIds(collegeIdList);
            }
            for (SysDepart dep : collegeDepList) {
                String personInCharge = dep.getPersonInCharge();
                if (StringUtils.isEmpty(personInCharge)) {
                    continue;
                }

                String depId = dep.getId();
                List<String> userIdList = collegeDepUserMap.get(depId)
                        .stream()
                        .map(SysUser::getId)
                        .distinct()
                        .collect(Collectors.toList());
                if (userIdList.isEmpty()) {
                    continue;
                }
                for (String person : personInCharge.split(",")) {
                    List<String> ids = userIdList;
                    if (collegePersonInChargeMap.containsKey(person)) {
                        Set<String> set = new LinkedHashSet<>();
                        set.addAll(collegePersonInChargeMap.get(person));
                        set.addAll(userIdList);
                        ids = new ArrayList<>(set);
                    }
                    collegePersonInChargeMap.put(person, ids);
                }
            }
        }

        // 3. 班主任
        Map<String, List<String>> headmasterMap = new HashMap<>();
        if (!studentIdList.isEmpty()) {
            Wrapper<SysUserDepart> userDepartWrapper = Wrappers.<SysUserDepart>lambdaQuery()
                    .in(SysUserDepart::getUserId, studentIdList);
            List<SysUserDepart> userDepartList = userDepartService.list(userDepartWrapper);
            Map<String, List<SysUserDepart>> depUserMap = userDepartList.stream()
                    .collect(Collectors.groupingBy(SysUserDepart::getDepId));
            List<SysDepart> depList = Collections.emptyList();
            if (!depUserMap.isEmpty()) {
                Wrapper<SysDepart> depWrapper = Wrappers.<SysDepart>lambdaQuery()
                        .eq(SysDepart::getOrgType, "4")
                        .in(SysDepart::getId, depUserMap.keySet())
                        .select(SysDepart::getId, SysDepart::getHeadmaster);
                depList = departService.list(depWrapper);
            }
            for (SysDepart dep : depList) {
                String headmaster = dep.getHeadmaster();
                if (StringUtils.isEmpty(headmaster)) {
                    continue;
                }

                String depId = dep.getId();
                List<String> userIdList = depUserMap.get(depId)
                        .stream()
                        .map(SysUserDepart::getUserId)
                        .distinct()
                        .collect(Collectors.toList());
                if (userIdList.isEmpty()) {
                    continue;
                }
                for (String person : headmaster.split(",")) {
                    List<String> ids = userIdList;
                    if (headmasterMap.containsKey(person)) {
                        Set<String> set = new LinkedHashSet<>();
                        set.addAll(headmasterMap.get(person));
                        set.addAll(userIdList);
                        ids = new ArrayList<>(set);
                    }
                    headmasterMap.put(person, ids);
                }
            }
        }

        List<WxCommonMsgInfo> wxCommonMsgInfoList = new LinkedList<>();
        List<ScItemHoursWarningMessageInfo> messageInfoList = new ArrayList<>();

        // 学生预警
        if (!studentIdList.isEmpty()) {
            List<WxUserDTO> studentList = sysWeixinUserService.findUserOpenIds(studentIdList, appid);
            for (WxUserDTO wxUserDTO : studentList) {
                String theme = wxUserDTO.getRealname() + "/" + wxUserDTO.getUsername() + ",您有一条新的提醒";
                WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                        .setUseCommonTemplate(Boolean.TRUE)
                        .setTheme(theme)
                        .setTitle("学时提示")
                        .setUserId(wxUserDTO.getUserId())
                        .setCreateDate(new Date())
                        .setContent(wxUserDTO.getRealname() + " 同学你好！你已连续"+ param.getInterval() +"周未报名且未获得第二课堂学时。学习、科研虽忙，但也要劳逸结合哦，快来一起参加第二课堂活动吧！（学时提示周期："+startNY+"至"+endNY + "）")
                        .setOpenId(wxUserDTO.getOpenId())
                        .setRemark("青春科大智慧团学综合信息平台");
                wxCommonMsgInfoList.add(wxCommonMsgInfo);
            }
        }

        // 学院负责人预警
        if (!collegePersonInChargeMap.isEmpty()) {
            List<WxUserDTO> collegePersonList = sysWeixinUserService.findUserOpenIds(collegePersonInChargeMap.keySet(), appid);
            for (WxUserDTO wxUserDTO : collegePersonList) {
                String id = SnowIdUtils.uniqueLongHex();
                String userId = wxUserDTO.getUserId();
                List<String> userIdList = collegePersonInChargeMap.get(userId);
                String theme = wxUserDTO.getRealname() + "/" + wxUserDTO.getUsername() + ",您有一条新的提醒";
                String role = "role_xyfzr";
                String url = "pagesA/creaditHours/creaditHours?userId=" + userId + "&start=" + start + "&end=" + end + "&role=" + role + "&msgId=" + id;
                String content = wxUserDTO.getRealname() + " 老师您好！您所管理的学院共有" + userIdList.size() + "名学生连续" + param.getInterval() +
                        "周未报名且未获得学时。请重点关注学时提示名单中的低年级本科生。您辛苦了！（学时提示周期：" + startNY + "至" + endNY + "）";
                WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                        .setUseCommonTemplate(Boolean.TRUE)
                        .setTheme(theme)
                        .setTitle("学时提示")
                        .setUserId(userId)
                        .setCreateDate(new Date())
                        .setContent(content)
                        .setOpenId(wxUserDTO.getOpenId())
                        .setRemark("请转至电脑端学生学时提示查询统计页面查看")
                        .setMiniAppUrl(url);
                wxCommonMsgInfoList.add(wxCommonMsgInfo);
                ScItemHoursWarningMessageInfo info= new ScItemHoursWarningMessageInfo();
                info.setId(id).setContent(content).setCreateTime(new Date());
                messageInfoList.add(info);
            }
        }

        // 班主任预警
        if (!headmasterMap.isEmpty()) {
            List<WxUserDTO> teacherList = sysWeixinUserService.findUserOpenIds(headmasterMap.keySet(), appid);
            for (WxUserDTO wxUserDTO : teacherList) {
                String id = SnowIdUtils.uniqueLongHex();
                String userId = wxUserDTO.getUserId();
                List<String> userIdList = headmasterMap.get(userId);
                String theme = wxUserDTO.getRealname() + "/" + wxUserDTO.getUsername() + ",您有一条新的提醒";
                String role = "role_bzr";
                String url = "pagesA/creaditHours/creaditHours?userId=" + userId + "&start=" + start + "&end=" + end + "&role=" + role + "&msgId=" + id;
                String content = wxUserDTO.getRealname() + " 老师您好！您所管理的班级共有" + userIdList.size() + "名学生连续" + param.getInterval()
                        + "周未报名且未获得学时。请重点关注学时提示名单中的低年级本科生。您辛苦了！（学时提示周期：" + startNY + "至" + endNY + "）";
                WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                        .setUseCommonTemplate(Boolean.TRUE)
                        .setTheme(theme)
                        .setTitle("学时提示")
                        .setUserId(userId)
                        .setCreateDate(new Date())
                        .setContent(content)
                        .setOpenId(wxUserDTO.getOpenId())
                        .setRemark("请转至电脑端学生学时提示查询统计页面查看")
                        .setMiniAppUrl(url);
                wxCommonMsgInfoList.add(wxCommonMsgInfo);
                ScItemHoursWarningMessageInfo info= new ScItemHoursWarningMessageInfo();
                info.setId(id).setContent(content).setCreateTime(new Date());
                messageInfoList.add(info);
            }
        }

        for (WxCommonMsgInfo wxCommonMsgInfo : wxCommonMsgInfoList) {
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
        if (messageInfoList.size() > 0){
            scItemHoursWarningDetailMapper.saveWaringMesageInfo(messageInfoList);
        }
        sw.stop();
        XxlJobHelper.handleSuccess("学时预警发送消息定时任务完成，耗时 " + sw.getTime() + "ms");
    }

    /**
     * 判断是否跳过执行
     */
    private boolean skip(ItemHoursWarningTaskParam param) {
        if (param.getDate() != null) {
            return false;
        }

        // 开学时间
        ScWeeksEntity currentWeek = scWeeksService.getCurrentWeek();
        //判断当前时间是否在假期时间之内，在假期时间之内，判断当前天数和放假时间之间的天数，
        // 如果小于7，就结束本次判断，进行是否是第四周的判断；
        // 不小于7，且当前时间在假期中，就结束方法，返回true，让学时预警消息发送结束
        Date jqks = currentWeek.getJqks();
        Date date = new Date();
        if (date.after(jqks)){
            long diff = date.getTime() - jqks.getTime();
            TimeUnit time = TimeUnit.DAYS;
            long diffrence = time.convert(diff, TimeUnit.MILLISECONDS);
            if (diffrence > 7){
                return true;
            }
        }
        LocalDate startDate = LocalDateTime.ofInstant(currentWeek.getKsrq().toInstant(), ZoneId.systemDefault()).toLocalDate();
        // 调整到周一
        startDate = startDate.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));

        LocalDate today = LocalDate.now();
        if (today.isBefore(startDate)) {
            return true;
        }

        long daysBetween = ChronoUnit.DAYS.between(startDate, today);
        long weeksBetween = daysBetween / 7;
        if (weeksBetween == 0) {
            // 跳过第一周
            return true;
        }
        return weeksBetween % param.getInterval() != 0;
    }

    @Override
    public IPage<ScWarningVo> pageList(Page<ScWarningVo> page, ScWarningVo dto) {
        List<String> depIds = new ArrayList<>();
        IPage<ScWarningVo> pageList = new Page<>();
        if (dto.getDepId()!= null && !"".equals(dto.getDepId())) {
            List<SysDepart> deps = departService.getChildrenListByDeptId(dto.getDepId());
            depIds = deps.stream().map(SysDepart::getId).collect(Collectors.toList());
        } else {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            //判断是否是团委部门负责人
            Boolean twFlag = sysRoleService.getTwCharge(user.getId());
            if (!twFlag) {
                //判断是否是学院负责人
                List<String> collgeIds = sysDepartService.getCollegeByUserId(user.getId());
                //判断是否是班主任
                List<String> headmaster = sysDepartService.getHeadmasterDepIdsByUserId(user.getId());
                collgeIds.addAll(headmaster);
                if (collgeIds.isEmpty()) {
                    return page;
                }
                List<String> pidsList = sysDepartService.listByIds(collgeIds).stream().map(SysDepart::getPids).collect(Collectors.toList());
                depIds = sysDepartMapper.getDepidsByLikePids(pidsList);
            }
        }
        pageList = baseMapper.pageList(page, dto,depIds);
        if (dto.getTimeInnerDaoList().size()>0) {
            List<ScWarningVo> records = pageList.getRecords();
            records.forEach(p ->{
                p.setCreateTime(p.getMoreTime());
            });
            pageList.setRecords(records);
            return pageList;
        }
        return pageList;
    }

    @Override
    public List<ScItemHoursWarningDetailEntity> getListByUserIds(Set<String> userIds) {
        return baseMapper.getListByUserIds(userIds);
    }

    @Override
    public List<ScItemHoursWarningDetailEntity> getToList() {
        return baseMapper.getToList();
    }

    @Override
    public List<ScItemHoursWarningDetailDto> queryDetailList(String userId, LocalDate start, LocalDate end, String role) {
        if (StringUtils.isEmpty(role)) {
            return Collections.emptyList();
        }

        Wrapper<ScItemHoursWarningDetailEntity> itemHoursWarningWrapper = Wrappers.<ScItemHoursWarningDetailEntity>lambdaQuery()
                .ge(start != null, ScItemHoursWarningDetailEntity::getStartTime, start)
                .lt(end != null, ScItemHoursWarningDetailEntity::getEndTime, end)
                .select(ScItemHoursWarningDetailEntity::getUserId);
        List<ScItemHoursWarningDetailEntity> list = this.list(itemHoursWarningWrapper);
        List<String> studentIdList = list.stream()
                .map(ScItemHoursWarningDetailEntity::getUserId)
                .collect(Collectors.toList());
        if (studentIdList.isEmpty()) {
            return Collections.emptyList();
        }

        Set<String> userIdSet = new LinkedHashSet<>();
        if ("role_bzr".equals(role)) {
            Wrapper<SysUserDepart> userDepartWrapper = Wrappers.<SysUserDepart>lambdaQuery()
                    .in(SysUserDepart::getUserId, studentIdList);
            List<SysUserDepart> userDepartList = userDepartService.list(userDepartWrapper);
            Map<String, List<SysUserDepart>> depUserMap = userDepartList.stream()
                    .collect(Collectors.groupingBy(SysUserDepart::getDepId));
            List<SysDepart> depList = Collections.emptyList();
            if (!depUserMap.isEmpty()) {
                Wrapper<SysDepart> depWrapper = Wrappers.<SysDepart>lambdaQuery()
                        .eq(SysDepart::getOrgType, "4")
                        .in(SysDepart::getId, depUserMap.keySet())
                        .select(SysDepart::getId, SysDepart::getHeadmaster);
                depList = departService.list(depWrapper);
            }
            for (SysDepart dep : depList) {
                String headmaster = dep.getHeadmaster();
                if (StringUtils.isEmpty(headmaster)) {
                    continue;
                }
                String depId = dep.getId();
                List<String> userIdList = depUserMap.get(depId)
                        .stream()
                        .map(SysUserDepart::getUserId)
                        .distinct()
                        .collect(Collectors.toList());
                for (String person : headmaster.split(",")) {
                    if (person.equals(userId)) {
                        userIdSet.addAll(userIdList);
                        break;
                    }
                }
            }
        }else if ("role_xyfzr".equals(role)) {
            Collection<SysUser> studentUserList = userService.listByIds(studentIdList);
            Map<String, List<SysUser>> collegeDepUserMap = studentUserList.stream()
                    .filter(user -> !StringUtils.isEmpty(user.getCollegeId()))
                    .collect(Collectors.groupingBy(SysUser::getCollegeId));
            Collection<SysDepart> collegeDepList = Collections.emptyList();
            if (!collegeDepUserMap.isEmpty()) {
                List<String> collegeIdList = studentUserList.stream()
                        .map(SysUser::getCollegeId)
                        .collect(Collectors.toList());
                collegeDepList = departService.listByIds(collegeIdList);
            }
            for (SysDepart dep : collegeDepList) {
                String personInCharge = dep.getPersonInCharge();
                if (StringUtils.isEmpty(personInCharge)) {
                    continue;
                }
                String depId = dep.getId();
                List<String> userIdList = collegeDepUserMap.get(depId)
                        .stream()
                        .map(SysUser::getId)
                        .distinct()
                        .collect(Collectors.toList());
                for (String person : personInCharge.split(",")) {
                    if (person.equals(userId)) {
                        userIdSet.addAll(userIdList);
                        break;
                    }
                }
            }
        }

        if (userIdSet.isEmpty()) {
            return Collections.emptyList();
        }

        List<ScItemHoursWarningDetailDto> result = new LinkedList<>();

        Collection<SysUser> sysUserList = userService.listByIds(userIdSet);
        Map<String, SysUser> sysUserMap = sysUserList.stream()
                .collect(Collectors.toMap(SysUser::getId, u -> u));
        for (String user : userIdSet) {
            ScItemHoursWarningDetailDto itemHoursWarningDetailDto = new ScItemHoursWarningDetailDto();
            itemHoursWarningDetailDto.setUserId(user);

            SysUser sysUser = sysUserMap.get(user);
            if (sysUser != null) {
                itemHoursWarningDetailDto.setUserName(sysUser.getRealname());
                itemHoursWarningDetailDto.setClasses(sysUser.getClasses());
                itemHoursWarningDetailDto.setGrade(sysUser.getGrade());
            }

            result.add(itemHoursWarningDetailDto);
        }

        //处理排序
        //1.新建两个集合,一个处理"PB"排序,一个处理其他排序
        //PB集合
        List<ScItemHoursWarningDetailDto> scItemHoursWarningDetailDtoPB = new ArrayList<>();
        //其他集合
        List<ScItemHoursWarningDetailDto> scItemHoursWarningDetailDtoQt = new ArrayList<>();

        for (ScItemHoursWarningDetailDto scItemHoursWarningDetailDto : result) {
            if ("PB".equals(scItemHoursWarningDetailDto.getUserId().substring(0, 2))) {
                scItemHoursWarningDetailDtoPB.add(scItemHoursWarningDetailDto);
            } else {
                scItemHoursWarningDetailDtoQt.add(scItemHoursWarningDetailDto);
            }
        }
        //2.设计排序规则

        //按照年级升序
        Comparator<ScItemHoursWarningDetailDto> byGradeASC = Comparator.comparing(ScItemHoursWarningDetailDto::getGrade);

        //按照班级升序
        Comparator<ScItemHoursWarningDetailDto> byClassesASC = Comparator.comparing((o) -> o.getClasses().substring(o.getClasses().length() - 3, o.getClasses().length() - 1));

        //按照学号升序
        Comparator<ScItemHoursWarningDetailDto> byUserIdASC = Comparator.comparing(ScItemHoursWarningDetailDto::getUserId);

        //联合排序
        Comparator<ScItemHoursWarningDetailDto> finalComparator = byGradeASC.thenComparing(byClassesASC).thenComparing(byUserIdASC);

        if(!CollectionUtils.isEmpty(scItemHoursWarningDetailDtoPB)){
            //3.进行排序
            scItemHoursWarningDetailDtoPB.sort(finalComparator);
        }

        if (scItemHoursWarningDetailDtoQt.size() > 0){
            scItemHoursWarningDetailDtoQt.sort(byUserIdASC);
            scItemHoursWarningDetailDtoPB.addAll(scItemHoursWarningDetailDtoQt);
        }
        return scItemHoursWarningDetailDtoPB;
    }

    @Override
    public ResponseEntity<?> exportXls(HttpServletResponse response, String userId, LocalDate start, LocalDate end, String role,String msgId) throws Exception {
        List<ExcelExportEntity> entity = new ArrayList();
        String warningMsg = this.getWarningMsg(msgId).getResult();
        warningMsg = "    "+warningMsg;
        warningMsg = new StringBuilder(warningMsg).insert(48,"<br>").toString();

        ExcelExportEntity entity1 = new ExcelExportEntity("学号", "userId");
        entity1.setWidth(28);
        entity.add(entity1);
        entity1 = new ExcelExportEntity("姓名", "userName");
        entity1.setWidth(28);
        entity.add(entity1);
        entity1 = new ExcelExportEntity("班级", "classes");
        entity1.setWidth(28);
        entity.add(entity1);
        Collection dataList = new ArrayList();
        List<ScItemHoursWarningDetailDto> scItemHoursWarningDetailDtos = queryDetailList(userId, start, end, role);
        for (ScItemHoursWarningDetailDto scItemHoursWarningDetailDto : scItemHoursWarningDetailDtos) {
            Map<String, Object> mapUser = new HashMap<>();
            mapUser.put("userId", scItemHoursWarningDetailDto.getUserId());
            mapUser.put("userName", scItemHoursWarningDetailDto.getUserName());
            mapUser.put("classes", scItemHoursWarningDetailDto.getClasses());
            dataList.add(mapUser);
        }

        ExportParams ep = new ExportParams(
                "学时提示详情"
                , "");
        ep.setStyle(ExcelStyleUtil.class);
        //换行准备
        warningMsg=warningMsg.replace("<br>","EXCELROW");
        //设置第二标题
        ep.setSecondTitle(warningMsg);
        //第二标题高度
        ep.setSecondTitleHeight((short)15);
        Workbook workbook = ExcelExportUtil.exportExcel(ep, entity, dataList);
        /**设置单元格格式为文本格式*/
        CellStyle cellStyle = workbook.createCellStyle();
        DataFormat dataFormat = workbook.createDataFormat();
        cellStyle.setDataFormat(dataFormat.getFormat("@"));
        Sheet sheet = workbook.getSheetAt(0);
        sheet.setDefaultColumnStyle(0, cellStyle);//设置首列格式为"文本"
        sheet.setPrintGridlines(true);
        //第二标题换行
        CellStyle cellStyle1 = workbook.createCellStyle();
        cellStyle1.setWrapText(true);
        Cell cell = sheet.getRow(1).getCell(0);
        String temp = warningMsg.replaceAll("EXCELROW","\n");
        cell.setCellValue(temp);
        cell.setCellStyle(cellStyle1);

        String filename = "未获得学时学生详情.pdf";
        filename = new URLCodec().encode(filename, "UTF-8");
        ByteArrayOutputStream bos = new ByteArrayOutputStream();
        workbook.write(bos);
        byte[] barray = bos.toByteArray();
        InputStream in = new ByteArrayInputStream(barray);
        byte[] bytes = DocUtils.convertExcelToPdf(in);

        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .body(bytes);
    }

    @Override
    public List<ScWarningPeriods> periodsList() {
        List<ScWarningPeriods> periods = new ArrayList<>();
        List<ScItemHoursWarningDetailEntity> list = baseMapper.getTimeList();
        SimpleDateFormat format = new SimpleDateFormat("MM月dd日");
        int i = 1;
        String xq = "";
        for (ScItemHoursWarningDetailEntity entity : list) {
            ScWeeksEntity scWeeksEntity = scWeeksService.getWeekMessage(entity.getStartTime());
            if ( !xq.equals(scWeeksEntity.getXq())) {
                i =1;
            }
            ScWarningPeriods data = new ScWarningPeriods();
            data.setId(entity.getId());
            data.setPeriods(i);
            StringBuffer bf = new StringBuffer();
            bf.append(scWeeksEntity.getXn());
            if ("1".equals(scWeeksEntity.getXq())) {
                bf.append("秋季第");
            } else {
                bf.append("春季第");
            }
            bf.append(i).append("期").append("（");
            bf.append(format.format(entity.getStartTime())).append("-")
                    .append(format.format(entity.getEndTime()))
                    .append("）");
            data.setName(bf.toString());
            data.setSt(entity.getStartTime());
            data.setEt(entity.getEndTime());
            periods.add(data);
            i++;
            xq =scWeeksEntity.getXq();
        }
        return periods;
    }

    @Override
    public List<ScWarningVo> exportList(ScWarningVo dto) {
        List<String> depIds = new ArrayList<>();
        List<ScWarningVo> pageList = new ArrayList<>();
        if (dto.getDepId()!= null && !"".equals(dto.getDepId())) {
            List<SysDepart> deps = departService.getChildrenListByDeptId(dto.getDepId());
            depIds = deps.stream().map(SysDepart::getId).collect(Collectors.toList());
        } else {
            LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            //判断是否是团委部门负责人
            Boolean twFlag = sysRoleService.getTwCharge(user.getId());
            if (!twFlag) {
                //判断是否是学院负责人
                List<String> collgeIds = sysDepartService.getCollegeByUserId(user.getId());
                //判断是否是班主任
                List<String> headmaster = sysDepartService.getHeadmasterDepIdsByUserId(user.getId());
                collgeIds.addAll(headmaster);
                if (collgeIds.isEmpty()) {
                    return pageList;
                }
                List<String> pidsList = sysDepartService.listByIds(collgeIds).stream().map(SysDepart::getPids).collect(Collectors.toList());
                depIds = sysDepartMapper.getDepidsByLikePids(pidsList);
            }
        }
        pageList = baseMapper.exportList(dto,depIds);
        if (dto.getTimeInnerDaoList().size()>0) {
            pageList.forEach(p ->{
                p.setCreateTime(p.getMoreTime());
            });
            return pageList;
        }
        return pageList;
    }

    @Override
    public List<ScWarningPeriods> choseNowPeriodsList(List<ScWarningPeriods> list) {
        Date ksrq = scWeeksService.getCurrentWeek().getKsrq();
        List<ScWarningPeriods> nowList = new ArrayList<>();
        for (ScWarningPeriods sw: list) {
            boolean before = sw.getSt().after(ksrq);
            if (before){
                nowList.add(sw);
            }
        }
        return nowList;
    }

    @Override
    public Result<String> getWarningMsg(String msgId) {
        Result<String> result = new Result<>();
        if (StringUtils.isEmpty(msgId)) throw new ZsxcBootException("参数错误");
        String warningMsg = this.getBaseMapper().getWarningMsg(msgId);
        if (oConvertUtils.isEmpty(warningMsg)){
            warningMsg = "";
        }
        result.setResult(warningMsg);
        return result;
    }
}
