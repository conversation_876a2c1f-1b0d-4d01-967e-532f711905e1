package com.zs.create.base.enums;

public enum PracticeStatusEnum {

    data_null("200",""),
    ZC_TYPE("-2","暂存"),
    CH_TYPE("-1","撤回"),
    BM_SQZ("1","报名申请中"),
    BM_YSH("2","报名已审核"),
    BM_BH("3","报名驳回"),
    BM_GSZ("4","报名公示中"),
    BM_YBM("5","已报名"),
    JX_SQZ("6","结项申请中"),
    JX_BH("7","结项驳回"),
    JX_YSH("8","结项已审核"),
    JX_GSZ("9","结项公示中"),
    JX_GSJS("10","结项公示已结束"),
    YHX("11","已结项");

    private String code;
    private String desc;

    PracticeStatusEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static PracticeStatusEnum codeOf(String code){
        for (PracticeStatusEnum practiceStatusEnum : values()) {
            if (practiceStatusEnum.getCode().equals(code)) {
                return practiceStatusEnum;
            }
        }
        return PracticeStatusEnum.data_null;
    }
}
