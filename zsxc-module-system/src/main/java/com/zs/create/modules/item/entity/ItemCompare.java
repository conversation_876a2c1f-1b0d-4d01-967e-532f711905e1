package com.zs.create.modules.item.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@Accessors(chain = true)
public class ItemCompare  implements Serializable {
    /**
     * 主键
     */
    @ApiModelProperty(value = "主键")
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String itemName;
    /**
     * 项目模块 数据字段中配置
     */
    @ApiModelProperty(value = "项目模块")
    private String module;
    /**
     * 项目形式 数据字典中配置
     */
    @ApiModelProperty(value = "项目形式")
    private String form;
    /**
     * 项目级别 见字典
     */
    @ApiModelProperty(value = "项目级别")
    private String activityLevel;
    /**
     * 项目标签（用于项目推荐）
     */
    @NotBlank(message = "项目标签不能为空")
    @Length(max=256, message = "项目标签字段过长")
    private String itemLable;

    /**
     * 总赋予时长
     */
    @ApiModelProperty(value = "时长")
    private BigDecimal serviceHour=BigDecimal.ZERO;
    /**
     * 有效时长
     */
    @ApiModelProperty(value = "有效时长")
    private BigDecimal validHour=BigDecimal.ZERO;

    /**
     * 联系人
     */
    @ApiModelProperty(value = "联系人")
    private String linkMan;
    /**
     * 联系人电话
     */
    @ApiModelProperty(value = "联系人电话")
    private String tel;
    /**
     * 主办单位
     */
    @ApiModelProperty(value = "主办单位")
    private String sponsor;
    /**
     * 校外主办单位
     */
    @ApiModelProperty(value = "校外主办单位")
    private String ewSponsor;
    /**
     * 承办单位
     */
    @ApiModelProperty(value = "承办单位")
    private String organizer;
    /**
     * 项目图片
     */
    @ApiModelProperty(value = "项目图片")
    private String pic;
    /**
     * 项目内容
     */
    @ApiModelProperty(value = "项目内容")
    private String baseContent;
    /**
     * 项目开始时间
     */
    @ApiModelProperty(value = "项目开始时间")
    private Date st;
    /**
     * 项目结束时间
     */
    @ApiModelProperty(value = "项目结束时间")
    private Date et;
    /**
     * 报名范围-部门
     */
    @ApiModelProperty(value = "报名范围-部门")
    private String rangeDeptIds;
    /**
     * 报名范围-年级
     */
    @ApiModelProperty(value = "报名范围-年级")
    private String nj;
    /**
     * 报名范围-学期
     */
    @ApiModelProperty(value = "报名范围-学期")
    private String xq;
    /**
     * 个人报名人数限制
     */
    @ApiModelProperty(value = "个人报名人数限制")
    private Integer peopleNum;
    /**
     * 报名范围  0:全体人员 1：自定义范围
     */
    @ApiModelProperty(value = "报名范围  0:全体人员 1：自定义范围 2：组织方 3：人员导入")
    private String applyRange;

    /**
     * 策划书附件
     */
    @ApiModelProperty(value = "策划书附件")
    @Length(max=128 ,message = "策划书附件地址过长")
    private String planningAtta;

    @ApiModelProperty(value = "报名开始时间")
    private Date applySt;

    @ApiModelProperty(value = "报名结束时间")
    private Date applyEt;

    @ApiModelProperty(value = "附件类型")
    @Length(max=64 ,message = "附件类型字段过长")
    private String attaType;
    /**
     * 组织方式构想
     */
    @ApiModelProperty(value = "组织方式构想")
    private String conceive;
    /**
     * 经费金额
     */
    @ApiModelProperty(value = "经费金额")
    private BigDecimal outlayMoney=BigDecimal.ZERO;
}
