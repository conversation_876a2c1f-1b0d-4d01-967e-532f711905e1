<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.weixin.mapper.SysWeixinMenuMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.weixin.entity.SysWeixinMenuEntity" id="sysWeixinMenuMap">
        <result property="id" column="id"/>
        <result property="menuName" column="menu_name"/>
        <result property="menuModule" column="menu_module"/>
        <result property="menuType" column="menu_type"/>
        <result property="menuIcon" column="menu_icon"/>
        <result property="sortOrder" column="sort_order"/>
        <result property="isHidden" column="is_hidden"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>