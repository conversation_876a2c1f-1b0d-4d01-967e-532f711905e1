package com.zs.create.modules.score.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zs.create.config.SerializerBigDecimal;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 成绩单
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-11 15:15:14
 */
@Data
@TableName("sc_score_report")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_score_report对象", description="成绩单")
public class ScScoreReportEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	@TableId
	private String id;
	/**
	 * 学生id
	 */
	private String userId;

	/**
	 * 德模块-成绩
	 */
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	private BigDecimal moduleDScore;

	/**
	 * 智模块-成绩
	 */

	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	private BigDecimal moduleZScore;
	/**
	 * 体模块-成绩
	 */
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	private BigDecimal moduleTScore;
	/**
	 * 美模块-成绩
	 */
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	private BigDecimal moduleMScore;
	/**
	 * 美模块-成绩
	 */
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	@JsonSerialize(using = SerializerBigDecimal.class)
	private BigDecimal moduleLScore;
	/**
	 * 创建者
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 数据修改者
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

	private String grade;


	//同年级总人数
	@TableField(exist = false)
	private Integer sumScoreNum=0;

	//同年级大于自己的人数
	@TableField(exist = false)
	private Integer maxScoreNum=0;


}
