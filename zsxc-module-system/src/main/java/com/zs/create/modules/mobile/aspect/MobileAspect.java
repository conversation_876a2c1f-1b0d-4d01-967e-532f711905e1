package com.zs.create.modules.mobile.aspect;

import com.zs.create.modules.system.service.SysScientificqiValueService;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2022/3/11
 */
@Aspect
@Component
public class MobileAspect {

    @Autowired
    private SysScientificqiValueService sysScientificqiValueService;

    @Pointcut("execution(public * com.zs.create.modules.mobile..*.*Controller.*(..))")
    public void pointcut() {
    }

    @After("pointcut()")
    public void after() {
        sysScientificqiValueService.firstLogin();
    }
}
