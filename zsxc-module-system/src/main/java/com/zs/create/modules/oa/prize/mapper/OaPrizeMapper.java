package com.zs.create.modules.oa.prize.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.oa.prize.entity.OaPrizeEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 奖项Mapper层
 *
 * <AUTHOR>
 * @email null
 * @date 2021-01-16 14:41:53
 * @Version: V1.0
 */
public interface OaPrizeMapper extends BaseMapper<OaPrizeEntity> {

    List<OaPrizeEntity> myInPublicPrize(Page<OaPrizeEntity> page, @Param("userId") String userId);

    List<OaPrizeEntity> myNotInPublicPrize(Page<OaPrizeEntity> page, @Param("userId") String userId);

    /**
     * 查询奖项结束时间在目标时间段内的所有奖项
     * @param todayStart
     * @param todayEnd
     * @return
     */
    List<OaPrizeEntity> qryByEndTime(@Param("todayStart")String todayStart, @Param("todayEnd")String todayEnd);
}
