package com.zs.create.modules.third.scYouthLearning.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 青年大学习
 *
 * <AUTHOR> @email
 * @date 2022-05-09 16:19:37
 */
@Data
@TableName("sc_youth_learning")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "sc_youth_learning对象", description = "青年大学习")
public class ScYouthLearningEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String username;
    /**
     * 性别:1男,2女,3未知
     */
    @ApiModelProperty(value = "性别:1男,2女,3未知")
    private String gender;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     *
     */
    @ApiModelProperty(value = "直属高校")
    private String university;
    /**
     *
     */
    @ApiModelProperty(value = "学校")
    private String school;
    /**
     *
     */
    @ApiModelProperty(value = "学院")
    private String college;
    /**
     *
     */
    @ApiModelProperty(value = "班级")
    private String classes;



    /**
     * 分数
     */
    @ApiModelProperty(value = "分数")
    private String score;
    /**
     * 学习时间
     */
    @ApiModelProperty(value = "学习时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studytime;
    /**
     * 期数中文
     */
    @ApiModelProperty(value = "期数中文")
    private String qishu;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 更新时间
     */
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 更新人
     */
    @ApiModelProperty(value = "更新人")
    private String updateBy;
    /**
     * 0-未转化,  1-已转化
     */
    @ApiModelProperty(value = "0-未转化,1-已转化")
    private Integer dataType;

    @TableField(exist = false)
    @ApiModelProperty(value = "0-未转化,1-已转化")
    private String dataTypeName;

     /**
     * 学号
     */
    @ApiModelProperty(value = "学号")
    private String studentCode;

    /**
     * 学院id
     */
    @ApiModelProperty(value = "学院id")
    private String collegeId;

    /**
     * 班级id
     */
    @ApiModelProperty(value = "班级id")
    private String classesId;

   /**
     * 学校id
     */
    @ApiModelProperty(value = "学校id")
    private String schoolId;

    /**
     * 季数
     */
    @ApiModelProperty(value = "季数")
    private String jishu;

    /**
     * 年度
     */
    @ApiModelProperty(value = "年度")
    private String years;

    /**
     * 期数数字
     */
    @ApiModelProperty(value = "期数数字")
    private String periods;

    /**
     * 期数日期组合
     */
    @ApiModelProperty(value = "期数日期组合")
    private String times;


    @TableField(exist = false)
    private String level1;

    @TableField(exist = false)
    private String level2;

    @TableField(exist = false)
    private String level3;

    @TableField(exist = false)
    private String level4;


    @TableField(exist = false)
    @ApiModelProperty(value = "累计学习次数")
    private Integer total;

    @TableField(exist = false)
    @ApiModelProperty(value = "未转化原因")
    private String reason;

    @TableField(exist = false)
    @ApiModelProperty(value = "导入日期")
    private String importTime;

    @ApiModelProperty(value = "微信id")
    private String openid;

    @TableField(exist = false)
    @ApiModelProperty(value = "处理得到的学院")
    private String collegeTemp;

    @TableField(exist = false)
    @ApiModelProperty(value = "处理得到的班级")
    private String classesTemp;

}
