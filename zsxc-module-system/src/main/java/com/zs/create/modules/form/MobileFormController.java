package com.zs.create.modules.form;

import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.form.entity.MobileForm;
import com.zs.create.modules.form.service.IMobileFormService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @Description 移动端表单Controller层
 * <AUTHOR>
 * @Date 2019/10/12 9:16
 * @Version 1.0
 **/
@Slf4j
@RestController
@RequestMapping("/mobile/form")
@Api(tags = "移动端表单")
public class MobileFormController {

    @Autowired
    private IMobileFormService mobileFormService;

    /**
     * @Description 移动表单数据保存
     * <AUTHOR>
     * @Date 2019/10/12 9:29
     **/
    @PostMapping(value = "/saveMobileForm")
    @ApiOperation("移动表单数据保存")
    public Result<T> saveMobileForm(@RequestBody MobileForm mobileForm) {
        Result<T> result = new Result<T>();
        try {
            mobileFormService.saveMobileForm(mobileForm);
            result.success("移动表单数据保存成功！");
        } catch (Exception e) {
            log.info(e.getMessage(), e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * @Description 移动表单数据获取
     * <AUTHOR>
     * @Date 2019/10/12 9:54
     **/
    @GetMapping(value = "/getMobileForm")
    @ApiOperation("移动表单数据获取")
    public Result<String> getMobileForm(@RequestParam(name = "code", required = true) String code) {
        Result<String> result = new Result<String>();
        String value = mobileFormService.getMobileForm(code);
        result.setSuccess(true);
        result.setResult(value);
        return result;
    }


}
