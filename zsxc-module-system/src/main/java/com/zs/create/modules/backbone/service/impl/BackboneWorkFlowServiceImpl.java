package com.zs.create.modules.backbone.service.impl;

import com.zs.create.modules.backbone.entity.BackboneWorkFlowEntity;
import com.zs.create.modules.backbone.mapper.BackboneWorkFlowMapper;
import com.zs.create.modules.backbone.service.BackboneWorkFlowService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description 流程记录表Service实现层
 *
 * <AUTHOR> @email 
 * @date 2022-06-07 08:42:23
 * @Version: V1.0
 */
@Service
public class BackboneWorkFlowServiceImpl extends ServiceImpl<BackboneWorkFlowMapper, BackboneWorkFlowEntity> implements BackboneWorkFlowService {

    @Autowired
    private BackboneWorkFlowMapper backboneWorkFlowMapper;

    @Override
    public List<BackboneWorkFlowEntity> queryByApplyId(String id,int status,String userId) {
        return backboneWorkFlowMapper.queryByApplyId(id,status,userId);
    }
}
