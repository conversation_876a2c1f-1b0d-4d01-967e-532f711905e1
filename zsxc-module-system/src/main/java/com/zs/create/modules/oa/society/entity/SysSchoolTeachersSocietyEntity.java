package com.zs.create.modules.oa.society.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 社团拟知道单位
 * 
 * <AUTHOR>
 * @email ''
 * @date 2021-05-12 15:45:05
 */
@Data
@TableName("sys_school_teachers_society")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sys_school_teachers_society对象", description="社团拟知道单位")
public class SysSchoolTeachersSocietyEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private String id;
	/**
	 * 分类名称
	 */
	private String name;
	/**
	 * 排序
	 */
	private Integer sort;
	/**
	 * 创建人的主键id
	 */
	private String createBy;
	/**
	 * 创建人的姓名
	 */
	private String createByName;
	/**
	 * 创建时间
	 */
	private Date createDate;
	/**
	 * 修改人的主键id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateDate;
	/**
	 * 删除标识  0未删除  1已删除
	 */
	private Integer delFlag;

}
