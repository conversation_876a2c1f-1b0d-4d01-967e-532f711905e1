package com.zs.create.vote;

import lombok.Data;

import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @className: RankTest
 * @description: TODO 类描述
 * @author: hy
 * @date: 2020-11-23
 **/
public class RankTest {
    public static void main(String[] args) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Map<String,Object> map = new HashMap<>();
        map.put("startTime","2023-06-16 14:09:35");
        try{
            String startTime = (String) map.get("startTime");
            Date startDate = format.parse(startTime);
            Date endDate = new Date();
            long time = (endDate.getTime()-startDate.getTime())/1000;
            map.put("take_time",time);
        }catch (Exception e){

        }
        System.out.println(map);
    }
    private static void test1() {
        List<Person> list=getList();
        list.sort((x,y)->x.getAge()-y.getAge());
        int a=0,b=Integer.MIN_VALUE,c=0;
        for (Person person : list) {
            if (person.getAge().equals(b)){
                c++;
            }else {
                b=person.getAge();
                a=a+c+1;
                c=0;
            }
            person.setRank(a);
        }
        list.stream().sorted((x,y)->x.getRank()-y.getRank()).forEach(System.out::println);
    }

    private static List<Person> getList() {
        ArrayList<Person> list = new ArrayList<>();
        list.add(new Person("张三", 20));
        list.add(new Person("李四", 50));
        list.add(new Person("王五", 25));
        list.add(new Person("赵六", 25));
        list.add(new Person("marry", 35));
        list.add(new Person("jack", 35));
        list.add(new Person("ashe", 35));
        return list;
    }

    @Data
    static class Person{
        private String name;
        private Integer age;
        private Integer rank;
        public Person(String name, Integer age) {
            this.name = name;
            this.age = age;
        }
        @Override
        public String toString() {
            return "Person{" +
                    "name='" + name + '\'' +
                    ", age=" + age +
                    ", rank=" + rank +
                    '}';
        }
    }
}
