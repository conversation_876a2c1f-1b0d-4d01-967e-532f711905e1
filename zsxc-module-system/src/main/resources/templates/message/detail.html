<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <title>详情</title>
    <style>
        header {
            border-bottom: 1px solid #e6e6e6;
            padding: 10px;
        }
        .title {
            width: 100vw;
            color: #333333;
            line-height: 25px;
            margin-top: 30px;
            margin-bottom: 30px;
            font-size: 25px;
            word-wrap: break-word;
        }
        .title_1 {
            font-size: 25px;
        }
        .title_2 {
            font-size: 20px;
        }
        .title_3 {
            font-size: 15px;
        }
        section {
            padding: 20px 10px;
        }
    </style>
</head>
<body>

<div>
    <header>
        <p id="p" class="title" th:text="${title}"></p>
        <span id="span1" class="title">发布时间:</span> <span id="span2" class="title" th:text="${publicTimeStr}"></span>
    </header>
    <select id="fontSizes" style="margin-top: 3px; margin-left: 10px;margin-bottom: 0px;">
        <option type="button" value="大号" onclick="change('title_1')"><span class="title">大号字体</span></option>
        <option type="button" value="中号" onclick="change('title_2')" selected><span class="title">中号字体</span></option>
        <option type="button" value="小号" onclick="change('title_3')"><span class="title">小号字体</span></option>
    </select>
    <section style="margin-top: 0px">
        <div id="div" class="title" th:utext="${content}" style="margin-top: 3px; line-height: 40px"></div>
    </section>
</div>
<script>
    var opts = document.getElementById('fontSizes')

    opts.onchange=  function change(e) {
        var eleIds = ["p", "span1", "span2", "div"];
        for (var k = 0; k < eleIds.length; k++) {
            var ele = document.getElementById(eleIds[k]);
            for (var i = 1; i <= 3; i++) {
                ele.classList.remove("title_" + i);
            }
            ele.classList.add("title_" + (opts.selectedIndex + 1));
        }
    }
</script>

</body>