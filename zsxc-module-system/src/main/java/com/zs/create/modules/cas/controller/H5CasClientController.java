package com.zs.create.modules.cas.controller;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.constant.CommonConstant;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.PasswordUtil;
import com.zs.create.common.util.StringKit;
import com.zs.create.modules.cas.util.CASServiceUtil;
import com.zs.create.modules.cas.util.XmlUtils;
import com.zs.create.modules.mobile.WxLoginApiController;
import com.zs.create.modules.shiro.vo.DefContants;
import com.zs.create.modules.system.entity.SysRole;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.entity.SysUserRole;
import com.zs.create.modules.system.model.SysLoginModel;
import com.zs.create.modules.system.service.*;
import com.zs.create.modules.system.vo.LoginDto;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import com.zs.create.util.RedisUtil;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * <AUTHOR>
 * @Date 2024/5/14
 */
@Slf4j
@RestController
@RequestMapping("/h5Cas/client")
public class H5CasClientController {

    @Autowired
    private ISysUserService sysUserService;
    @Value("${cas.prefixUrl}")
    private String prefixUrl;
    @Autowired
    private ISysLogService sysLogService;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    SysWeixinUserService sysWeixinUserService;
    @Autowired
    private ISysRoleService roleService;
    @Autowired
    private ISysUserRoleService sysUserRoleService;
    @Autowired
    WxLoginApiController wxLoginApiController;
    @Autowired
    ISysDepartService sysDepartService;

    @ResponseBody
    @PostMapping("/validateLogin")
    public Result<?> validateLogin(@RequestBody LoginDto loginDto,
                                   HttpServletRequest request) {
        Result result = new Result<JSONObject>();
        log.info("Rest api login.");
        try {
            String validateUrl = prefixUrl + "/serviceValidate";
            String res = CASServiceUtil.getSTValidate(validateUrl, loginDto.getTicket(), loginDto.getService());
            log.warn("res====>>>" + res);
            final String error = XmlUtils.getTextForElement(res, "authenticationFailure");
            if (StringUtils.isNotEmpty(error)) {
                throw new Exception(error);
            }
            final String user = StringKit.null2String(XmlUtils.getTextForElement(res, "user"));
            final String gid = StringKit.null2String(XmlUtils.getTextForElement(res, "gid"));
            if (StringKit.isEmpty(user)) {
                if (StringKit.isEmpty(gid)) {
                    log.error("统一身份登录失败id:{},{}", user, gid);
                    return result.error500("统一身份登录失败");
                }
            }
//            log.info("-------token----username---" + principal);
            //1. 校验用户是否有效
            SysUser sysUser = sysUserService.getUserByName(user);
            if (StringKit.isEmpty(sysUser)) {
                sysUser = sysUserService.getUserByGid(user);
                if (StringKit.isEmpty(sysUser)) {
                    sysUser = sysUserService.getUserByName(gid);
                }
                if (StringKit.isEmpty(sysUser)) {
                    sysUser = sysUserService.getUserByGid(gid);
                }
            }
            result = sysUserService.checkUserIsEffective(sysUser);
            if (!result.isSuccess()) {
                return result;
            }

            //2. 保存用户信息到session中);
            request.getSession().setAttribute("loginname", sysUser.getUsername());
            //获取用户部门信息
            sysUser.setLoginNum(sysUser.getLoginNum() + 1);
            sysUserService.updateById(sysUser);
            //增加团委权限判断
            Boolean twCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
            sysUser.setIsTwCharge(twCharge);
            JSONObject jsonObject = wxLoginApiController.wxUserInfo(sysUser);

            result.setResult(jsonObject);
            result.success("登录成功");
        } catch (Exception e) {
            result.error500("登陆失败");
        }
        return result;
    }

    /**
     * 退出登录
     *
     * @param request
     * @param response
     * @return
     */
    @RequestMapping(value = "/logout")
    public Result<Object> logout(HttpServletRequest request, HttpServletResponse response) {
        //用户退出逻辑
        Subject subject = SecurityUtils.getSubject();
        LoginUser sysUser = (LoginUser) subject.getPrincipal();
        sysLogService.addLog("用户名: " + sysUser.getUsername() + ",退出成功！", CommonConstant.LOG_TYPE_1, null);
        log.info(" 用户名:  " + sysUser.getUsername() + ",退出成功！ ");
        subject.logout();
        String token = request.getHeader(DefContants.X_ACCESS_TOKEN);
        //清空用户Token缓存
        redisUtil.del(CommonConstant.PREFIX_USER_TOKEN + token);
        //清空用户权限缓存：权限Perms和角色集合
        redisUtil.del(CommonConstant.LOGIN_USER_CACHERULES_ROLE + sysUser.getUsername());
        redisUtil.del(CommonConstant.LOGIN_USER_CACHERULES_PERMISSION + sysUser.getUsername());
        return Result.ok("退出登录成功！");
    }

    /**
     * 查询登录用户角色
     *
     * @param roleCode
     * @return
     */
    private Boolean getHaveRole(String roleCode, String userId) {
        QueryWrapper<SysRole> roleQueryWrapper = new QueryWrapper<>();
        roleQueryWrapper.eq("code", roleCode);
        SysRole role = roleService.getOne(roleQueryWrapper);
        if (role != null) {
            QueryWrapper<SysUserRole> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("user_id", userId);
            queryWrapper.eq("user_id", role.getId());
            int count = sysUserRoleService.count(queryWrapper);
            if (count > 0) {
                return true;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 测试环境账号密码登录
     *
     * @param sysLoginModel
     * @return
     * @throws Exception
     */
    @RequestMapping(value = "/mobileLogin")
    @ApiOperation("登录接口")
    public Result<?> login(@RequestBody SysLoginModel sysLoginModel) throws Exception {
        Result result = new Result<JSONObject>();
        try {
            String username = sysLoginModel.getUsername();
            //前端密码加密，后端进行密码解密
            //密码解密
            String password = sysLoginModel.getPassword();

            //1. 校验用户是否有效
            SysUser sysUser = sysUserService.getUserByName(username);
            if (sysUser == null) {
                sysUser = sysUserService.getUserByGid(username);
            }
            result = sysUserService.checkUserIsEffective(sysUser);
            if (!result.isSuccess()) {
                return result;
            }
            //2. 校验用户名或密码是否正确
            String userpassword = PasswordUtil.encrypt(sysUser.getUsername(), password, sysUser.getSalt());
            String syspassword = sysUser.getPassword();
            if (!syspassword.equals(userpassword)) {
                result.error500("用户名或密码错误");
                return result;
            }
            //增加科气值
            sysUser.setLoginNum(sysUser.getLoginNum() + 1);
            sysUserService.updateById(sysUser);
            //增加团委权限判断
            Boolean twCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
            sysUser.setIsTwCharge(twCharge);
            JSONObject jsonObject = wxLoginApiController.wxUserInfo(sysUser);

            result.setResult(jsonObject);
            result.success("登录成功");
            return result;

        } catch (Exception e) {
            result.error500("登陆失败");
        }
        return result;
    }
}
