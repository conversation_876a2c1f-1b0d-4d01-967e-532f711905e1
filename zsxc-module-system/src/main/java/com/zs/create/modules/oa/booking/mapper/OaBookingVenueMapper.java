package com.zs.create.modules.oa.booking.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.oa.booking.entity.venueBookingVo;
import com.zs.create.modules.oa.ticketManagement.entity.OaVenueActivityEntity;
import org.apache.ibatis.annotations.Param;
import com.zs.create.modules.oa.booking.entity.OaBookingVenueEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description 场馆预约-场馆Mapper层
 *
 * <AUTHOR> @email 
 * @date 2023-04-27 14:13:44
 * @Version: V1.0
 */
public interface OaBookingVenueMapper extends BaseMapper<OaBookingVenueEntity> {

    List<venueBookingVo> mobileGetVenue();
}