package com.zs.create.modules.lib.service.impl;

import com.zs.create.modules.lib.entity.ScLibrayProgrammeEntity;
import com.zs.create.modules.lib.mapper.ScLibrayProgrammeMapper;
import com.zs.create.modules.lib.service.ScLibrayProgrammeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description 项目库培养方案中间表Service实现层
 *
 * <AUTHOR>
 * @email null
 * @date 2020-07-09 10:19:02
 * @Version: V1.0
 */
@Service
public class ScLibrayProgrammeServiceImpl extends ServiceImpl<ScLibrayProgrammeMapper, ScLibrayProgrammeEntity> implements ScLibrayProgrammeService {

    @Autowired
    private ScLibrayProgrammeMapper scLibrayProgrammeMapper;

    @Override
    public int deleteList(List<String> list,String id){
        return scLibrayProgrammeMapper.deleteList(list,id);
    }
}
