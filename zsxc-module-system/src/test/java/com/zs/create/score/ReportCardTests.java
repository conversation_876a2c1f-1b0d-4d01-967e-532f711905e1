package com.zs.create.score;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.modules.score.util.HtmlUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.thymeleaf.TemplateEngine;
import org.thymeleaf.context.Context;

import java.io.File;
import java.nio.charset.StandardCharsets;

/**
 * <AUTHOR>
 * @date 2022/3/25
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class ReportCardTests {

    @Value("${qrUrl}")
    private String qrUrl;
    @Autowired
    private TemplateEngine templateEngine;

    @Test
    public void templateRenderTest() {
        Context context = new Context();
        context.setVariable("qrUrl", qrUrl);
        String html = templateEngine.process("score/scReportCard/preview", context);
        System.out.println(html);
    }

    @Test
    public void html2imageTest() throws Exception {
        File dir = FileUtils.getFile(FileUtils.getTempDirectory(), "scReportCardTests");
        if (!dir.exists()) {
            //noinspection ResultOfMethodCallIgnored
            dir.mkdirs();
        }
        long id = SnowIdUtils.uniqueLong();

        Context context = new Context();
        context.setVariable("qrUrl", qrUrl);
        String html = templateEngine.process("score/scReportCard/preview", context);
        File htmlFile = FileUtils.getFile(dir, "preview_" + id + ".html");
        FileUtils.writeStringToFile(htmlFile, html, StandardCharsets.UTF_8);

        File imageFile = FileUtils.getFile(dir, "preview_" + id + ".png");

        byte[] bytes = HtmlUtils.html2image(html, 1080, 776 * 2, null);
        FileUtils.writeByteArrayToFile(imageFile, bytes);
    }

    @Test
    public void generateQRCodeTest() throws Exception {
        File tempDirectory = FileUtils.getTempDirectory();
        File targetDir = FileUtils.getFile(tempDirectory, "scReportCardTests");
        if (!targetDir.exists()) {
            //noinspection ResultOfMethodCallIgnored
            targetDir.mkdirs();
        }
        File file = File.createTempFile("qrcode", ".png", targetDir);

        String content = "https://yw.zs-si.com/sc-report/scanScore?userId=PB2020";
        QrConfig config = QrConfig.create()
                .setErrorCorrection(ErrorCorrectionLevel.H)
                .setMargin(1)
                .setWidth(150)
                .setHeight(150);
        QrCodeUtil.generate(content, config, file);
    }
}
