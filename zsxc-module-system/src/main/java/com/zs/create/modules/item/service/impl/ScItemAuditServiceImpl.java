package com.zs.create.modules.item.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.base.util.ExcelUtil;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.constant.CommonConstant;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.DateUtils;
import com.zs.create.common.util.StringKit;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.item.entity.*;
import com.zs.create.modules.item.enums.ItemAuditStatusEnum;
import com.zs.create.modules.item.enums.ScItemEnum;
import com.zs.create.modules.item.mapper.ScItemAuditMapper;
import com.zs.create.modules.item.mapper.ScItemCollectMapper;
import com.zs.create.modules.item.mapper.ScItemMapper;
import com.zs.create.modules.item.service.*;
import com.zs.create.modules.lib.entity.ScItemLibraryEntity;
import com.zs.create.modules.lib.mapper.ScItemLibraryMapper;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.entity.SysWorkflowEntity;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.system.service.ISysUserDepartService;
import com.zs.create.modules.system.service.ISysUserService;
import com.zs.create.modules.system.service.SysWorkflowService;
import com.zs.create.modules.system.util.DictUtils;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.mapper.SysWeixinUserMapper;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import com.zs.create.modules.workflow.entity.CommentEntity;
import com.zs.create.modules.workflow.instance.service.impl.ProcessInstanceService;
import com.zs.create.modules.workflow.service.IWorkflowService;
import com.zs.create.modules.workflow.util.TaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.*;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.identity.Authentication;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.pvm.PvmActivity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.impl.pvm.process.ProcessDefinitionImpl;
import org.activiti.engine.impl.pvm.process.TransitionImpl;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.util.Assert;
import org.apache.shiro.util.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@Slf4j
public class ScItemAuditServiceImpl implements ScItemAuditService {
    @Autowired
    RuntimeService runtimeService;
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    ScItemMapper scItemMapper;
    @Autowired
    IdentityService identityService;
    @Autowired
    TaskService taskService;
    @Autowired
    HistoryService historyService;
    @Autowired
    RepositoryService repositoryService;

    @Autowired
    ScItemAuditMapper scItemAuditMapper;
    @Autowired
    ProcessInstanceService processInstanceService;
    @Autowired
    IWorkflowService workflowService;
    @Autowired
    @Lazy
    WxMessageSender wxMessageSender;

    @Autowired
    ISysUserService sysUserService;
    @Autowired
    SysWeixinUserService sysWeixinUserService;
    @Autowired
    ScItemCollectMapper scItemCollectMapper;
    @Autowired
    ScItemLibraryMapper scItemLibraryMapper;
    @Autowired
    SysWeixinUserMapper sysWeixinUserMapper;
    @Autowired
    ISysUserDepartService sysUserDepartService;
    @Autowired
    ItemAuditUserService itemAuditUserService;
    @Autowired
    SysWorkflowService sysWorkflowService;
    @Autowired
    ScItemBudgetService scItemBudgetService;
    @Autowired
    ScHoursDirectoryService scHoursDirectoryService;
    @Autowired
    ScHoursTwoPublicService scHoursTwoPublicService;
    @Autowired
    ScItemAppealService scItemAppealService;
    @Autowired
    private CampusAppService campusAppService;
    @Value("${gzhAppid}")
    String appid;

    public static final String ROLE_XTWSJ = "role_xtwsj";


    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String auditItemProcess(ItemAuditDTO itemAuditDTO,
                                   String assignee, Map<String, Object> variables, Boolean autoAuditFirst) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String taskId = itemAuditDTO.getTaskId();
        String auditNote = itemAuditDTO.getAuditNote();
        Task task = taskService.createTaskQuery().taskId(taskId).active().singleResult();
        if (null == task) throw new ZsxcBootException("当前任务不存在或已经被审核");
        String businessKey = getTaskBusinessKey(task);
        try {
            saveWorkflow(taskId, businessKey, assignee, task.getProcessInstanceId(), auditNote);
            taskService.claim(taskId, assignee);
            taskService.addComment(taskId, task.getProcessInstanceId(), auditNote);
            // modify by hy
            // taskService.setVariablesLocal(taskId,variables);
            taskService.setVariablesLocal(taskId, variables);
            taskService.complete(taskId, variables);
            // 3月1号添加审核后修改第一审核人是否审核字段为1
            if (!Boolean.TRUE.equals(autoAuditFirst)) {
                ScItemEntity scItemEntity = scItemMapper.selectById(businessKey);
                scItemEntity.setDelAudit(DelFlagEnum.DEL.getCode());
                // 20250616 update：驳回时，重置“第一步审核人是否审核”字段
                if (itemAuditDTO.getStatus() != null && itemAuditDTO.getStatus().equals(ItemAuditDTO.PROCESS_REJECT)) {
                    scItemEntity.setDelAudit(DelFlagEnum.NO_DEL.getCode());
                }

                List<CommentEntity> comments = workflowService.getProcessCommentsByInstanceId(task.getProcessInstanceId());
                for (CommentEntity comment : comments) {
                    if (sysUser.getUsername().equals(comment.getUserId())) {
                        Date time = comment.getTime();
                        scItemEntity.setAuditTime(time);
                    }
                }
                if (scItemEntity.getAuditTime() == null) {
                    CommentEntity commentEntity = comments.get(comments.size() - 1);
                    if (commentEntity.getTime() == null) {
                        scItemEntity.setAuditTime(comments.get(comments.size() - 2).getTime());
                    } else {
                        scItemEntity.setAuditTime(commentEntity.getTime());
                    }
                }
                scItemMapper.updateById(scItemEntity);
            }
            // 流程结束修改状态
            modifyStatus4ProcessEndAndSendWxMes(itemAuditDTO, task.getProcessInstanceId(), assignee, businessKey, variables, auditNote);
            return task.getProcessInstanceId();
        } catch (ActivitiTaskAlreadyClaimedException e) {
            log.error("报错{}", e.getMessage());
            throw new ZsxcBootException("任务已被审核");
        }
    }

    public void saveWorkflow(String taskId, String businessKey, String assignee, String processInstanceId, String auditNote) {
        // 判断是否是最后一步
        boolean nex = nextTaskIsEnd(taskId);
        if (nex) {
            // 获得审核人列表
            String candidate = String.join(",", TaskUtil.getCandidateUserIdList(taskId));
            SysWorkflowEntity work = new SysWorkflowEntity();
            work.setItemId(businessKey)
                    .setAssignee(assignee)
                    .setCandidate(candidate)
                    .setTaskId(taskId)
                    .setProcessInstanceId(processInstanceId)
//                    .setProcessInstanceKey()
                    .setAuditNote(auditNote);
            sysWorkflowService.save(work);
        }
    }

    public Boolean nextTaskIsEnd(String taskId) {
        // 参数校验
        // 查询当前任务对象
        Task task = ProcessEngines.getDefaultProcessEngine().getTaskService()
                .createTaskQuery().taskId(taskId).singleResult();
        // 根据taskId获取流程实例Id
        String processInstanceId = task.getProcessInstanceId();
        String definitionId = ProcessEngines.getDefaultProcessEngine()
                .getRuntimeService().createProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult()
                .getProcessDefinitionId();
        ProcessDefinitionEntity processDefinitionEntity =
                (ProcessDefinitionEntity) ((RepositoryServiceImpl)
                        ProcessEngines.getDefaultProcessEngine().getRepositoryService())
                        .getDeployedProcessDefinition(definitionId);
        ActivityImpl activityImpl = processDefinitionEntity.
                findActivity(task.getTaskDefinitionKey());
        // 获取节点所有流向线路信息
        List<PvmTransition> outTransitions = activityImpl.getOutgoingTransitions();
        for (PvmTransition tr : outTransitions) {
            // 获取线路的终点节点
            PvmActivity ac = tr.getDestination();
            // 当其中一条流线的中点不是结束节点时，直接返回 false（不是结束节点）
            if (!"endEvent".equals(ac.getProperty("type"))) {
                return false;
            }
        }
        return true;
    }


    public void modifyStatus4ProcessEndAndSendWxMes(ItemAuditDTO itemAuditDTO, String processInstanceId, String assignee, String businessKey, Map variables, String auditNote) {
        ScItemEntity scItemEntity = scItemMapper.selectById(businessKey);
        Assert.notNull(scItemEntity, "项目信息不存在");
        if (judgeProcessIsEnd(processInstanceId)) {
            if (null != variables &&
                    ItemAuditDTO.PROCESS_REJECT.equals(variables.get("status"))) { // 驳回结束
                Integer rejectstatus = itemAuditDTO.getRejectstatus();
                if (rejectstatus.equals(DelFlagEnum.NO_DEL.getCode())) {      // 0：驳回不可修改
                    scItemMapper.updateExamineStatus(businessKey, ItemAuditStatusEnum.REJECTNOUPDATE.getCode());
                } else {
                    scItemMapper.updateExamineStatus(businessKey, ItemAuditStatusEnum.REJECT.getCode());
                }
                sendAuditResult2ItemCreateUser(scItemEntity, ItemAuditDTO.PROCESS_REJECT, assignee, null, auditNote);
                sendAuditToAuditor(scItemEntity, assignee, processInstanceId);
            } else { // 审核通过
                scItemEntity.setLastApprovalMan(assignee);
                scItemMapper.updateById(scItemEntity);
                // 需要场地申请
                if (ScItemEntity.PLACE_APPLY_YES.equals(scItemEntity.getNeedPlaceApply())) {
                    scItemMapper.updateExamineStatus(businessKey, ItemAuditStatusEnum.COMPLETE_INFO.getCode());
                    sendAuditResult2ItemCreateUser(scItemEntity, ItemAuditDTO.PROCESS_PASS, assignee, ScItemEntity.PLACE_APPLY_YES, auditNote);
                } else {
                    if (ScItemEntity.ROOT_PID.equals(scItemEntity.getPid())) {
                        scItemMapper.updateExamineStatus(businessKey, ItemAuditStatusEnum.PROJECT_SET_UP.getCode());
                    } else {
                        // 子项目审核
                        scItemMapper.updateExamineStatus(businessKey, ItemAuditStatusEnum.PUBLISH.getCode());
                    }
                    // scItemBudgetService.modifyBudgetItemStatus(scItemEntity.getId(), ItemAuditStatusEnum.PROJECT_SET_UP.getCode());
                    sendAuditResult2ItemCreateUser(scItemEntity, ItemAuditDTO.PROCESS_PASS, assignee, ScItemEntity.PLACE_APPLY_NO, auditNote);
                }
            }
        } else { // 流程未结束
            processNotEndMes(processInstanceId, scItemEntity);
        }
    }

    /**
     * 流程未结束消息提醒
     *
     * @param processInstanceId
     * @param scItemEntity
     */
    public void processNotEndMes(String processInstanceId, ScItemEntity scItemEntity) {
        Task currentTask = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .active().singleResult();

        List<String> taskCandidates = workflowService.getTaskUser(currentTask.getId());
        if (!CollectionUtils.isEmpty(taskCandidates)) {
            for (String reciveUserId : taskCandidates) {
                sendTodoTaskMes2candidateUser(scItemEntity, reciveUserId);
            }
        }

    }

    /**
     * 给代办人发送待办任务消息
     *
     * @param item
     * @param reciveUserId
     */
    public void sendTodoTaskMes2candidateUser(ScItemEntity item, String reciveUserId) {
        if (null != item) {
            String title = "项目审核提醒";
            StringBuilder contentBuilder = new StringBuilder()
                    .append("有关项目：").append(item.getItemName())
                    .append("，等待您的审核，请及时处理。");
            String remark = "点击可查看详情";

            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(reciveUserId, appid);
            if (null == wxMesUserInfo) {
                log.error("给代办人发送待办任务消息，未找到接收人：{} 的信息", reciveUserId);
                // return;
            }
            String openId = "";
            String theme = "";
            if (StringKit.isNotEmpty(wxMesUserInfo)) {
                openId = StringKit.null2String(wxMesUserInfo.getOpenId());
                theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的工作提醒";
            }
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setMiniAppUrl("pagesB/projectreview/projectreview")
                    .setUserId(reciveUserId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }

    /**
     * 给项目创建人发送审核结果消息
     *
     * @param item
     * @param auditResult
     * @param assignee
     */
    public void sendAuditResult2ItemCreateUser(ScItemEntity item, Integer auditResult, String assignee, String placeApply, String auditNote) {
        if (null != item) {
            String createUserId = item.getCreateBy();
            String title = "项目审核提醒";
            StringBuilder contentBuilder = new StringBuilder();
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(createUserId, appid);
            if (null == wxMesUserInfo) {
                log.error("给项目创建人发送审核结果消息，未找到接收人：{} 的信息", createUserId);
                return;
            }
            String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的工作提醒";
            String openId = wxMesUserInfo.getOpenId();
            if (ItemAuditDTO.PROCESS_PASS.equals(auditResult)) {
                contentBuilder.append("您提交的项目：" + item.getItemName());
                if (ScItemEntity.PLACE_APPLY_NO.equals(placeApply)) {
                    contentBuilder.append("已经审核通过，请及时进行下一步操作。");
                } else {
                    contentBuilder.append("已经审核通过，请及时补充场地信息。");
                }

            } else {
                contentBuilder.append("有关项目：" + item.getItemName());
                String userCodeAndUserName = sysUserService.getUserCodeAndUserName(assignee);
                contentBuilder.append("被" + userCodeAndUserName + "驳回了，驳回理由：" + auditNote + "请及时处理");
            }

            String remark = "点击可查看详情";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(item.getCreateBy())
                    .setCreateDate(new Date())
                    .setMiniAppUrl("pagesB/created/created")
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }

    /**
     * 给前面审核过此项目的审核人发送消息
     *
     * @param scItemEntity
     * @param assignee
     */
    public void sendAuditToAuditor(ScItemEntity scItemEntity, String assignee, String processInstanceId) {
        if ("1".equals(scItemEntity.getForm())) {
            // 查询二课领导小组用户
            List<String> xtwUserList = sysUserService.qryUserByRole(ROLE_XTWSJ).stream().map(SysUser::getId).collect(Collectors.toList());
            if (xtwUserList.contains(assignee)) {
                List<CommentEntity> comments
                        = workflowService.getProcessCommentsByInstanceId(processInstanceId);
                for (CommentEntity comment : comments) {
                    // 过滤掉二课领导小组
                    if (comment.getUserId().equals(assignee)) {
                        continue;
                    }
                    String title = "项目审核提醒";
                    String content = "您所审核通过的" + scItemEntity.getItemName() + "已被二课领导小组办公室驳回";
                    WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(comment.getUserId(), appid);
                    if (null == wxMesUserInfo) {
                        log.error("给项目创建人发送审核结果消息，未找到接收人：{} 的信息", comment.getUserId());
                        continue;
                    }
                    String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的工作提醒";
                    String openId = wxMesUserInfo.getOpenId();

                    String remark = "点击可查看详情";
                    WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                            .setUseCommonTemplate(Boolean.TRUE)
                            .setTheme(theme)
                            .setTitle(title)
                            .setUserId(comment.getUserId())
                            .setCreateDate(new Date())
                            .setMiniAppUrl("pagesB/projectreview/projectreview?curIndex=1")
                            .setContent(content)
                            .setOpenId(openId)
                            .setRemark(remark);
                    wxMessageSender.wxMessageSend(wxCommonMsgInfo);
                }
            }
        }

    }

    @Override
    @Transactional
    public Result<?> revokeItem(String processInstanceId) {
        Result<?> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 3.使用流程实例，查询
        /*ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if(pi==null) {
            throw new ZsxcBootException("流程未启动或已执行完成，无法撤回");
        }*/
        // 4.使用流程实例对象获取BusinessKey
        // String business_key = pi.getBusinessKey();
        // Task task = taskService.createTaskQuery().processInstanceBusinessKey(business_key).singleResult();
        Task task = taskService.createTaskQuery().processInstanceId(processInstanceId).singleResult();
        if (task == null) {
            throw new ZsxcBootException("流程未启动或已执行完成，无法撤回");
        }
        List<HistoricTaskInstance> list = historyService
                .createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .finished()
                .orderByTaskCreateTime()
                .desc()
                .list();
        // String myTaskId = null;
        HistoricTaskInstance hisTask = null;
        if (list != null && list.size() > 0) {
            HistoricTaskInstance hisTaskObj = list.get(0);
            if (sysUser.getId().equals(hisTaskObj.getAssignee())) {
                hisTask = hisTaskObj;
            }
        }

        if (null == hisTask || !sysUser.getId().equals(hisTask.getAssignee())) {
            throw new ZsxcBootException("下一步审核人已审核，无法撤回");
        }

        /*for(HistoricTaskInstance hti : list) {
            if(sysUser.getId().equals(hti.getAssignee())) {
                //myTaskId = hti.getId();
                myTask = hti;
                break;
            }
        }*/
        /*if(null==myTaskId) {
            throw new ZsxcBootException("下一步审核人已审核，无法撤回");
        }*/
        // 获取上一活动的节点id
        String taskNodeId = hisTask.getTaskDefinitionKey();
        ProcessDefinitionImpl processDefinitionImpl = (ProcessDefinitionImpl) repositoryService.getProcessDefinition(hisTask.getProcessDefinitionId());

        // 取得上一步活动
        ActivityImpl currActivity = processDefinitionImpl.findActivity(taskNodeId);
        // 取得当前待办活动节点
        ActivityImpl execActivity = processDefinitionImpl.findActivity(task.getTaskDefinitionKey());
        // 清除当前活动的出口
        List<PvmTransition> pvmTransitionList = execActivity.getOutgoingTransitions();
        List<PvmTransition> oriPvmTransitionList = new ArrayList<>(pvmTransitionList);
        pvmTransitionList.clear();

        // 把进口当做出口，重新建立通道
        List<TransitionImpl> newTransitions = new ArrayList<>();
        TransitionImpl newTransition = execActivity.createOutgoingTransition();
        newTransition.setDestination(currActivity);
        newTransitions.add(newTransition);
        // 完成任务
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        for (Task taskObj : tasks) {
            Map<String, Object> currentVariables = new HashMap<>();
            Authentication.setAuthenticatedUserId(sysUser.getId());
            taskService.addComment(taskObj.getId(), taskObj.getProcessInstanceId(), "撤回");
            taskService.complete(taskObj.getId(), currentVariables);
            // 删除历史、此处执行这两行代码在ACT_HI_TASKINST表中是看不到撤回记录的，但是在ACT_HI_ACTINST表中能看到全部记录
            historyService.deleteHistoricTaskInstance(taskObj.getId());
            historyService.deleteHistoricTaskInstance(hisTask.getId());
        }
        // 恢复方向
        for (TransitionImpl transitionImpl : newTransitions) {
            execActivity.getOutgoingTransitions().remove(transitionImpl);
        }
        pvmTransitionList.addAll(oriPvmTransitionList);
        result.setSuccess(true);
        result.success("撤回成功");
        return result;
    }

    @Override
    public Result<?> recallAuditItem(String itemId) {
        Result<?> result = new Result<>();
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        scItemEntity.setStick("1");
        // 结束流程
        String processInstanceId = scItemEntity.getProcessInstanceId();
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        processEngine.getRuntimeService().deleteProcessInstance(processInstanceId, "删除当前流程");
        // 立项跟补充信息都是审核完的状态
        scItemEntity.setExamineStatus(ItemAuditStatusEnum.STAGING.getCode());
        scItemEntity.setProcessInstanceId("");
        scItemMapper.updateById(scItemEntity);
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
        result.setSuccess(true);
        result.success("撤回成功");
        return result;
    }

    @Override
    public void queryTaskList(TaskQueryDTO taskQueryDTO, HttpServletRequest request, HttpServletResponse response) {
        log.info("项目审核，入参{}", taskQueryDTO);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        taskQueryDTO.setAssignee(sysUser.getId());

        // 用于区分页面请求入口 1：待审核，2：已审核
        String itemDistinguish = "";
        List<TaskVo> taskVoList = null;
        if (TaskQueryDTO.QUERY_HISTORY.equals(taskQueryDTO.getQueryType())) {
            taskVoList = this.historyTaskList(taskQueryDTO);
            itemDistinguish = "2";
        }
        if (TaskQueryDTO.QUERY_TODO.equals(taskQueryDTO.getQueryType())) {
            taskVoList = this.todoTaskList(taskQueryDTO);
            ;
            itemDistinguish = "1";
        }
        taskVoList.stream().forEach(record -> {
            record.setModule(Objects.requireNonNull(ScItemEnum.codeOf(record.getModule())).getDesc());
            record.setForm(Objects.requireNonNull(ScItemEnum.codeOf(ScItemEntity.FORM + record.getForm())).getDesc());
        });

        if ("1".equals(itemDistinguish)) {
            // excel标题
            String[] title = {"项目名称", "所在模块", "项目形式", "联系人"};

            // excel文件名
            String fileName = "项目审核" + System.currentTimeMillis() + ".xls";

            // sheet名
            String sheetName = "项目审核";

            String[][] content = new String[taskVoList.size()][title.length];
            for (int i = 0; i < taskVoList.size(); i++) {
                int j = 0;
                TaskVo obj = taskVoList.get(i);
                content[i][j++] = obj.getItemName();
                content[i][j++] = obj.getModule();
                content[i][j++] = obj.getForm();
                content[i][j++] = obj.getLinkMan();

                if (i == taskVoList.size() - 1) {
                    break;
                }
            }
            // 创建XSSFWorkbook
            XSSFWorkbook wb = ExcelUtil.getHSSFWorkbook(sheetName, title, content, null);

            // 响应到客户端
            try {
                ExcelUtil.setResponseHeader(response, fileName);
                OutputStream os = response.getOutputStream();
                wb.write(os);
                os.flush();
                os.close();
            } catch (Exception e) {
                e.printStackTrace();
                throw new ZsxcBootException("导出失败");
            }


        } else if ("2".equals(itemDistinguish)) {
            // excel标题
            String[] title = {"项目名称", "所在模块", "项目形式", "联系人", "审核时间"};

            // excel文件名
            String fileName = "项目审核" + System.currentTimeMillis() + ".xls";

            // sheet名
            String sheetName = "项目审核";

            SimpleDateFormat sf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String[][] content = new String[taskVoList.size()][title.length];
            for (int i = 0; i < taskVoList.size(); i++) {
                int j = 0;
                TaskVo obj = taskVoList.get(i);
                content[i][j++] = obj.getItemName();
                content[i][j++] = obj.getModule();
                content[i][j++] = obj.getForm();
                content[i][j++] = obj.getLinkMan();
                if (Objects.isNull(obj.getTime())) {
                    content[i][j++] = " ";
                } else {
                    content[i][j++] = sf.format(obj.getTime());
                }
                if (i == taskVoList.size() - 1) {
                    break;
                }
            }
            // 创建XSSFWorkbook
            XSSFWorkbook wb = ExcelUtil.getHSSFWorkbook(sheetName, title, content, null);

            // 响应到客户端
            try {
                ExcelUtil.setResponseHeader(response, fileName);
                OutputStream os = response.getOutputStream();
                wb.write(os);
                os.flush();
                os.close();
            } catch (Exception e) {
                e.printStackTrace();
                throw new ZsxcBootException("导出失败");
            }
        }

    }

    @Override
    @XxlJob("auditTimeSyncMysql")
    public void auditTimeSyncMysql() {
        List<ScItemEntity> scItemEntities = scItemMapper.qryAllAuditTimeIsNull().stream().distinct().collect(Collectors.toList());

        for (ScItemEntity record : scItemEntities) {
            List<CommentEntity> comments = null;
            try {
                comments = workflowService.getProcessCommentsByInstanceId(record.getProcessInstanceId());
                log.info("流程数据查询为[{}]", comments);
            } catch (Exception e) {
                log.error("报错信息为", e);
            }
            if (!CollectionUtil.isEmpty(comments)) {
                if (record.getAuditTime() == null) {
                    CommentEntity commentEntity = comments.get(comments.size() - 1);
                    if (commentEntity.getTime() == null) {
                        record.setAuditTime(comments.get(comments.size() - 2).getTime());
                    } else {
                        record.setAuditTime(commentEntity.getTime());
                    }
                    scItemMapper.updateAuditTime(record);
                }
            }
        }
    }

    /**
     * 历史任务列表查询
     *
     * @param taskQueryDTO
     * @return
     */
    public List<TaskVo> historyTaskList(TaskQueryDTO taskQueryDTO) {
        List<TaskVo> taskVos = scItemAuditMapper.historyTaskList(taskQueryDTO);
        for (TaskVo sc : taskVos) {
            if (ItemAuditStatusEnum.SCORE_CONFIRM.getCode().equals(sc.getExamineStatus())) {
                LocalDateTime today = LocalDateTime.now();
                LambdaQueryWrapper<ScHoursDirectoryEntity> wrapper = Wrappers.<ScHoursDirectoryEntity>lambdaQuery()
                        .eq(ScHoursDirectoryEntity::getItemId, sc.getItemId());
                ScHoursDirectoryEntity one = scHoursDirectoryService.getOne(wrapper);
                Integer publicDuration = Integer.valueOf(DictUtils.queryDictTextByKey("item_public_perioid",
                        "hours", "1"));
                ScHoursTwoPublicEntity twoPublicEntity = scHoursTwoPublicService.getOne(Wrappers.<ScHoursTwoPublicEntity>lambdaQuery().eq(ScHoursTwoPublicEntity::getItemId, sc.getItemId()).last("limit 1"));
                if (twoPublicEntity == null) {
                    if (today.isAfter(DateUtils.dateToLocalDateTime(one.getPublicStartTime()).plusHours(publicDuration))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                } else if (twoPublicEntity != null && twoPublicEntity.getTwoPublicStartTime() != null) {
                    if (today.isAfter(DateUtils.dateToLocalDateTime(one.getPublicStartTime()).plusHours(publicDuration)) && today.isBefore(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                    if (today.isAfter(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime())) && today.isBefore(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()).plusHours(ScItemEntity.TWO_PUBLIC_PERIOD))) {
                        LambdaQueryWrapper<ScItemAppealEntity> appealWrapper = Wrappers.<ScItemAppealEntity>lambdaQuery()
                                .eq(ScItemAppealEntity::getItemId, sc.getItemId())
                                .eq(ScItemAppealEntity::getIsHandle, "-1");
                        int count = scItemAppealService.count(appealWrapper);
                        if (count == 0) {
                            sc.setExamineStatus(ItemAuditStatusEnum.TWO_SCORE_CONFIRM.getCode());
                        } else {
                            sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                        }
                    }
                    if (today.isAfter(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()).plusHours(ScItemEntity.TWO_PUBLIC_PERIOD))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                }
            }
        }
        return taskVos;
    }

    /**
     * 代办任务列表查询
     *
     * @param taskQueryDTO
     * @return
     */
    public List<TaskVo> todoTaskList(TaskQueryDTO taskQueryDTO) {
        List<TaskVo> taskVos = scItemAuditMapper.todoTaskList(taskQueryDTO);
        return taskVos;
    }

    /*@Override
    // 先屏蔽，后面要消息提醒的时候再换成这个方法
    public Result<?> revokeItem(String processInstanceId){
        Result<?> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //3.使用流程实例，查询
        ProcessInstance pi = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if(pi==null) {
            throw new ZsxcBootException("流程未启动或已执行完成，无法撤回");
        }
        //4.使用流程实例对象获取BusinessKey
        String business_key = pi.getBusinessKey();
        Task task = taskService.createTaskQuery().processInstanceBusinessKey(business_key).singleResult();
        if(task==null) {
            throw new ZsxcBootException("流程未启动或已执行完成，无法撤回");
        }
        List<HistoricTaskInstance> list = historyService
                .createHistoricTaskInstanceQuery()
                .processInstanceId(processInstanceId)
                .finished()
                .orderByTaskCreateTime()
                .desc()
                .list();
        HistoricTaskInstance hisTask = null;
        if(list != null && list.size()>0) {
			HistoricTaskInstance hisTaskObj = list.get(0);
			if(sysUser.getId().equals(hisTaskObj.getAssignee())) {
				hisTask = hisTaskObj;
			}
		}

        if(null==hisTask || !sysUser.getId().equals(hisTask.getAssignee())) {
            throw new ZsxcBootException("下一步审核人已审核，无法撤回");
        }
        //获取上一活动的节点id
        String taskNodeId = hisTask.getTaskDefinitionKey();
        ProcessDefinitionImpl processDefinitionImpl = (ProcessDefinitionImpl) repositoryService.getProcessDefinition(hisTask.getProcessDefinitionId());

        // 取得上一步活动
        ActivityImpl currActivity = processDefinitionImpl.findActivity(taskNodeId);
        // 取得当前待办活动节点
        ActivityImpl execActivity = processDefinitionImpl.findActivity(task.getTaskDefinitionKey());
        // 清除当前活动的出口
        List<PvmTransition> oriPvmTransitionList = new ArrayList<>();
        List<PvmTransition> pvmTransitionList = execActivity.getOutgoingTransitions();
        for (PvmTransition pvmTransition : pvmTransitionList) {
            oriPvmTransitionList.add(pvmTransition);
        }
        pvmTransitionList.clear();

        //把进口当做出口，重新建立通道
        List<TransitionImpl> newTransitions = new ArrayList<>();
        TransitionImpl newTransition = execActivity.createOutgoingTransition();
        newTransition.setDestination(currActivity);
        newTransitions.add(newTransition);
        // 完成任务
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstanceId).list();
        for (Task taskObj : tasks) {
            Map<String,Object> currentVariables = new HashMap<>();
            Authentication.setAuthenticatedUserId(sysUser.getId());
            taskService.addComment(taskObj.getId(), taskObj.getProcessInstanceId(), "撤回");
            taskService.complete(taskObj.getId(), currentVariables);
            //删除历史、此处执行这两行代码在ACT_HI_TASKINST表中是看不到撤回记录的，但是在ACT_HI_ACTINST表中能看到全部记录
            historyService.deleteHistoricTaskInstance(taskObj.getId());
            historyService.deleteHistoricTaskInstance(hisTask.getId());
        }
        // 恢复方向
        for (TransitionImpl transitionImpl : newTransitions) {
            execActivity.getOutgoingTransitions().remove(transitionImpl);
        }
        for (PvmTransition pvmTransition : oriPvmTransitionList) {
            pvmTransitionList.add(pvmTransition);
        }

        this.revokeItemSendAuditResult(business_key,sysUser.getId(),task.getId());

        result.setSuccess(true);
        result.success("撤回成功");
        return result;
    }*/


    /**
     * 流程撤回给项目审核候选人发送撤回消息
     * @param currentUserId   当前撤回人
     * @param itemId   业务id
     * @param taskId  任务id
     */
    // 先屏蔽，后面需要加再放开
    /*public  void revokeItemSendAuditResult(String itemId , String currentUserId,String taskId){
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        SysUser sysUser = sysUserService.getUserById(currentUserId);
        //------------------------给项目创建人撤回发送提醒-------------------------
        if(null != scItemEntity){
            String createUserId = scItemEntity.getCreateBy();
            String title = "项目审核撤回提醒";
            StringBuilder contentBuilder = new StringBuilder();
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(createUserId, appid);
            if(null == wxMesUserInfo){
                log.error("给项目创建人发送审核撤回消息，未找到接收人：{} 的信息", createUserId);
                return;
            }
            String theme = wxMesUserInfo.getRealname() + "/"+ wxMesUserInfo.getUsername()+",您有一条新的工作提醒";
            String openId = wxMesUserInfo.getOpenId();
            contentBuilder.append("您提交的项目：" + scItemEntity.getItemName());
            contentBuilder.append("被"+sysUser.getUsername()+"/"+sysUser.getRealname()+"撤回处理");
            String remark = "点击可查看详情";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(scItemEntity.getCreateBy())
                    .setCreateDate(new Date())
                    .setMiniAppUrl("pagesB/created/created")
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
        //------------------------给项目创建人发送撤回提醒-------------------------
        //------------------------给流程代办人发送消息提醒-------------------------
        List<String> taskCandidates = workflowService.getTaskUser(taskId);
        if(!CollectionUtils.isEmpty(taskCandidates)){
            for(String reciveUserId : taskCandidates){
                if(null != scItemEntity){
                    String title = "项目审核撤回提醒";
                    StringBuilder contentBuilder = new StringBuilder();
                    WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(reciveUserId, appid);
                    if(null == wxMesUserInfo){
                        log.error("给项目审核下一步代办人发送审核撤回消息，未找到接收人：{} 的信息", reciveUserId);
                        return;
                    }
                    String theme = wxMesUserInfo.getRealname() + "/"+ wxMesUserInfo.getUsername()+",您有一条新的工作提醒";
                    String openId = wxMesUserInfo.getOpenId();
                    contentBuilder.append("项目：" + scItemEntity.getItemName());
                    contentBuilder.append("审核流程被"+sysUser.getUsername()+"/"+sysUser.getRealname()+"撤回处理");
                    String remark = "点击可查看详情";
                    WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                            .setUseCommonTemplate(Boolean.TRUE)
                            .setTheme(theme)
                            .setTitle(title)
                            .setUserId(reciveUserId)
                            .setCreateDate(new Date())
                            .setMiniAppUrl("pagesB/created/created")
                            .setContent(contentBuilder.toString())
                            .setOpenId(openId)
                            .setRemark(remark);
                    wxMessageSender.wxMessageSend(wxCommonMsgInfo);
                }
            }
        }
        //------------------------给流程代办人发送消息提醒-------------------------
    }*/


    /**
     * 项目库发布新项目提醒收藏人
     */
    @Override
    public void sendAuditResultItemCollectUser(String libId) {
        QueryWrapper<ScItemCollectEntity> itemCollectQueryWrapper = new QueryWrapper<>();
        itemCollectQueryWrapper.eq("library_id", libId);
        itemCollectQueryWrapper.eq("del_flag", DelFlagEnum.NO_DEL.getCode());
        itemCollectQueryWrapper.eq("collect_type", CommonConstant.DEL_FLAG_1.toString());
        List<ScItemCollectEntity> scItemCollectEntityList = scItemCollectMapper.selectList(itemCollectQueryWrapper);
        if (CollectionUtil.isNotEmpty(scItemCollectEntityList)) {
            List<String> userIdList = scItemCollectEntityList.stream().map(ScItemCollectEntity::getUserId).collect(Collectors.toList());
            ScItemLibraryEntity scItemLibraryEntity = scItemLibraryMapper.selectById(libId);
            if (scItemLibraryEntity == null) throw new ZsxcBootException("参数有误！未查询到该精品库");
            List<WxUserDTO> userOpenIds = sysWeixinUserMapper.findUserOpenIds(userIdList, appid);
            userOpenIds.stream().forEach(wxUserDTO -> {
                String title = "新项目发布提醒";
                String theme = wxUserDTO.getRealname() + "/" + wxUserDTO.getUsername() + ",您有一条新的工作提醒";
                StringBuilder contentBuilder = new StringBuilder()
                        .append("有关项目库：").append(scItemLibraryEntity.getName())
                        .append("，有新的项目发布，请及时查看报名");
                String remark = "青春科大智慧团学综合信息平台";
                WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                        .setUseCommonTemplate(Boolean.TRUE)
                        .setTheme(theme)
                        .setTitle(title)
                        .setCreateDate(new Date())
                        .setContent(contentBuilder.toString())
                        .setOpenId(wxUserDTO.getOpenId())
                        .setRemark(remark);
                wxMessageSender.wxMessageSend(wxCommonMsgInfo);
            });
        }
    }


    /**
     * 根据task 获取业务id
     *
     * @param task
     * @return
     */
    private String getTaskBusinessKey(Task task) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId()).active().singleResult();
        return processInstance.getBusinessKey();
    }

    /**
     * 判断流程是否结束
     *
     * @param processInstanceId
     * @return
     */
    @Override
    public Boolean judgeProcessIsEnd(String processInstanceId) {
        return processInstanceService.judgeProcessIsEnd(processInstanceId);
    }

    /**
     * 获取代办任务list
     *
     * @param taskQueryDTO
     * @return
     */
    public List<TaskVo> todoList(TaskQueryDTO taskQueryDTO) {
        return scItemAuditMapper.todoTaskList(taskQueryDTO);
    }


    /**
     * 代办任务分页
     *
     * @param taskQueryDTO
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<TaskVo> todoTaskPage(TaskQueryDTO taskQueryDTO
            , Integer pageNo
            , Integer pageSize) {
        Page<TaskVo> page = new Page<TaskVo>(pageNo, pageSize);
//        page.setRecords(scItemAuditMapper.todoTaskPage(page , taskQueryDTO));
        List<TaskVo> taskVos = scItemAuditMapper.todoTaskPage(page, taskQueryDTO);
        for (TaskVo sc : taskVos) {
            if (ItemAuditStatusEnum.SCORE_CONFIRM.getCode().equals(sc.getExamineStatus())) {
                LocalDateTime today = LocalDateTime.now();
                LambdaQueryWrapper<ScHoursDirectoryEntity> wrapper = Wrappers.<ScHoursDirectoryEntity>lambdaQuery()
                        .eq(ScHoursDirectoryEntity::getItemId, sc.getItemId());
                ScHoursDirectoryEntity one = scHoursDirectoryService.getOne(wrapper);
                Integer publicDuration = Integer.valueOf(DictUtils.queryDictTextByKey("item_public_perioid",
                        "hours", "1"));
                ScHoursTwoPublicEntity twoPublicEntity = scHoursTwoPublicService.getOne(Wrappers.<ScHoursTwoPublicEntity>lambdaQuery().eq(ScHoursTwoPublicEntity::getItemId, sc.getItemId()));
                if (twoPublicEntity == null) {
                    if (today.isAfter(DateUtils.dateToLocalDateTime(one.getPublicStartTime()).plusHours(publicDuration))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                } else if (twoPublicEntity != null && twoPublicEntity.getTwoPublicStartTime() != null) {
                    if (today.isAfter(DateUtils.dateToLocalDateTime(one.getPublicStartTime()).plusHours(publicDuration)) && today.isBefore(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                    if (today.isAfter(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime())) && today.isBefore(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()).plusHours(ScItemEntity.TWO_PUBLIC_PERIOD))) {
                        LambdaQueryWrapper<ScItemAppealEntity> appealWrapper = Wrappers.<ScItemAppealEntity>lambdaQuery()
                                .eq(ScItemAppealEntity::getItemId, sc.getItemId())
                                .eq(ScItemAppealEntity::getIsHandle, "-1");
                        int count = scItemAppealService.count(appealWrapper);
                        if (count == 0) {
                            sc.setExamineStatus(ItemAuditStatusEnum.TWO_SCORE_CONFIRM.getCode());
                        } else {
                            sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                        }
                    }
                    if (today.isAfter(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()).plusHours(ScItemEntity.TWO_PUBLIC_PERIOD))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                }
            }
        }
        page.setRecords(taskVos);
        return page;

    }


    /**
     * 历史任务分页
     *
     * @param taskQueryDTO
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<TaskVo> historyTaskPage(TaskQueryDTO taskQueryDTO
            , Integer pageNo
            , Integer pageSize) {
        Page<TaskVo> page = new Page<>(pageNo, pageSize);
//        page.setRecords(scItemAuditMapper.historyTaskPage(page ,taskQueryDTO));
        List<TaskVo> taskVos = scItemAuditMapper.historyTaskPage(page, taskQueryDTO);
        for (TaskVo sc : taskVos) {
            if (ItemAuditStatusEnum.SCORE_CONFIRM.getCode().equals(sc.getExamineStatus())) {
                LocalDateTime today = LocalDateTime.now();
                LambdaQueryWrapper<ScHoursDirectoryEntity> wrapper = Wrappers.<ScHoursDirectoryEntity>lambdaQuery()
                        .eq(ScHoursDirectoryEntity::getItemId, sc.getItemId());
                ScHoursDirectoryEntity one = scHoursDirectoryService.getOne(wrapper);
                Integer publicDuration = Integer.valueOf(DictUtils.queryDictTextByKey("item_public_perioid",
                        "hours", "1"));
                ScHoursTwoPublicEntity twoPublicEntity = scHoursTwoPublicService.getOne(Wrappers.<ScHoursTwoPublicEntity>lambdaQuery().eq(ScHoursTwoPublicEntity::getItemId, sc.getItemId()).last("limit 1"));
                if (twoPublicEntity == null) {
                    if (today.isAfter(DateUtils.dateToLocalDateTime(one.getPublicStartTime()).plusHours(publicDuration))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                } else if (twoPublicEntity != null && twoPublicEntity.getTwoPublicStartTime() != null) {
                    if (today.isAfter(DateUtils.dateToLocalDateTime(one.getPublicStartTime()).plusHours(publicDuration)) && today.isBefore(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                    if (today.isAfter(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime())) && today.isBefore(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()).plusHours(ScItemEntity.TWO_PUBLIC_PERIOD))) {
                        LambdaQueryWrapper<ScItemAppealEntity> appealWrapper = Wrappers.<ScItemAppealEntity>lambdaQuery()
                                .eq(ScItemAppealEntity::getItemId, sc.getItemId())
                                .eq(ScItemAppealEntity::getIsHandle, "-1");
                        int count = scItemAppealService.count(appealWrapper);
                        if (count == 0) {
                            sc.setExamineStatus(ItemAuditStatusEnum.TWO_SCORE_CONFIRM.getCode());
                        } else {
                            sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                        }
                    }
                    if (today.isAfter(DateUtils.dateToLocalDateTime(twoPublicEntity.getTwoPublicStartTime()).plusHours(ScItemEntity.TWO_PUBLIC_PERIOD))) {
                        sc.setExamineStatus(ItemAuditStatusEnum.DO_SCORE_CONFIRM.getCode());
                    }
                }
            }
        }
        page.setRecords(taskVos);
        return page;
    }

    @Override
    public Page<TaskVo> taskPage(TaskQueryDTO taskQueryDTO, Integer pageNo, Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        taskQueryDTO.setAssignee(sysUser.getId());
        Page<TaskVo> taskVoPage = null;
        if (TaskQueryDTO.QUERY_HISTORY.equals(taskQueryDTO.getQueryType())) {
            taskVoPage = this.historyTaskPage(taskQueryDTO, pageNo, pageSize);
        }
        if (TaskQueryDTO.QUERY_TODO.equals(taskQueryDTO.getQueryType())) {
            taskVoPage = this.todoTaskPage(taskQueryDTO, pageNo, pageSize);
            ;
        }
        return taskVoPage;
    }

}
