package com.zs.create.modules.backbone.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.backbone.AsyncHandel.AsyncHandel;
import com.zs.create.modules.backbone.entity.BackboneLaunchApplyEntity;
import com.zs.create.modules.backbone.entity.BackboneReviewMeetingEntity;
import com.zs.create.modules.backbone.mapper.BackboneReviewMeetingMapper;
import com.zs.create.modules.backbone.service.BackboneLaunchApplyService;
import com.zs.create.modules.backbone.service.BackboneReviewMeetingService;
import com.zs.create.modules.backbone.vo.BackboneReviewVo;
import com.zs.create.modules.system.service.ISysDictService;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * @Description 评议公示Service实现层
 *
 * <AUTHOR>
 * @email null
 * @date 2021-05-26 10:46:57
 * @Version: V1.0
 */
@Service
public class BackboneReviewMeetingServiceImpl extends ServiceImpl<BackboneReviewMeetingMapper, BackboneReviewMeetingEntity> implements BackboneReviewMeetingService {


    @Autowired
    private ISysDictService sysDictService;

    @Resource
    private BackboneReviewMeetingMapper backboneReviewMeetingMapper;

    @Autowired
    private BackboneLaunchApplyService backboneLaunchApplyService;

    @Autowired
    private AsyncHandel asyncHandel;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMettings(BackboneReviewMeetingEntity backboneReviewMeeting) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        backboneReviewMeeting.setGuideTeacher(loginUser.getId());
        backboneReviewMeeting.setGuideTeacherCode(loginUser.getUsername());
        backboneReviewMeeting.setGuideTeacherRealname(loginUser.getRealname());
        backboneReviewMeeting.setCreateBy(loginUser.getId());
        backboneReviewMeeting.setCreateTime(new Date());
        this.save(backboneReviewMeeting);
    }

    @Override
    public BackboneReviewVo getReviewHeaderByLaunchIdAndDepId(String launchId, String depId) {
        return backboneReviewMeetingMapper.getReviewHeaderByLaunchIdAndDepId(launchId, depId);
    }

    /**
     * 发送消息
     * @param backboneReviewMeetingEntity
     */
    @Override
    public void sendMessage(BackboneReviewMeetingEntity backboneReviewMeetingEntity) {
        //查出所有此评议会下的申报人员
        List<BackboneLaunchApplyEntity> list = backboneLaunchApplyService.list(new QueryWrapper<BackboneLaunchApplyEntity>().eq("del_flag", 0).eq("launch_id", backboneReviewMeetingEntity.getLaunchId()).eq("dep_id", backboneReviewMeetingEntity.getDepId()));
        for (BackboneLaunchApplyEntity applyEntity : list) {
            String titleStu = "学时公示提醒";
            StringBuilder contentBuilderStu = new StringBuilder().append("有关学生骨干评议会：" + backboneReviewMeetingEntity.getMeetingTitle() + "，学时已公示，请及时查看");
            String remarkStu = "点击可查看详情";
            String userNoStu = applyEntity.getCreateBy();
            asyncHandel.sendWxMessage(userNoStu, titleStu, contentBuilderStu, remarkStu, "pagesB/projectpublic/projectpublic");
        }

    }

    @Override
    public Integer deleteByLaunchIdAndDepId(String launchId, String depId) {
        return backboneReviewMeetingMapper.deleteByLaunchIdAndDepId(launchId, depId);
    }

    @Override
    public List<BackboneReviewMeetingEntity> selectByLaAndDep(String launchId, String depId) {
        return backboneReviewMeetingMapper.selectByLaAndDep(launchId,depId);
    }
}
