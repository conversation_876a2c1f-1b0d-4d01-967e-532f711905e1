package com.zs.create.modules.communication.suggest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zs.create.modules.communication.suggest.entity.ScItemSuggestEntity;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 项目—我有意见Mapper层
 *
 * <AUTHOR> @email 
 * @date 2023-02-10 10:10:40
 * @Version: V1.0
 */
public interface ScItemSuggestMapper extends BaseMapper<ScItemSuggestEntity> {

    ScItemSuggestEntity getByUserAndItemId(@Param("itemId") String itemId, @Param("userCode")String userCode);

    List<String> getSecUserList();
}
