package com.zs.create.modules.communication.vote.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.DateUtils;
import com.zs.create.modules.communication.vote.constants.VoteConstants;
import com.zs.create.modules.communication.vote.entity.CpVoteConfigEntity;
import com.zs.create.modules.communication.vote.entity.CpVoteInfoEntity;
import com.zs.create.modules.communication.vote.entity.CpVoteUserEntity;
import com.zs.create.modules.communication.vote.entity.dto.CpVoteQueryDTO;
import com.zs.create.modules.communication.vote.entity.vo.CpVoteInfoVo;
import com.zs.create.modules.communication.vote.mapper.CpVoteInfoMapper;
import com.zs.create.modules.communication.vote.mapper.CpVoteUserMapper;
import com.zs.create.modules.communication.vote.service.*;
import com.zs.create.modules.delayqueue.core.DelayQueue;
import com.zs.create.modules.delayqueue.core.DelayQueueJob;
import com.zs.create.modules.item.service.AsynTaskFature;
import com.zs.create.modules.system.service.ISysAnnouncementService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

/**
 * @Description 投票Service实现层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-17 15:11:04
 * @Version: V1.0
 */
@Service
public class CpVoteInfoServiceImpl extends ServiceImpl<CpVoteInfoMapper, CpVoteInfoEntity> implements CpVoteInfoService {

    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    CpVoteOptionService cpVoteOptionService;
    @Autowired
    CpVoteConfigService cpVoteConfigService;
    @Autowired
    CpVoteUserService cpVoteUserService;
    @Autowired
    CpVoteUserMapper cpVoteUserMapper;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    ISysAnnouncementService sysAnnouncementService;
    @Autowired
    CpVoteService cpVoteService;
    @Autowired
    private AsynTaskFature asyncTaskFuture;
    /**
     * 保存投票基本信息
     * @param cpVoteInfoEntity
     * @return
     */
    @Override
    public Boolean saveCpVote(CpVoteInfoEntity cpVoteInfoEntity){
        if(CpVoteInfoEntity.VOTE_CHECK_RADIO.equals(cpVoteInfoEntity.getCheckType())){
            cpVoteInfoEntity.setCheckMin(1).setCheckMax(1); //单选默认赋值 最大选和最小选
        }

        if(CpVoteInfoEntity.VOTE_CHECK_CHECKBOX.equals(cpVoteInfoEntity.getCheckType())){
            if (cpVoteInfoEntity.getCheckMax() > 100){
                throw new ZsxcBootException("“最多选”不可大于100");
            }
            if(cpVoteInfoEntity.getCheckMin() == null) {
                cpVoteInfoEntity.setCheckMin(-1);
            }
            if(cpVoteInfoEntity.getCheckMax() == null) {
                cpVoteInfoEntity.setCheckMax(-1);
            }
        }

        boolean res = this.saveOrUpdate(cpVoteInfoEntity);
        String key = VoteConstants.VOTE_INFO_KEY_PREFIX + cpVoteInfoEntity.getId() ;
        if(res){
            redisTemplate.delete(key);
            //使用延迟队列首页展示系统消息
            try {
                Boolean flag = this.publishNotice(cpVoteInfoEntity);
                return flag;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 延迟队列发布通知消息
     * @param cpVoteInfoEntity
     * @return
     */
    private Boolean publishNotice(CpVoteInfoEntity cpVoteInfoEntity){
        deleteDelayQueueMessage(cpVoteInfoEntity.getId());
        long ttrTime = 20L;
        DelayQueueJob delayQueueJob = new DelayQueueJob();
        delayQueueJob.setTopic(VoteConstants.VOTE_START_MESSAGE_TOPIC);
        delayQueueJob.setDelayTime(cpVoteInfoEntity.getVoteSt().getTime());
        delayQueueJob.setMessage(cpVoteInfoEntity.getId()+"");
        delayQueueJob.setTtrTime(ttrTime);
        delayQueueJob.setId(cpVoteInfoEntity.getId());
        DelayQueue.push(delayQueueJob);
        return Boolean.TRUE;

    }


    /**
     *  删除延迟队列任务
     * @param voteId 投票voteId
     * @return
     */
    private void deleteDelayQueueMessage(Long voteId){
        DelayQueue.finish(voteId); // 延迟队列任务删除
        sysAnnouncementService.deleteById(VoteConstants.VOTE_START_MES_ID_PREFIX + voteId);
    }

    /**
     * 删除投票基本信息
     * @param id
     * @return
     */
    @Override
    public Boolean delelteCpVote(Long id) {
        boolean res =  this.removeById(id);
        String key = VoteConstants.VOTE_INFO_KEY_PREFIX + id ;
        if(res){
            redisTemplate.delete(key);
            deleteDelayQueueMessage(id);
            return Boolean.TRUE ;
        }
        return Boolean.FALSE;
    }



    /**
     * 查询投票基本信息
     * @param id
     * @return
     */
    @Override
    public CpVoteInfoEntity getOne(Long id){
        String key = VoteConstants.VOTE_INFO_KEY_PREFIX + id ;

        Map map = redisTemplate.opsForHash().entries(key);
        if(CollectionUtils.isEmpty(map)){
            CpVoteInfoEntity voteInfoFromDb = this.getById(id);
            if(null != voteInfoFromDb) {
                redisTemplate.opsForHash().putAll(key , BeanUtil.beanToMap(voteInfoFromDb));
                //保证当前时间-投票结束时间 都是有缓存的
                redisTemplate.expire(key , VoteConstants.getVoteCacheExpire(voteInfoFromDb.getVoteEt())
                        , TimeUnit.MILLISECONDS);
                return voteInfoFromDb;
            }else {
                throw new ZsxcBootException("该条数据已被删除，请刷新后重试。");
            }

        }else{
            CpVoteInfoEntity cpVoteInfoEntity = BeanUtil.mapToBean(map ,
                    CpVoteInfoEntity.class ,true);
            return cpVoteInfoEntity;
        }
    }


    /**
     *获取投票信息vo
     * @param id
     * @return
     */
    @Override
    public CpVoteInfoVo getVoteInfoVo(Long id){
        CpVoteInfoEntity one = this.getOne(id);
        if(null == one) return null;
        CpVoteInfoVo cpVoteInfoVo = new CpVoteInfoVo();
        BeanUtils.copyProperties(one , cpVoteInfoVo);
        voteRulesBuilder(cpVoteInfoVo);
        return cpVoteInfoVo;
    }





    /**
     * 投票规则产生器
     * @param cpVoteInfoVo
     */
    private void voteRulesBuilder(CpVoteInfoVo cpVoteInfoVo){
        List<String> rules = new ArrayList<>();
        CpVoteConfigEntity config = cpVoteConfigService.getByVoteId(cpVoteInfoVo.getId());
        if(CpVoteInfoEntity.VOTE_CHECK_RADIO.equals(cpVoteInfoVo.getCheckType())){
            rules.add("单选");
        }else{
            rules.add("多选" );
        }
        if(config == null) {
            config = new CpVoteConfigEntity().setShowVoteNum(0)
                    .setCanVoteNextDay(CpVoteConfigEntity.CAN_VOTE_NEXT_DAY_NO);
        }
        if(CpVoteInfoEntity.VOTE_CHECK_RADIO.equals(cpVoteInfoVo.getCheckType())){
            if(CpVoteConfigEntity.CAN_VOTE_NEXT_DAY_NO.equals(config.getCanVoteNextDay())){
                rules.add("每人可投一票" );
            }else{
                rules.add("每人每天可投一票" );
            }
        }else{
            String maxVoteNumStr = "无限制";
            if(!CpVoteInfoEntity.CHECK_NO_LIMIT.equals(cpVoteInfoVo.getCheckMax())) {
                maxVoteNumStr = cpVoteInfoVo.getCheckMax() + "票" ;
            }
            if(CpVoteConfigEntity.CAN_VOTE_NEXT_DAY_NO.equals(config.getCanVoteNextDay())){
                rules.add("每人最大可投" +maxVoteNumStr  );
            }else{
                rules.add("每人每天最大可投" + maxVoteNumStr );
            }

        }
        cpVoteInfoVo.setVoteRules(rules);
    }




    /**
     * 增加投票浏览量
     * @param id
     * @return
     */
    @Override
    public void addVisitNum(Long id){
        //有缓存说明可能是热点数据 双写
        if(redisTemplate.hasKey(VoteConstants.VOTE_INFO_KEY_PREFIX + id )){
            redisTemplate.opsForHash().increment(VoteConstants.VOTE_INFO_KEY_PREFIX + id ,"votedNum" , 1);
        }
        //todo mq异步跟新数据库visit_num
        return ;


    }



    /**
     * 分页查询
     * @param page
     * @param cpVoteQueryDTO
     * @return
     */
    @Override
    public IPage<CpVoteInfoEntity> pageList(Page<CpVoteInfoEntity> page, CpVoteQueryDTO cpVoteQueryDTO , LoginUser user) {
        QueryWrapper<CpVoteInfoEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.like(StringUtils.isNotBlank(cpVoteQueryDTO.getVoteTitle()) ,
                "vote_title" , cpVoteQueryDTO.getVoteTitle());

        if(CpVoteInfoEntity.VOTE_STATUS_UNSTART.equals(cpVoteQueryDTO.getVoteStatus())){
            queryWrapper.gt("vote_st" , LocalDateTime.now()); //大于
        }

        if(CpVoteInfoEntity.VOTE_STATUS_PROCESSING.equals(cpVoteQueryDTO.getVoteStatus())){
            //小于等于
            queryWrapper.le("vote_st" , LocalDateTime.now())
                    .gt("vote_et" , LocalDateTime.now());//大于

        }
        //进行中未参与
        if(CpVoteInfoEntity.VOTE_STATUS_NOT_PARTICIPATE.equals(cpVoteQueryDTO.getVoteStatus())){
            //小于等于
            queryWrapper.le("vote_st" , LocalDateTime.now())
                    .gt("vote_et" , LocalDateTime.now());//大于
            queryWrapper.notInSql("id" , "select vote_id from cp_vote_user where create_by = '" + user.getId() + "'");

        }

        if(CpVoteInfoEntity.VOTE_STATUS_FINISHED.equals(cpVoteQueryDTO.getVoteStatus())){
            queryWrapper.le("vote_et" , LocalDateTime.now()); //小于等于
        }

        //已完成
        if(CpVoteInfoEntity.VOTE_STATUS_PARTICIPATE.equals(cpVoteQueryDTO.getVoteStatus())){
            queryWrapper.le("vote_et" , LocalDateTime.now()); //小于等于
            queryWrapper.inSql("id" , "select vote_id from cp_vote_user where create_by = '" + user.getId() + "'");

        }

        queryWrapper.orderByDesc("create_time");

        IPage<CpVoteInfoEntity> queryPage = this.page(page, queryWrapper);
        return queryPage;
    }

    /**
     * 投票时间校验
     * @param cpVoteInfo
     */
    private void checkVoteTime(CpVoteInfoEntity cpVoteInfo){
        boolean isVoteProid = DateUtils.isEffectiveDate(new Date(), cpVoteInfo.getVoteSt(), cpVoteInfo.getVoteEt());
        if(!isVoteProid) throw new ZsxcBootException("非投票时间,不得投票");
    }




    /**
     * 校验optionId是否正确 检验参数是否正确 校验单选，多选投票数
     * @param cpVoteInfo
     * @param optionIds
     */
    private void checkOptIds(CpVoteInfoEntity cpVoteInfo ,  Set<Long> optionIds){
        if(CollectionUtils.isEmpty(optionIds)) throw new ZsxcBootException("optionIds 参数不正确");
        //optionIds 参数校验
        List<Long> opts = cpVoteOptionService.getOptionIdsByVoteId(cpVoteInfo.getId());
        if(CollectionUtils.isEmpty(opts)) throw new ZsxcBootException("投票数据错误");
        Set<Long> optionIdsByVoteId =new HashSet<>(opts) ;
        if(!optionIdsByVoteId.containsAll(optionIds)) throw new ZsxcBootException("optionIds 参数不正确");

        if(CpVoteInfoEntity.VOTE_CHECK_RADIO.equals(cpVoteInfo.getCheckType())){ //单选
            if(optionIds.size()>1) throw new ZsxcBootException("单选只能投1票");
        }

        if(CpVoteInfoEntity.VOTE_CHECK_CHECKBOX.equals(cpVoteInfo.getCheckType())){//多选
            Integer checkMin = cpVoteInfo.getCheckMin();
            Integer checkMax = cpVoteInfo.getCheckMax();
            Integer userVoteNum = optionIds.size();

            if(!CpVoteInfoEntity.CHECK_NO_LIMIT.equals(checkMin)){ //最小投有限制
                if(userVoteNum<checkMin) throw new ZsxcBootException( "最小投票数为：" + checkMin) ;
            }

            if(!CpVoteInfoEntity.CHECK_NO_LIMIT.equals(checkMax)){ //最大投有限制
                if(userVoteNum > checkMax) throw new ZsxcBootException( "最大投票数为：" + checkMax);
            }
        }



    }

    /**
     * 校验投票上限
     * @param cpvoteConfig
     * @param voteId
     * @param optionIds
     */
    private void checkVoteNumLimit(CpVoteConfigEntity cpvoteConfig , Long voteId , Set<Long> optionIds){
        if(cpvoteConfig!= null && cpvoteConfig.getVoteNumLimit() != null && cpvoteConfig.getVoteNumLimit()!= -1L){
            Long voteLimitNum =  cpvoteConfig.getVoteNumLimit();
            Long voteNum = redisTemplate.opsForValue().increment(VoteConstants.VOTE_NUM_PREFIX + voteId, optionIds.size());
            //校验投票上限
            if(voteNum>voteLimitNum) {
                redisTemplate.opsForValue().decrement(VoteConstants.VOTE_NUM_PREFIX + voteId, optionIds.size());
                throw new ZsxcBootException("投票数量已达上限:" + voteLimitNum);
            }
        }
    }

    /**
     * 初始化投票配置
     * @param voteId
     * @return
     */
    public CpVoteConfigEntity initCpVoteConfig(Long voteId){
        CpVoteConfigEntity cpvoteConfig = cpVoteConfigService.getByVoteId(voteId);
        //初始化
        if(null == cpvoteConfig){
            cpvoteConfig = new CpVoteConfigEntity().setCanVoteNextDay(CpVoteConfigEntity.CAN_VOTE_NEXT_DAY_NO)
                    .setShowVoteNum(0);
        }
        return cpvoteConfig;
    }

    @Override
    public Result<?> vote(Long voteId, Set<Long> optionIds , LoginUser user) throws ExecutionException, InterruptedException {
        //获取登录用户
        if(null == user) throw new ZsxcBootException("无用户身份");
        CpVoteInfoEntity cpVoteInfo = this.getOne(voteId);
        if(null == cpVoteInfo) throw new ZsxcBootException("voteId参数不正确");
        //初始化投票配置
        CpVoteConfigEntity cpVoteConfig = this.initCpVoteConfig(voteId);
        //校验投票时间
        this.checkVoteTime(cpVoteInfo);
        //校验optIds参数
        this.checkOptIds(cpVoteInfo , optionIds);
        //校验是否重复投票
        this.checkUserRepeatVote(cpVoteInfo , cpVoteConfig , optionIds, user);
        //校验用户投票数
        this.checkUserVoteNum(cpVoteInfo , cpVoteConfig , optionIds, user);
        //校验总投票上限
        this.checkVoteNumLimit(cpVoteConfig , voteId, optionIds);
        HttpServletRequest request = null;
        RequestAttributes requestAttributes = RequestContextHolder.getRequestAttributes();
        if(requestAttributes != null){
            request = ((ServletRequestAttributes)requestAttributes).getRequest();
        }
        Future<Boolean> futureResult =asyncTaskFuture.voteHandle(voteId, optionIds, user.getUsername(), request);
        Boolean voteSuccess;
        //Boolean voteSuccess=  cpVoteService.vote(voteId, optionIds, user.getUsername(), request) ;
        //获取任务执行结果
        while (true) {
            if (futureResult.isDone()) {
                voteSuccess = futureResult.get();
                break;
            }
        }
        Result<?> resSuccess = Result.ok(optionIds);
        resSuccess.setMessage("投票成功");
        return voteSuccess? resSuccess: Result.error("投票失败");
    }

    /**
     * 校验用户是否重复投票
     * @param cpVoteInfo
     * @param cpVoteConfig
     * @param optionIds
     * @param user
     */
    private void checkUserRepeatVote(CpVoteInfoEntity cpVoteInfo, CpVoteConfigEntity cpVoteConfig, Set<Long> optionIds , LoginUser user) {
        String tips = "您已投过选项";
        QueryWrapper<CpVoteUserEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("vote_id", cpVoteInfo.getId()).eq("create_by", user.getUsername());
        //每天可投
        if(CpVoteConfigEntity.CAN_VOTE_NEXT_DAY_YES.equals(cpVoteConfig.getCanVoteNextDay())){
            queryWrapper.eq("date(create_time)", DateUtils.getDate("yyyy-MM-dd"));
            tips = "您当天已投过选项";
        }
        queryWrapper.in("opt_id" , optionIds);
        int count = cpVoteUserService.count(queryWrapper);
        if(count > 0) throw new ZsxcBootException(tips);
    }

    /**
     * 校验用户投票数量
     * @param cpVoteInfo
     * @param cpVoteConfig
     * @param optionIds
     * @param user
     */
    private void checkUserVoteNum(CpVoteInfoEntity cpVoteInfo, CpVoteConfigEntity cpVoteConfig, Set<Long> optionIds , LoginUser user){
        String tips = "您已超出投票上限" ;
        QueryWrapper<CpVoteUserEntity> queryWrapper = new QueryWrapper();
        queryWrapper.eq("vote_id", cpVoteInfo.getId()).eq("create_by", user.getUsername());
        //每天可投
        if(CpVoteConfigEntity.CAN_VOTE_NEXT_DAY_YES.equals(cpVoteConfig.getCanVoteNextDay())){
            queryWrapper.eq("date(create_time)", DateUtils.getDate("yyyy-MM-dd"));
            tips = "您当天已超出投票上限" ;
        }

        int votedNum = cpVoteUserService.count(queryWrapper);
        if(votedNum + optionIds.size() > cpVoteInfo.getCheckMax()){
            throw new ZsxcBootException(tips);
        }
    }










}
