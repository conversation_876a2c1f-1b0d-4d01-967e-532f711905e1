<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.credit.mapper.OaCreditApplyMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.credit.entity.OaCreditApplyEntity" id="oaCreditApplyMap">
        <result property="id" column="id"/>
        <result property="courseId" column="course_id"/>
        <result property="topicTitle" column="topic_title"/>
        <result property="module" column="module"/>
        <result property="hours" column="hours"/>
        <result property="credit" column="credit"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="examineStatus" column="examine_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


    <select id="selectSignItemlist" resultType="com.zs.create.modules.oa.credit.entity.OaCreditApplyItemVo">
        select b.id,b.item_name as itemName
        from sc_item_registration a
        left join sc_item b on a.item_id=b.id
        left join oa_configure_item c on b.id=c.item_id
        where a.user_id=#{userId}
        and c.course_id=#{courseId}
        and a.del_flag='0'
        and b.del_flag='0'
        and c.del_flag='0'
    </select>

    <select id = "todoTaskPage" resultType="com.zs.create.modules.oa.credit.entity.OaCreditApplyEntity">
        SELECT cp.*,task.ID_ as task_id
        FROM act_ru_task task
        inner join act_ru_identitylink i on i.TASK_ID_ = task.ID_
        INNER JOIN oa_credit_apply cp ON task.PROC_INST_ID_ = cp.process_instance_id
        <where>
            and `task`.`SUSPENSION_STATE_` = 1
            <if test="taskQueryDTO.assignee != null and taskQueryDTO.assignee != ''">
                and (
                `task`.`ASSIGNEE_` = #{taskQueryDTO.assignee}
                or (
                `task`.`ASSIGNEE_` is null and `i`.TYPE_ = 'candidate' and (`i`.USER_ID_ = #{taskQueryDTO.assignee})
                ) )
            </if>

            <if test="taskQueryDTO.courseId != null and taskQueryDTO.courseId != ''">
                and cp.course_id = #{taskQueryDTO.courseId}
            </if>
            <if test="taskQueryDTO.userName != null and taskQueryDTO.userName != ''">
                and (cp.user_name like concat('%',#{taskQueryDTO.userName},'%') or cp.user_id like concat('%',#{taskQueryDTO.userName},'%'))
            </if>
            <if test="taskQueryDTO.module != null and taskQueryDTO.module != ''">
                and cp.module = #{taskQueryDTO.module}
            </if>
        </where>
        order by task.create_time_ desc
    </select>


    <select id = "historyTaskPage" resultType="com.zs.create.modules.oa.credit.entity.OaCreditApplyEntity">
        SELECT * FROM oa_credit_apply cp
        <where>
            <if test="taskQueryDTO.assignee != null and taskQueryDTO.assignee != ''">
                cp.process_instance_id
                in (select  proc_inst_id_ from act_hi_taskinst taskinst  where  taskinst.ASSIGNEE_ = #{taskQueryDTO.assignee})
            </if>
            <if test="taskQueryDTO.courseId != null and taskQueryDTO.courseId != ''">
                and cp.course_id = #{taskQueryDTO.courseId}
            </if>
            <if test="taskQueryDTO.userName != null and taskQueryDTO.userName != ''">
                 and (cp.user_name like concat('%',#{taskQueryDTO.userName},'%') or cp.user_id like concat('%',#{taskQueryDTO.userName},'%'))
            </if>
            <if test="taskQueryDTO.module != null and taskQueryDTO.module != ''">
                and cp.module = #{taskQueryDTO.module}
            </if>
        </where>
        order by cp.update_time desc
    </select>
    <select id="getCountUser" resultType="java.lang.Integer">
        SELECT
        COUNT(DISTINCT a.id)
        FROM
        (
        SELECT
        id,
        username,
        realname,
        type,
        CASE
        WHEN grade!="" THEN
        (case
        when #{xq}="1" then (year(now()) - grade+1)
        when #{xq}="2" then (year(now()) - grade)
        end )
        ELSE
        6
        END as nj
        FROM sys_user) a INNER JOIN sys_user_depart b ON a.id=b.user_id
        <where>
            <if test="nj != null and nj != ''">
                FIND_IN_SET(a.nj,#{nj})
            </if>
            <if test="userType != null and userType != ''">
                and a.type=#{userType}
            </if>
            <if test="userId != null and userId != ''">
                and a.id=#{userId}
            </if>
            <if test="list !=null and list.size()>0">
                AND b.dep_id in
                <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
    </select>


</mapper>