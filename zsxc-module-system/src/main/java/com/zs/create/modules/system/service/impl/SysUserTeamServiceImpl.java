package com.zs.create.modules.system.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.item.enums.HonorStatusEnum;
import com.zs.create.modules.item.utils.QRCodeTool;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.entity.SysUserDepart;
import com.zs.create.modules.system.entity.SysUserTeamEntity;
import com.zs.create.modules.system.mapper.SysUserTeamMapper;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.system.service.ISysUserDepartService;
import com.zs.create.modules.system.service.ISysUserService;
import com.zs.create.modules.system.service.SysUserTeamService;
import com.zs.create.modules.system.util.HttpClientUtil;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import com.zs.create.modules.workflow.instance.service.impl.ProcessInstanceService;
import com.zs.create.modules.workflow.service.IWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ActivitiTaskAlreadyClaimedException;
import org.activiti.engine.IdentityService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description 我的社团Service实现层
 * <AUTHOR>
 * @email null
 * @date 2020-09-18 15:17:17
 * @Version: V1.0
 */
@Service
@Slf4j
public class SysUserTeamServiceImpl extends ServiceImpl<SysUserTeamMapper, SysUserTeamEntity> implements SysUserTeamService {


    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    IdentityService identityService;
    @Autowired
    RuntimeService runtimeService;
    @Autowired
    TaskService taskService;
    @Autowired
    ProcessInstanceService processInstanceService;
    @Autowired
    IWorkflowService workflowService;
    @Autowired
    WxMessageSender wxMessageSender;
    @Autowired
    SysWeixinUserService sysWeixinUserService;
    @Autowired
    private ISysUserDepartService sysUserDepartService;
    @Autowired
    private CampusAppService campusAppService;
    @Autowired
    private SysUserTeamMapper sysUserTeamMapper;

    @Value("${qrUrl}")
    private String qrUrl;

    @Value("${gzhAppid}")
    private String appid;

    @Value("${gzhSecret}")
    private String secret;

    private static final Integer width = 400;
    private static final Integer height = 400;


    @Override
    public Map<String , Object> getStaticQrCode(String depId) throws UnsupportedEncodingException {
        SysDepart sysDepart = sysDepartService.getById(depId);
        if(null == sysDepart) throw new ZsxcBootException("参数错误：未查询到社团信息");

        String qrContent = this.getQrUrl(depId);
        String imageForBase64 = null;
        try {
            imageForBase64 = QRCodeTool.createImageForBase64(qrContent , width , height);
        } catch (Exception e) {
            log.error("二维码生成失败");
        }
        Map<String ,Object> resMap = new HashMap<>();
        resMap.put("qrCode" , imageForBase64);

        return resMap;
    };


    private String getQrUrl( String depId) throws UnsupportedEncodingException {
        String wxRedirectUri =qrUrl +"/mobile/team/wx/" + depId;
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="
                + appid + "&redirect_uri=" + URLEncoder.encode(wxRedirectUri , "utf-8")
                + "&response_type=code&scope=snsapi_userinfo&state=123#wechat_redirect";
        return url;
    }

    @Override
    @Transactional
    public String joinTeam(String depId ,String code){
        SysDepart sysDepart=sysDepartService.getById(depId);
        if (sysDepart==null) throw new ZsxcBootException("系统内部错误,请联系管理员：depId参数有误");
        String unionId = this.getUnionId(code);
        if(StringUtils.isBlank(unionId)) throw new ZsxcBootException("系统内部错误,请联系管理员：未获取微信unionId");
        SysUser user;
        try{
            user = sysUserService.getUserByUnionId(unionId);
        }catch (Exception e){
            throw new ZsxcBootException("系统内部错误,请联系管理员：根据unionId查询用户失败");
        }
        if(null == user) throw new ZsxcBootException("扫码失败，请先登录微信小程序或重新关注公众号再试");

        return user.getId();
    };


    @Override
    @Transactional
    public Boolean signUpTeam(SysUserTeamEntity teamEntity){
        SysDepart sysDepart=sysDepartService.getById(teamEntity.getDepId());
        if (sysDepart==null) throw new ZsxcBootException("部门id不正确，请确认");
        teamEntity.setDepName(sysDepart.getDepartName());
        this.save(teamEntity);

        startTeamProcess(teamEntity , teamEntity.getUserId(),sysDepart.getPersonInCharge());
        return true;
    }


    @Override
    @Transactional
    public Boolean signOutTeam(SysUserTeamEntity teamEntity){
        SysDepart sysDepart=sysDepartService.getById(teamEntity.getDepId());
        if (sysDepart==null) throw new ZsxcBootException("部门id不正确，请确认");
        teamEntity.setDepName(sysDepart.getDepartName());
        this.save(teamEntity);

        startOutTeamProcess(teamEntity , teamEntity.getUserId(),sysDepart.getPersonInCharge());
        return true;
    }



    /**
     * 获取微信unionId
     * @param code
     * @return
     */
    public String getUnionId(String code){
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code";
        String path = url.replace("APPID", appid).replace("SECRET", secret).replace("CODE", code);
        String s = HttpClientUtil.doPost(path);
        Map<String, String> hash = (Map<String, String>) JSON.parse(s);
        String access_token = hash.get("access_token");
        String openid = hash.get("openid");

        String userInfoApi = "https://api.weixin.qq.com/sns/userinfo?access_token=" + access_token + "&openid=" + openid + "&lang=zh_CN";
        String user = HttpClientUtil.doGet(userInfoApi);
        Map<String, String> userMap = (Map<String, String>) JSON.parse(user);
        return userMap.get("unionid");
    }


    /**
     * 启动流程实例
     */
    public ProcessInstance startTeamProcess(SysUserTeamEntity userTeamEntity , String currentUserId,String personInCharge) {
        //创建流程变量
        Map<String, Object> variables = new HashMap<>(2);
        variables.put("personInCharge", personInCharge);
        //启动流程实例
        identityService.setAuthenticatedUserId(currentUserId);
        ProcessInstance processInstance =
                runtimeService.startProcessInstanceByKey(SysUserTeamEntity.processDefinitionKey, userTeamEntity.getId(), variables);
        //自动审核第一个申请任务
        List<Task> tasks = taskService.createTaskQuery().active()
                .processInstanceId(processInstance.getId())
                .orderByTaskCreateTime().asc().list();//通过流程实例获取正在执行的任务
        if (!CollectionUtils.isEmpty(tasks)) {
            for (Task task : tasks) {
                auditHonorProcess(task.getId() , "申请" ,  "--"+currentUserId , null );
            }
        }
        //更新项目表数据【状态,冗余processInstanceId 方便查询】
        userTeamEntity.setStatus(SysUserTeamEntity.APPLY).setProcessInstanceId(processInstance.getId());
        this.updateById(userTeamEntity);
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstance.getId()));
        return processInstance;
    }




    /**
     * 启动流程实例
     */
    public ProcessInstance startOutTeamProcess(SysUserTeamEntity userTeamEntity , String currentUserId,String personInCharge) {
        //创建流程变量
        Map<String, Object> variables = new HashMap<>(2);
        variables.put("personInCharge", personInCharge);
        //启动流程实例
        identityService.setAuthenticatedUserId(currentUserId);
        ProcessInstance processInstance =
                runtimeService.startProcessInstanceByKey(SysUserTeamEntity.processDefinitionKey, userTeamEntity.getId(), variables);
        //自动审核第一个申请任务
        List<Task> tasks = taskService.createTaskQuery().active()
                .processInstanceId(processInstance.getId())
                .orderByTaskCreateTime().asc().list();//通过流程实例获取正在执行的任务
        if (!CollectionUtils.isEmpty(tasks)) {
            for (Task task : tasks) {
                auditOutProcess(task.getId() , "退出" ,  "--"+currentUserId , null );
            }
        }
        //更新项目表数据【状态,冗余processInstanceId 方便查询】
        userTeamEntity.setStatus(SysUserTeamEntity.APPLY).setProcessInstanceId(processInstance.getId());
        this.updateById(userTeamEntity);
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstance.getId()));
        return processInstance;
    }





    @Override
    @Transactional
    public String auditHonorProcess(String taskId, String auditNote,
                                  String assignee, Map<String, Object> variables) {
        Task task = taskService.createTaskQuery().taskId(taskId).active().singleResult();
        if (null == task) throw new ZsxcBootException("当前任务不存在或已经被审核");
        String Key = getTaskBusinessKey(task);
        try {
            taskService.claim(taskId, assignee);
            taskService.addComment(taskId, task.getProcessInstanceId(), auditNote);
            taskService.complete(taskId, variables);
            //流程结束修改状态
            modifyBussinessStatus(task.getProcessInstanceId() , Key , variables,auditNote);
            return task.getProcessInstanceId();
            // 结束
        } catch (ActivitiTaskAlreadyClaimedException e) {
            throw new ZsxcBootException("任务已被审核");
        }
    }



    @Override
    @Transactional
    public String auditOutProcess(String taskId, String auditNote,
                                  String assignee, Map<String, Object> variables) {
        Task task = taskService.createTaskQuery().taskId(taskId).active().singleResult();
        if (null == task) throw new ZsxcBootException("当前任务不存在或已经被审核");
        String Key = getTaskBusinessKey(task);
        try {
            taskService.claim(taskId, assignee);
            taskService.addComment(taskId, task.getProcessInstanceId(), auditNote);
            taskService.complete(taskId, variables);
            //流程结束修改状态
            modifyBussinessOut(task.getProcessInstanceId() , Key , variables,auditNote);
            return task.getProcessInstanceId();
        } catch (ActivitiTaskAlreadyClaimedException e) {
            throw new ZsxcBootException("任务已被审核");
        }
    }



    /**
     * 根据task 获取业务id
     * @param task
     * @return
     */
    private String getTaskBusinessKey(Task task){
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId()).active().singleResult();
        return processInstance.getBusinessKey();
    }

    /**
     * 判断流程是否结束
     * @param processInstanceId
     * @return
     */
    public Boolean judgeProcessIsEnd(String processInstanceId){
        return  processInstanceService.judgeProcessIsEnd(processInstanceId);
    }



    public void modifyBussinessStatus(String processInstanceId , String businessKey , Map variables,String auditNote){
        if(judgeProcessIsEnd(processInstanceId)){
            if(null != variables && SysUserTeamEntity.PROCESS_REJECT.equals(variables.get("status"))){ //驳回结束
                SysUserTeamEntity entity = this.getById(businessKey);
                entity.setStatus(SysUserTeamEntity.AUDIT_REJECT);
                this.updateById(entity);
                sendAuditResult2ScoreCreateUser(entity,SysUserTeamEntity.AUDIT_REJECT,auditNote);
            } else  { //审核通过
                SysUserTeamEntity entity = this.getById(businessKey);
                Assert.notNull(entity , "社团信息不存在");
                entity.setStatus(SysUserTeamEntity.AUDIT_PASS);
                this.updateById(entity);
                //关联社团与用户关系
                SysUserDepart userDepart=new SysUserDepart(null,entity.getUserId(),entity.getDepId());
                sysUserDepartService.save(userDepart);
                sendAuditResult2ScoreCreateUser(entity,SysUserTeamEntity.AUDIT_PASS,auditNote);
            }
        }/*else {
            //流程未结束
            SysUserTeamEntity entity = this.getById(businessKey);
            processNotEndMes(processInstanceId , entity);
        }*/
    }


    public void modifyBussinessOut(String processInstanceId , String businessKey , Map variables,String auditNote){
        if(judgeProcessIsEnd(processInstanceId)){
            if(null != variables && SysUserTeamEntity.PROCESS_REJECT.equals(variables.get("status"))){ //驳回结束
                SysUserTeamEntity entity = this.getById(businessKey);
                entity.setStatus(SysUserTeamEntity.AUDIT_REJECT);
                this.updateById(entity);
                sendAuditResult2ScoreCreateUserOut(entity,SysUserTeamEntity.AUDIT_REJECT,auditNote);
            } else  { //审核通过
                SysUserTeamEntity entity = this.getById(businessKey);
                Assert.notNull(entity , "社团信息不存在");
                entity.setStatus(SysUserTeamEntity.AUDIT_PASS);
                this.updateById(entity);
                //删除社团与用户关系
                QueryWrapper<SysUserDepart> userDepartQueryWrapper=new QueryWrapper<>();
                userDepartQueryWrapper.eq("user_id",entity.getUserId());
                userDepartQueryWrapper.eq("dep_id",entity.getDepId());
                SysUserDepart depart=sysUserDepartService.getOne(userDepartQueryWrapper);
                sysUserDepartService.removeById(depart);
                //删除报名记录
                sysUserTeamMapper.deleteUserAndDep(entity.getUserId(),entity.getDepId());


                sendAuditResult2ScoreCreateUserOut(entity,SysUserTeamEntity.AUDIT_PASS,auditNote);
            }
        }

    }



    /**
     * 给项目创建人发送审核结果消息
     */
    public  void sendAuditResult2ScoreCreateUser(SysUserTeamEntity entity , Integer auditResult,String auditNote) {
        if (null != entity) {
            String createUserId = entity.getUserId();
            String title = "社团成员审核提醒";
            StringBuilder contentBuilder = new StringBuilder();
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(createUserId, appid);
            if (null == wxMesUserInfo) {
                log.error("给社团报名人发送审核结果消息，未找到接收人：{} 的信息", createUserId);
                return;
            }
            String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的工作提醒";
            String openId = wxMesUserInfo.getOpenId();
            if (HonorStatusEnum.AUDIT_PASS.getCode().equals(auditResult)) {
                contentBuilder.append("您申请的加入社团信息，");
                contentBuilder.append("已经审核通过，审核意见："+auditNote);

            } else {
                contentBuilder.append("您申请的加入社团信息，");
                contentBuilder.append("已经被驳回，审核意见："+auditNote);
            }

            String remark = "青春科大智慧团学综合信息平台";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(createUserId)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);

        }
    }



    /**
     * 给项目创建人发送审核结果消息
     */
    public  void sendAuditResult2ScoreCreateUserOut(SysUserTeamEntity entity , Integer auditResult,String auditNote) {
        if (null != entity) {
            String createUserId = entity.getUserId();
            String title = "社团成员审核提醒";
            StringBuilder contentBuilder = new StringBuilder();
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(createUserId, appid);
            if (null == wxMesUserInfo) {
                log.error("给社团报名人发送审核结果消息，未找到接收人：{} 的信息", createUserId);
                return;
            }
            String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的工作提醒";
            String openId = wxMesUserInfo.getOpenId();
            if (HonorStatusEnum.AUDIT_PASS.getCode().equals(auditResult)) {
                contentBuilder.append("您申请的退出社团信息，");
                contentBuilder.append("已经审核通过，审核意见："+auditNote);

            } else {
                contentBuilder.append("您申请的退出社团信息，");
                contentBuilder.append("已经被驳回，审核意见:"+auditNote);
            }

            String remark = "青春科大智慧团学综合信息平台";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(createUserId)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }


    /**
     * 给代办人发送待办任务消息
     */
    public void sendTodoTaskMes2candidateUser(SysUserTeamEntity entity , String reciveUserId){
        if(null != entity){
            String title = "社团成员审核提醒";
            StringBuilder contentBuilder = new StringBuilder()
                    .append("有一条新的社团成员信息,")
                    .append("等待您的审核，请及时处理。");
            String remark = "青春科大智慧团学综合信息平台";
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(reciveUserId, appid);
            if(null == wxMesUserInfo){
                log.error("给代办人发送待办任务消息，未找到接收人：{} 的信息", reciveUserId);
                return;
            }
            String openId = wxMesUserInfo.getOpenId();
            String theme = wxMesUserInfo.getRealname() + "/"+ wxMesUserInfo.getUsername()+",您有一条新的工作提醒";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(reciveUserId)
                    .setCreateDate(new Date())
                    //.setMiniAppUrl("pagesB/auditAssociation/auditAssociation")
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }

    @Override
    public void sendTodoTaskMesCurrentUser(SysUserTeamEntity teamEntity, String userId) {
        if(null != teamEntity){
            String title = "社团加入提醒";
            StringBuilder contentBuilder = new StringBuilder()
                    .append("您申请加入的社团："+teamEntity.getDepName())
                    .append("等待社团负责人审核中，请耐心的等待。");
            String remark = "审核结束后会再次给您发送消息提醒";
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(userId, appid);
            if(null == wxMesUserInfo){
                log.error("给代办人发送待办任务消息，未找到接收人：{} 的信息", userId);
                return;
            }
            String openId = wxMesUserInfo.getOpenId();
            String theme = wxMesUserInfo.getRealname() + "/"+ wxMesUserInfo.getUsername()+",您有一条新的工作提醒";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(userId)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }

    @Override
    public void sendTodoTaskMes2CurrentUser(SysUserTeamEntity teamEntity, String userId) {
        if(null != teamEntity){
            String title = "社团退出提醒";
            StringBuilder contentBuilder = new StringBuilder()
                    .append("您申请退出的社团："+teamEntity.getDepName())
                    .append("等待社团负责人审核中，请耐心的等待。");
            String remark = "审核结束后会再次给您发送消息提醒";
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(userId, appid);
            if(null == wxMesUserInfo){
                log.error("给代办人发送待办任务消息，未找到接收人：{} 的信息", userId);
                return;
            }
            String openId = wxMesUserInfo.getOpenId();
            String theme = wxMesUserInfo.getRealname() + "/"+ wxMesUserInfo.getUsername()+",您有一条新的工作提醒";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(userId)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }


    @Override
    public Page<SysUserTeamEntity> taskPage(SysUserTeamEntity entity, Integer pageNo, Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        entity.setAssignee(sysUser.getId());
        Page<SysUserTeamEntity> taskVoPage = null;
        if(SysUserTeamEntity.QUERY_HISTORY.equals(entity.getQueryType())){
            taskVoPage = this.historyTaskPage(entity, pageNo, pageSize);
        }
        if(SysUserTeamEntity.QUERY_TODO.equals(entity.getQueryType())){
            taskVoPage = this.todoTaskPage(entity , pageNo ,pageSize);
        }
        return taskVoPage;
    }

    /**
     * 代办任务分页
     * @param
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<SysUserTeamEntity> todoTaskPage(SysUserTeamEntity entity
            , Integer pageNo
            , Integer pageSize){
        Page<SysUserTeamEntity> page = new Page<SysUserTeamEntity>(pageNo, pageSize);
        page.setRecords(sysUserTeamMapper.todoTaskPage(page , entity));
        return page;

    }


    /**
     * 历史任务分页
     * @param
     * @param pageNo
     * @param pageSize
     * @return
     */
    public Page<SysUserTeamEntity> historyTaskPage(SysUserTeamEntity entity
            , Integer pageNo
            , Integer pageSize){
        Page<SysUserTeamEntity> page = new Page<>(pageNo, pageSize);
        page.setRecords(sysUserTeamMapper.historyTaskPage(page ,entity));
        return page;
    }


    public Integer getJoinTeamDepCnt(String userId){
        return sysUserTeamMapper.getJoinTeamDepCnt(userId);
    }

}
