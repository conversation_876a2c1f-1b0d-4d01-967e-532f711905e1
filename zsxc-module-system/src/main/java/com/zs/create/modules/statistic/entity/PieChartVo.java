package com.zs.create.modules.statistic.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * @Author: lzd
 * @Date: 2021/2/25 15:48
 * 首页饼状图返回实体
 */
@Data
@Accessors(chain = true)
public class PieChartVo implements Serializable {
    private String module;  //模块
    private String xms;   //项目发布数
    private String bmrc;   //报名人次
    private String cyrc;   //参与人次

    public PieChartVo(String module, String xms, String bmrc, String cyrc) {
        this.module = module;
        this.xms = xms;
        this.bmrc = bmrc;
        this.cyrc = cyrc;
    }
}
