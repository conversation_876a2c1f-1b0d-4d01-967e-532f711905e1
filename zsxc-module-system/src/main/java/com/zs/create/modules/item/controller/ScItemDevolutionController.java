package com.zs.create.modules.item.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.entity.ScItemDevolutionEntity;
import com.zs.create.modules.item.service.ScItemDevolutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * @Description 签到签退二维码权力下放Controller层
 *
 * <AUTHOR>
 * @email null
 * @date 2020-12-24 10:24:48
 * @Version: V1.0
 */
@Slf4j
@Api(tags="签到签退二维码权力下放")
@RestController
@RequestMapping("/item/scItemDevolution")
public class ScItemDevolutionController {
    @Autowired
    private ScItemDevolutionService scItemDevolutionService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "签到签退二维码权力下放-分页列表查询")
    @ApiOperation(value="签到签退二维码权力下放-分页列表查询", notes="签到签退二维码权力下放-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ScItemDevolutionEntity>> queryPageList(ScItemDevolutionEntity scItemDevolution,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<ScItemDevolutionEntity>> result = new Result<>();
        QueryWrapper<ScItemDevolutionEntity> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(scItemDevolution.getRealname()))
            queryWrapper.and(i->i.like("realname",scItemDevolution.getRealname()).or().like("username",scItemDevolution.getRealname()));
        if (StringUtils.isNotBlank(scItemDevolution.getUsername()))
            queryWrapper.like("username",scItemDevolution.getUsername());
        queryWrapper.eq("item_id",scItemDevolution.getItemId());
        queryWrapper.groupBy("user_id", "item_id");
        Page<ScItemDevolutionEntity> page = new Page<>(pageNo, pageSize);
        IPage<ScItemDevolutionEntity> pageList = scItemDevolutionService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "签到签退二维码权力下放-添加")
    @ApiOperation(value="签到签退二维码权力下放-添加", notes="签到签退二维码权力下放-添加")
    @PostMapping(value = "/add")
    public Result<ScItemDevolutionEntity> add(@RequestParam(name="userIds",required=true) String userIds,
                                              @RequestParam(name="itemId",required=true) String itemId) {
        Result<ScItemDevolutionEntity> result = new Result<>();

            try {
                scItemDevolutionService.add(userIds,itemId);
                result.success("添加成功！");
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                result.error500("操作失败");
            }
//        }
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "签到签退二维码权力下放-编辑")
    @ApiOperation(value="签到签退二维码权力下放-编辑", notes="签到签退二维码权力下放-编辑")
    @PutMapping(value = "/edit")
    public Result<ScItemDevolutionEntity> edit(@RequestBody ScItemDevolutionEntity scItemDevolution) {
        Result<ScItemDevolutionEntity> result = new Result<>();
        ScItemDevolutionEntity scItemDevolutionEntity = scItemDevolutionService.getById(scItemDevolution.getId());
        if(scItemDevolutionEntity==null) {
            result.error500("未找到对应实体");
        }else {
            boolean ok = scItemDevolutionService.updateById(scItemDevolution);
            if(ok) {
                result.success("修改成功!");
            }
        }
        return result;
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "签到签退二维码权力下放-通过id删除")
    @ApiOperation(value="签到签退二维码权力下放-通过id删除", notes="签到签退二维码权力下放-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        ScItemDevolutionEntity byId = scItemDevolutionService.getById(id);

        if(byId == null) throw new RuntimeException("项目id参数异常！");

        try {
            Wrapper<ScItemDevolutionEntity> wrapper = Wrappers.<ScItemDevolutionEntity>lambdaQuery()
                    .eq(ScItemDevolutionEntity::getItemId, byId.getItemId())
                    .eq(ScItemDevolutionEntity::getUserId, byId.getUserId());
            scItemDevolutionService.remove(wrapper);
        } catch (Exception e) {
            log.error("删除失败",e.getMessage());
            return Result.error("删除失败!");
        }
		return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "签到签退二维码权力下放-批量删除")
    @ApiOperation(value="签到签退二维码权力下放-批量删除", notes="签到签退二维码权力下放-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<ScItemDevolutionEntity> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<ScItemDevolutionEntity> result = new Result<>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.scItemDevolutionService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "签到签退二维码权力下放-通过id查询")
    @ApiOperation(value="签到签退二维码权力下放-通过id查询", notes="签到签退二维码权力下放-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScItemDevolutionEntity> queryById(@RequestParam(name="id",required=true) String id) {
        Result<ScItemDevolutionEntity> result = new Result<>();
        ScItemDevolutionEntity scItemDevolution = scItemDevolutionService.getById(id);
        if(scItemDevolution==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(scItemDevolution);
            result.setSuccess(true);
        }
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<ScItemDevolutionEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                ScItemDevolutionEntity scItemDevolution = JSON.parseObject(deString, ScItemDevolutionEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(scItemDevolution, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<ScItemDevolutionEntity> pageList = scItemDevolutionService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "签到签退二维码权力下放列表");
        mv.addObject(NormalExcelConstants.CLASS, ScItemDevolutionEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("签到签退二维码权力下放列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ScItemDevolutionEntity> listScItemDevolutions = ExcelImportUtil.importExcel(file.getInputStream(), ScItemDevolutionEntity.class, params);
                scItemDevolutionService.saveBatch(listScItemDevolutions);
                return Result.ok("文件导入成功！数据行数:" + listScItemDevolutions.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
