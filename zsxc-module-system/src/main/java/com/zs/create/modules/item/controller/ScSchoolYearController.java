package com.zs.create.modules.item.controller;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.modules.item.entity.ScSchoolYearEntity;
import com.zs.create.modules.item.service.ScSchoolYearService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;


/**
 * 成绩单时间配置表Controller层
 *
 * <AUTHOR>
 */
@Slf4j
@Api(tags="项目-成绩单时间配置")
@RestController
@RequestMapping("/item/year")
public class ScSchoolYearController {

    @Autowired
    private ScSchoolYearService schoolYearService;


    /**
     * 分页列表查询
     */
    @AutoLog(value = "成绩单时间配置-列表")
    @ApiOperation(value="成绩单时间配置-列表", notes="成绩单时间配置-列表")
    @GetMapping(value = "/list")
    public Result<IPage<ScSchoolYearEntity>> pageList (ScSchoolYearEntity schoolYearEntity,
                                                       @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                       @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                       HttpServletRequest req) {
        Result<IPage<ScSchoolYearEntity>> result = new Result<>();
        Page<ScSchoolYearEntity> page  = new Page<>(pageNo, pageSize);
        IPage<ScSchoolYearEntity> pageList = schoolYearService.page(page);
        result.setResult(pageList);
        result.setSuccess(true);
        return result;
    }

    /**
     * 新增
     */
    @AutoLog(value = "成绩单时间配置-新增")
    @ApiOperation(value="成绩单时间配置-新增", notes="成绩单时间配置-新增")
    @PostMapping(value = "/add")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<ScSchoolYearEntity> add(@RequestBody ScSchoolYearEntity schoolYearEntity) {
        Result<ScSchoolYearEntity> result = new Result<>();
        try {
            schoolYearService.add(schoolYearEntity);
            result.setSuccess(true);
            result.setMessage("添加成功！");
        } catch (ZsxcBootException e) {
            result.error500(e.getMessage());
        } catch (Exception e) {
            result.error500("添加失败!");
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * 编辑
     */
    @AutoLog(value = "成绩单时间配置-编辑")
    @ApiOperation(value="成绩单时间配置-编辑", notes="成绩单时间配置-编辑")
    @PutMapping(value = "/edit")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<ScSchoolYearEntity> edit(@RequestBody ScSchoolYearEntity schoolYearEntity) {
        Result<ScSchoolYearEntity> result = new Result<>();
        try {
            schoolYearService.edit(schoolYearEntity);
            result.setSuccess(true);
            result.setMessage("操作成功！");
        } catch (ZsxcBootException e) {
            result.error500(e.getMessage());
        } catch (Exception e) {
            result.error500("操作失败!");
            log.error(e.getMessage());
        }
        return result;
    }

    /**
     * 删除
     */
    @AutoLog(value = "成绩单时间配置-删除")
    @ApiOperation(value="成绩单时间配置-删除", notes="成绩单时间配置-删除")
    @DeleteMapping(value = "/delete")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> delete(@RequestParam(name = "id")String id ) {
        try {
            schoolYearService.removeById(id);
            return Result.ok("删除成功!");
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.error("删除失败!");
        }
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "成绩单时间配置-批量删除")
    @ApiOperation(value="成绩单时间配置-批量删除", notes="成绩单时间配置-批量删除")
    @DeleteMapping(value = "/batchDelete")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> batchDelete(@RequestParam(name = "ids")String ids ) {
        try {
            schoolYearService.deleteByIds(ids);
            return Result.ok("删除成功!");
        } catch (Exception e) {
            log.error(e.getMessage());
            return Result.error("删除失败!");
        }
    }

    /**
     * 选择参数设计学年
     */
    @AutoLog(value = "成绩单时间配置-选择参数设计学年")
    @ApiOperation(value="成绩单时间配置-选择参数设计学年", notes="成绩单时间配置-选择参数设计学年")
    @GetMapping(value = "/checkWeeks")
    public Result<List<String>> checkWeeks () {
        Result<List<String>> result = new Result<>();
        List<String> list = schoolYearService.checkWeeks();
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }

}
