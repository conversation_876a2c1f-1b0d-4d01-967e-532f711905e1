package com.zs.create.modules.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.item.entity.ItemSignDTO;
import com.zs.create.modules.item.entity.ItemSignHourDTO;
import com.zs.create.modules.item.entity.ScItemSignEntity;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description 项目签到签退Service层
 * @email <EMAIL>
 * @date 2020-07-05 20:53:13
 * @Version: V1.0
 */
public interface ScItemSignService extends IService<ScItemSignEntity> {

    ItemSignDTO getSignBasicInfo(String itemId, Integer type);

    Map<String, Object> getStaticQrCode(String itemId, Integer type) throws UnsupportedEncodingException;

    Boolean sign(String itemId, Integer type, String signTag, String qrCodeType, String code);

    String getDynamicQrCode(String itemId, Integer type) throws UnsupportedEncodingException;

    Boolean modifyQrCodeStatus(String itemId, Integer type);

    List<ItemSignHourDTO> listUserItemSignHours(String itemId, List<String> userIds);

    BigDecimal getSignHours(List<ScItemSignEntity> userItemSignRecords);

    BigDecimal getSignHours(String itemId, String userId);

    String getUnionId(String code);

    Boolean signDevolution(String itemId, String userIds);

    List<String> partUser(String itemId, List<String> ids);
}

