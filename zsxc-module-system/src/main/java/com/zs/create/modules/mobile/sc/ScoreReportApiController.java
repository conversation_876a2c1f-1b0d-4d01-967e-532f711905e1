package com.zs.create.modules.mobile.sc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.item.entity.ScItemHoursEntity;
import com.zs.create.modules.item.service.ScItemHoursService;
import com.zs.create.modules.personal.entity.ScExperienceEntity;
import com.zs.create.modules.personal.service.ScExperienceService;
import com.zs.create.modules.score.dto.UserScoreDto;
import com.zs.create.modules.score.entity.ScScoreReportEntity;
import com.zs.create.modules.score.service.ScScoreReportService;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * @createUser hy
 * @createTime 2020-7-27
 * @description 成绩单
 */
@Slf4j
@Api(tags="手机端-成绩单api")
@RestController
@RequestMapping("/mobile/scorereport")
public class ScoreReportApiController {

    @Autowired
    @Lazy
    ScItemHoursService scItemHoursService;
    @Autowired
    @Lazy
    private ScScoreReportService scScoreReportService;
    @Autowired
    private ScExperienceService scExperienceService;
    @Autowired
    @Lazy
    private ISysUserService sysUserService;

    /**
     * 成绩单背面学时
     */
    @AutoLog(value = "手机端成绩单背面学时")
    @ApiOperation(value="手机端成绩单背面学时", notes="手机端成绩单背面学时")
    @GetMapping(value = "/userScoreHours")
    public Result<List<Map<String,Object>>> userScoreHours(){
        Result<List<Map<String,Object>>> result = new Result<List<Map<String,Object>>>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<Map<String,Object>>list=scItemHoursService.queryApiUserScoreHours(sysUser.getId());
        result.setSuccess(true);
        result.setResult(list);
        return result;
    }


    /**
     * 分页列表查询
     */
    @AutoLog(value = "成绩单-查看用户详情")
    @ApiOperation(value="成绩单-查看用户详情", notes="成绩单-查看用户详情")
    @GetMapping(value = "/queryByUserId")
    public Result<SysUser> queryByUserId() {
        Result<SysUser> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysUser user=sysUserService.getById(sysUser.getId());
        result.setSuccess(true);
        result.setResult(user);
        return result;
    }


    /**
     * 查看学时，星级，占比
     */
    @AutoLog(value = "成绩单-查看学时，星级，占比")
    @ApiOperation(value="成绩单-查看学时，星级，占比", notes="查看学时，星级，占比")
    @GetMapping(value = "/getModuleScoreAndHours")
    public Result<Map<String,Object>> getModuleScoreAndHours(){
        Result<Map<String,Object>> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SysUser user=sysUserService.getById(sysUser.getId());
        Map<String,Object> map=scScoreReportService.scoreUser(user);
        result.setSuccess(true);
        result.setResult(map);
        return result;
    }


    @AutoLog(value = "手机端成绩单-五育雷达图")
    @ApiOperation(value="手机端成绩单-五育雷达图", notes="手机端成绩单-五育雷达图")
    @GetMapping(value = "/wyEcharts")
    public Result<ScScoreReportEntity> wyEcharts(String userId) {
        Result<ScScoreReportEntity> result = new Result<>();
        QueryWrapper<ScScoreReportEntity> queryWrapper=new QueryWrapper<>();
        queryWrapper.eq("user_id",userId);
        ScScoreReportEntity sc=scScoreReportService.getOne(queryWrapper);
        List<ScScoreReportEntity> list=scScoreReportService.avgWy();
        if (sc!=null&&list.size()>0){
            for (ScScoreReportEntity score:list){
                if (score.getGrade().equals(sc.getGrade())){
                    sc.setModuleDScore(sc.getModuleDScore().subtract(score.getModuleDScore()));
                    sc.setModuleZScore(sc.getModuleZScore().subtract(score.getModuleZScore()));
                    sc.setModuleTScore(sc.getModuleTScore().subtract(score.getModuleTScore()));
                    sc.setModuleMScore(sc.getModuleMScore().subtract(score.getModuleMScore()));
                    sc.setModuleLScore(sc.getModuleLScore().subtract(score.getModuleLScore()));
                    break;
                }
            }
        }
        result.setSuccess(true);
        result.setResult(sc);
        return result;
    }


    /**
     * 成绩单任职奖惩经历
     */
    @AutoLog(value = "成绩单任职奖惩经历")
    @ApiOperation(value="成绩单任职奖惩经历", notes="成绩单任职奖惩经历")
    @GetMapping(value = "/queryScoreExperience")
    public Result<List<ScExperienceEntity>> queryScoreExperience(String type) {
        Result<List<ScExperienceEntity>> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ScExperienceEntity> list = scExperienceService.queryScoreExperience(sysUser.getId(),type);
        result.setSuccess(true);
        result.setResult(list);
        return result;
    }


    /**
     * 分页列表查询
     */
    @AutoLog(value = "成绩详情分页列表查询")
    @ApiOperation(value="成绩详情分页列表查询", notes="成绩详情分页列表查询")
    @GetMapping(value = "/scoreList")
    public Result<IPage<ScItemHoursEntity>> list(ScItemHoursEntity scItemHoursEntity,
                                                 @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                 @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                 HttpServletRequest req) {
        Result<IPage<ScItemHoursEntity>> result = new Result<IPage<ScItemHoursEntity>>();
        QueryWrapper<ScItemHoursEntity> queryWrapper =new QueryWrapper<>();
        queryWrapper.eq("item_id",scItemHoursEntity.getItemId());
        queryWrapper.eq("given",ScItemHoursEntity.GIVEN_YES);
        Page<ScItemHoursEntity> page = new Page<ScItemHoursEntity>(pageNo, pageSize);
        IPage<ScItemHoursEntity> pageList = scItemHoursService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

}
