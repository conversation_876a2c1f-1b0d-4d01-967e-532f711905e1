package com.zs.create.modules.score.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.item.entity.ScItemHoursEntity;
import com.zs.create.modules.score.dto.ScoreExcelVo;
import com.zs.create.modules.score.dto.ScoreYearExcelVo;
import com.zs.create.modules.score.entity.ScYearScoreEntity;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 学年成绩单Service层
 *
 * <AUTHOR> @email 
 * @date 2022-03-25 13:53:29
 * @Version: V1.0
 */
@Service
public interface ScYearScoreService extends IService<ScYearScoreEntity> {

    /**
     * @title 学年成绩单-成绩单查询
     * <AUTHOR>
     * @updateTime 2022/03/25
     */
    IPage<ScYearScoreEntity> queryScYearScore(ScYearScoreEntity scYearScoreEntity, Page<ScYearScoreEntity> page);

    /**
     * @title 学年成绩单-成绩单查询(不做分页)
     * <AUTHOR>
     * @updateTime 2022/03/28
     */
    List<ScoreYearExcelVo> queryScYearScoreList(ScYearScoreEntity scYearScoreEntity, String order, String column);

    /**
     * 三学年数据
     * @param threeUsers
     * @param threeXn
     * @return
     */
    List<ScYearScoreEntity> getThreeScore(List<String> threeUsers, List<String> threeXn);

    /**
     * 四学年数据
     * @param fourUsers
     * @param fourXn
     * @return
     */
    List<ScYearScoreEntity> getFourScore(List<String> fourUsers, List<String> fourXn);

    void updateScore(ScItemHoursEntity itemHours,Integer type);

    void subScore(ScItemHoursEntity itemHours,Integer type);

    /**
     * 根据用户id获取用户总数据
     * @param id
     * @return
     */
    ScYearScoreEntity qryByUserId(String id);
}

