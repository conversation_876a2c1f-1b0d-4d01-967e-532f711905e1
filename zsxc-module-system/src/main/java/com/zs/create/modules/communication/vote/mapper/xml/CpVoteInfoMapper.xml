<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.communication.vote.mapper.CpVoteInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.communication.vote.entity.CpVoteInfoEntity" id="cpVoteInfoMap">
        <result property="id" column="id"/>
        <result property="voteTitle" column="vote_title"/>
        <result property="voteSt" column="vote_st"/>
        <result property="voteEt" column="vote_et"/>
        <result property="checkType" column="check_type"/>
        <result property="checkMin" column="check_min"/>
        <result property="checkMax" column="check_max"/>
        <result property="voteRemark" column="vote_remark"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="visitNum" column="visit_num"/>
        <result property="votedNum" column="voted_num"/>
        <result property="commentNum" column="comment_num"/>
    </resultMap>


</mapper>