package com.zs.create.modules.workflow.task.service.impl;

import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.workflow.assignee.service.IAssigneeService;
import com.zs.create.modules.workflow.common.enums.TaskStatusEumn;
import com.zs.create.modules.workflow.common.exception.TaskException;
import com.zs.create.modules.workflow.instance.service.IProcessInstanceService;
import com.zs.create.modules.workflow.task.service.IProcessTaskService;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricTaskInstance;
import org.activiti.engine.task.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> guodl
 * @Date: 2019/8/9 09:40
 * @Description:流程任务service
 */
@Service("processTaskService")
public class ProcessTaskService implements IProcessTaskService {
    @Autowired
    TaskService taskService;
    @Autowired
    RuntimeService runtimeService;
    @Autowired
    HistoryService historyService;
    @Autowired
    IProcessInstanceService processInstanceService;
    @Autowired
    IAssigneeService assigneeService;
    @Autowired
    private CampusAppService campusAppService;
    /**
     * 查询个人待办任务
     *
     * @param taskAssignee         用户唯一标识(userId)
     * @param firstResult(从第几条开始查询 0开始 LIMIT 10 OFFSET 0
     * @param maxResults(每次查询多少条)  LIMIT 10 OFFSET 0
     * @return Map<String       ,       Object> count:总条数
     */
    @Override
    public Map<String, Object> queryMyDaiBanTaskList(String taskAssignee, int firstResult, int maxResults) {
        Map<String, Object> map = new HashMap<>();
        Long count = taskService.createTaskQuery().taskAssignee(taskAssignee).count();
        List<Task> tasks = taskService.createTaskQuery()
                .taskAssignee(taskAssignee).orderByTaskCreateTime().desc()
                .listPage(firstResult, maxResults);
        map.put("count", count);
        map.put("taskList", tasks);
        return map;
    }

    /**
     * 查询多人任务(任务类型为多人任务）
     *
     * @param taskAssignee         用户唯一标识(userId)
     * @param firstResult(从第几条开始查询 0开始 LIMIT 10 OFFSET 0
     * @param maxResults(每次查询多少条)  LIMIT 10 OFFSET 0
     * @return Map<String       ,       Object> count:总条数
     */
    @Override
    public Map<String, Object> queryCandidateUserTaskList(String taskAssignee, int firstResult, int maxResults) {
        Map<String, Object> map = new HashMap<>();
        Long count = taskService.createTaskQuery().taskCandidateUser(taskAssignee).count();
        List<Task> tasks = taskService.createTaskQuery()
                .taskCandidateUser(taskAssignee).orderByTaskCreateTime().desc()
                .listPage(firstResult, maxResults);
        map.put("count", count);
        map.put("taskList", tasks);
        return map;
    }

    @Override
    public Map<String, Object> queryCandidateGroupTaskList(List<String> groupIds, int firstResult, int maxResults) {
        Map<String, Object> map = new HashMap<>();
        Long count = taskService.createTaskQuery().taskCandidateGroupIn(groupIds).count();
        List<Task> tasks = taskService.createTaskQuery()
                .taskCandidateGroupIn(groupIds).orderByTaskCreateTime().desc()
                .listPage(firstResult, maxResults);
        map.put("count", count);
        map.put("taskList", tasks);
        return map;
    }


    @Override
    public Map<String, Object> queryHistroyTaskList(String taskAssignee, int firstResult, int maxResults) {
        Map<String, Object> map = new HashMap<>();
        Long count = historyService.createHistoricTaskInstanceQuery().taskAssignee(taskAssignee).count();
        List<HistoricTaskInstance> historicTaskInstances = historyService.createHistoricTaskInstanceQuery()
                .taskAssignee(taskAssignee).orderByHistoricTaskInstanceEndTime().desc()
                .listPage(firstResult, maxResults);
        map.put("count", count);
        map.put("historicTaskInstanceList", historicTaskInstances);
        return map;
    }

    /**
     * 查询个人任务
     *
     * @param taskId 任务ID
     * @return map 包括 task 和流程变量
     */
    @Override
    public Map<String, Object> getTask(String taskId) {
        Map<String, Object> map = null;
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (null != task) {
            map = new HashMap<>();
            map.put("task", task);
            //流程变量
            Map<String, Object> variable = taskService.getVariables(taskId);
            map.put("variable", variable);
        }
        return map;
    }

    /**
     * 完成任务
     *
     * @param taskId    完成任务
     * @param variables 流程变量
     */
    @Override
    public TaskStatusEumn compeleteTask(String taskId, Map<String, Object> variables) {
        Map<String, Object> taskMap = getTask(taskId);
        if (null == taskMap) {
            throw new TaskException("当前未找到该任务，任务ID【" + taskId + "】");
        }
        if (null == taskMap.get("task")) {
            throw new TaskException("当前未找到该任务，任务ID【" + taskId + "】");
        }
        Task t = (Task) taskMap.get("task");
        //默认执行
        if (null == variables) {
            variables = new HashMap<>();
        }
        variables.put("isDefaultExcute", true);
        try {
            taskService.complete(taskId, variables);
            // 推送消息
            campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(t.getProcessInstanceId()));
        } catch (Exception e) {
            e.printStackTrace();
            throw new TaskException("任务完成失败", e);
        }
        TaskStatusEumn taskStatusEumn = TaskStatusEumn.SP;
        //流程已经结束
        boolean bol = processInstanceService.judgeProcessIsEnd(t.getProcessInstanceId());
        if (bol) {
            taskStatusEumn = TaskStatusEumn.FINISH;
            return taskStatusEumn;
        }
        Map<String, Object> variable = (Map<String, Object>) taskMap.get("variable");
        //完成任务后判断下一个环节是否是启用监听来指定办理人
        try {
            if (variable.get("enableTaskListener") instanceof Boolean) {
                //如果流程未结束
                if (!bol) {
                    //如果不使用任务监听则手动设置下一环节任务
                    if (!((Boolean) variable.get("enableTaskListener"))) {
                        //查询任务
                        List<Task> tasks = taskService.createTaskQuery().processInstanceId(t.getProcessInstanceId()).list();
                        if (null != tasks && tasks.size() > 0) {
                            //设置下一环节任务的办理人
                            for (Task task : tasks) {
                                assigneeService.setTaskAssignee(task.getId());
                            }

                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            throw new TaskException("任务完成失败,指定下一环节办理人出错", e);
        }
        return taskStatusEumn;
    }

    @Override
    public void claimTask(String taskId, String userId) {
        try {
            taskService.claim(taskId, userId);
        } catch (Exception e) {
            throw new TaskException("任务拾取失败！", e);
        }

    }

    @Override
    public Task getTaskById(String taskId) {
        if (null == taskId || "".equals(taskId)) {
            throw new TaskException("任务ID【taskId】为空！");
        }
        Task task = taskService.createTaskQuery().taskId(taskId).singleResult();
        if (null == task) {
            throw new TaskException("当前未找到该任务，任务ID【" + taskId + "】");
        }
        return task;
    }

}

