package com.zs.create.modules.statistic.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.plugin.table.LoopRowTableRenderPolicy;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.base.enums.ScEnum;
import com.zs.create.base.util.SchoolBussinessUtil;
import com.zs.create.common.system.vo.DictModel;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.entity.ScItemFinishEntity;
import com.zs.create.modules.item.entity.ScItemHoursEntity;
import com.zs.create.modules.item.entity.ScItemRegistrationEntity;
import com.zs.create.modules.item.enums.HoursAuditStatusEnum;
import com.zs.create.modules.item.enums.ItemAuditStatusEnum;
import com.zs.create.modules.item.service.ScItemFinishService;
import com.zs.create.modules.item.service.ScItemHoursService;
import com.zs.create.modules.item.service.ScItemRegistrationService;
import com.zs.create.modules.item.service.ScItemService;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.paramdesign.entity.ScWeeksEntity;
import com.zs.create.modules.paramdesign.service.ScWeeksService;
import com.zs.create.modules.score.util.EncypterUtil;
import com.zs.create.modules.statistic.entity.SuperviseMonthlyReportDto;
import com.zs.create.modules.statistic.entity.SuperviseMonthlyReportEntity;
import com.zs.create.modules.statistic.mapper.SuperviseMonthlyReportMapper;
import com.zs.create.modules.statistic.service.SuperviseMonthlyReportService;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import com.zs.create.modules.system.service.*;
import com.zs.create.modules.system.util.EmailUtil;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 督导月报Service实现层
 * @date 2022-04-08 14:28:38
 * @Version: V1.0
 */
@Slf4j
@Service
public class SuperviseMonthlyReportServiceImpl extends ServiceImpl<SuperviseMonthlyReportMapper, SuperviseMonthlyReportEntity> implements SuperviseMonthlyReportService {

    @Value("${gzhAppid}")
    private String appid;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private ISysDictService dictService;
    @Autowired
    private ISysDepartService departService;
    @Autowired
    private ScWeeksService scWeeksService;
    @Autowired
    private ScItemService scItemService;
    @Autowired
    private ScItemFinishService scItemFinishService;
    @Autowired
    private ScItemRegistrationService scItemRegistrationService;
    @Autowired
    private ScItemHoursService scItemHoursService;
    @Autowired
    private SysWeixinUserService sysWeixinUserService;
    @Autowired
    private WxMessageSender wxMessageSender;
    @Autowired
    private ISysDepartService sysDepartService;
    @Value("${qdEmailUrl}")
    private String qdEmailUrl;

    @Override
    public byte[] download(SuperviseMonthlyReportEntity entity) throws Exception {
        SuperviseMonthlyReportDto dto = JSON.parseObject(entity.getContent(), SuperviseMonthlyReportDto.class);

        ClassPathResource resource = new ClassPathResource("template/SuperviseMonthlyReportTemplate.docx");

        LoopRowTableRenderPolicy loopRowTableRenderPolicy = new LoopRowTableRenderPolicy();
        Configure config = Configure.builder()
                .bind("schoolItemStatistic.itemModuleStatisticList", loopRowTableRenderPolicy)
                .bind("collegeItemStatistic.itemModuleStatisticList", loopRowTableRenderPolicy)
                .bind("gradePersonStatisticList", loopRowTableRenderPolicy)
                .bind("abnormalItemList", loopRowTableRenderPolicy)
                .build();

        ByteArrayOutputStream out = new ByteArrayOutputStream();
        XWPFTemplate template = XWPFTemplate.compile(resource.getInputStream(), config);
        template.render(dto);
        template.writeAndClose(out);

        return out.toByteArray();
    }

    /**
     * 督导月报消息提醒
     */
    @XxlJob("superviseMonthlyReportSendMessageJob")
    public void sendMessageTask() {
        LocalDate date = LocalDate.now().withDayOfMonth(1);

        String param = XxlJobHelper.getJobParam();
        if (!StringUtils.isEmpty(param)) {
            try {
                date = DateTimeFormatter.ISO_LOCAL_DATE.parse(param, LocalDate::from);
            } catch (Exception e) {
                // ignore
            }
        }
        // 1. 督导月报
        date = date.plusMonths(-1);
        List<SuperviseMonthlyReportEntity> list = this.lambdaQuery()
                .eq(SuperviseMonthlyReportEntity::getTime, date)
                .list();
        if (list.isEmpty()) {
            XxlJobHelper.handleSuccess("月报还未生成");
            return;
        }

        // 2. 学院
        List<String> depIdList = list.stream()
                .map(SuperviseMonthlyReportEntity::getCollegeId)
                .distinct()
                .collect(Collectors.toList());
        // 3. 部门与对应的负责人集合
        Map<String, List<String>> depMap = departService.listByIds(depIdList)
                .stream()
                .collect(Collectors.toMap(SysDepart::getId, d -> Arrays.stream(d.getPersonInCharge().split(","))
                        .distinct()
                        .collect(Collectors.toList())));
        //所有的负责人集合
        Set<String> userIdSet = depMap.values()
                .stream()
                .flatMap(Collection::stream)
                .collect(Collectors.toSet());
        if (userIdSet.isEmpty()) {
            return;
        }

        // 4. 关联了公众号的用户
        Map<String, WxUserDTO> userMap = sysWeixinUserService.findUserOpenIds(userIdSet, appid)
                .stream()
                .collect(Collectors.toMap(WxUserDTO::getUserId, u -> u));
        if (userMap.isEmpty()) {
            return;
        }

        List<WxCommonMsgInfo> wxCommonMsgInfoList = new LinkedList<>();
        for (SuperviseMonthlyReportEntity report : list) {
            // 督导月报id
            String id = report.getId();
            // 学院id
            String collegeId = report.getCollegeId();
            if (!depMap.containsKey(collegeId)) {
                continue;
            }

            List<String> userIdList = depMap.get(collegeId);
            for (String userId : userIdList) {
                if (!userMap.containsKey(userId)) {
                    continue;
                }
                WxUserDTO wxUserDTO = userMap.get(userId);

                String theme = wxUserDTO.getRealname() + "/" + wxUserDTO.getUsername() + ",您有一条新的提醒";
                String url = "pagesA/monthReport/monthReport?id=" + id;
                WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                        .setUseCommonTemplate(Boolean.TRUE)
                        .setTheme(theme)
                        .setTitle("月报查看提醒")
                        .setUserId(userId)
                        .setCreateDate(new Date())
                        .setContent(wxUserDTO.getRealname() + " 老师您好！您所负责学院的“" + date.getYear() + "年" + date.getMonthValue() + "月月报”已生成，请注意查看。")
                        .setOpenId(wxUserDTO.getOpenId())
                        .setRemark("点击可查看详情。")
                        .setMiniAppUrl(url);
                wxCommonMsgInfoList.add(wxCommonMsgInfo);
            }
        }
        //给学院负责人发邮件
        List<String> emails = new ArrayList<>();
        //查询所有的负责人id和对应的数据
        final Map<String, SysUser> sysUserMap = userService.getUserByIds(userIdSet).stream().collect(Collectors.toMap(SysUser::getId, u -> u, (key1, key2) -> key1));

        List<String> toList = new ArrayList<>(userIdSet);
        for (String id : toList) {
            SysUser user = sysUserMap.get(id);
            if (Objects.nonNull(user)){
                String email = user.getEmail();
                if (email!=null && !"".equals(email) && !"<EMAIL>".equals(email)) {
                    emails.add(email);
                }
            }
        }
        String[] emailstr = emails.toArray(new String[emails.size()]);
        String img = "老师您好！ "+"\r\n"+ "您所负责学院的" + date.getYear() + "年" + date.getMonthValue() + "月“第二课堂”月报已生成，请点击查看详情。"+"\r\n";
        String emailurl = qdEmailUrl + "/dataAnalysis/monthlyReport";
        EmailUtil.sendSimpleMail(emailstr, "中国科技大学第二课堂沟通平台",img + emailurl);

        for (WxCommonMsgInfo wxCommonMsgInfo : wxCommonMsgInfoList) {
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }
    /**
     * 督导月报定时任务
     */
    @XxlJob("superviseMonthlyReportGenerateJob")
    public void generateTask() {
        StopWatch sw = StopWatch.createStarted();

        LocalDate today = LocalDate.now();

        String param = XxlJobHelper.getJobParam();
        if (!StringUtils.isEmpty(param)) {
            try {
                today = DateTimeFormatter.ISO_LOCAL_DATE.parse(param, LocalDate::from);
            } catch (Exception e) {
                // ignore
            }
        }

        LocalDate end = today.withDayOfMonth(1).plusDays(-1);
        LocalDate start = end.withDayOfMonth(1);
        Date time = Date.from(start.atStartOfDay(ZoneId.systemDefault()).toInstant());

        // 1.1 查询上月发布的所有项目
        Wrapper<ScItemEntity> itemWrapper = Wrappers.<ScItemEntity>lambdaQuery()
                .ge(ScItemEntity::getCreateTime, start)
                .lt(ScItemEntity::getCreateTime, end.plusDays(1))
                .eq(ScItemEntity::getDelFlag, DelFlagEnum.NO_DEL.getCode());
        List<ScItemEntity> itemList = scItemService.list(itemWrapper);

        // 1.2 结项项目
        Wrapper<ScItemFinishEntity> itemFinishWrapper = Wrappers.<ScItemFinishEntity>lambdaQuery()
                .ge(ScItemFinishEntity::getCreateTime, start)
                .lt(ScItemFinishEntity::getCreateTime, end.plusDays(1));
        List<ScItemFinishEntity> itemFinishList = scItemFinishService.list(itemFinishWrapper);
        List<String> itemFinishItemIdList = itemFinishList.stream()
                .map(ScItemFinishEntity::getItemId)
                .collect(Collectors.toList());
        List<ScItemEntity> itemFinishItemList = Collections.emptyList();
        if (!itemFinishItemIdList.isEmpty()) {
            Wrapper<ScItemEntity> itemFinishItemWrapper = Wrappers.<ScItemEntity>lambdaQuery()
                    .in(ScItemEntity::getId, itemFinishItemIdList)
                    .eq(ScItemEntity::getDelFlag, DelFlagEnum.NO_DEL.getCode());
            itemFinishItemList = scItemService.list(itemFinishItemWrapper);
        }

        // 1.3 学时
        Wrapper<ScItemHoursEntity> itemHoursWrapper = Wrappers.<ScItemHoursEntity>lambdaQuery()
                .ge(ScItemHoursEntity::getUpdateTime, start)
                .lt(ScItemHoursEntity::getUpdateTime, end.plusDays(1))
                .eq(ScItemHoursEntity::getStatus, HoursAuditStatusEnum.AUDIT_PASS.getCode())
                .eq(ScItemHoursEntity::getGiven, ScItemHoursEntity.GIVEN_YES)
                .eq(ScItemHoursEntity::getDelFlag, DelFlagEnum.NO_DEL.getCode());
        List<ScItemHoursEntity> itemHoursAllList = scItemHoursService.list(itemHoursWrapper);
        // 1.4 学时关联项目
        List<String> itemHoursItemIdList = itemHoursAllList.stream()
                .map(ScItemHoursEntity::getItemId)
                .collect(Collectors.toList());
        List<ScItemEntity> itemHoursItemList = Collections.emptyList();
        if (!itemHoursItemIdList.isEmpty()) {
            Wrapper<ScItemEntity> itemHoursItemWrapper = Wrappers.<ScItemEntity>lambdaQuery()
                    .in(ScItemEntity::getId, itemHoursItemIdList)
                    .eq(ScItemEntity::getDelFlag, DelFlagEnum.NO_DEL.getCode());
            itemHoursItemList = scItemService.list(itemHoursItemWrapper);
        }

        Set<String> itemHoursItemIdSet = itemHoursItemList.stream()
                .map(ScItemEntity::getId)
                .collect(Collectors.toSet());
        List<ScItemHoursEntity> itemHoursList = itemHoursAllList.stream()
                .filter(itemHoursEntity -> itemHoursItemIdSet.contains(itemHoursEntity.getItemId()))
                .collect(Collectors.toList());

        // 1.5 报名信息
        List<ScItemRegistrationEntity> itemRegistrationList = Collections.emptyList();
        if (!itemHoursItemIdSet.isEmpty()) {
            Wrapper<ScItemRegistrationEntity> itemRegistrationWrapper = Wrappers.<ScItemRegistrationEntity>lambdaQuery()
                    .in(ScItemRegistrationEntity::getItemId, itemHoursItemIdSet)
                    .eq(ScItemRegistrationEntity::getDelFlag, DelFlagEnum.NO_DEL.getCode());
            itemRegistrationList = scItemRegistrationService.list(itemRegistrationWrapper);
        }

        // 1.6 本科生
        Set<String> userIdSet = new HashSet<>();
        itemRegistrationList.stream()
                .map(ScItemRegistrationEntity::getUserId)
                .forEach(userIdSet::add);
        itemHoursAllList.stream()
                .map(ScItemHoursEntity::getUserId)
                .forEach(userIdSet::add);
        List<SysUser> userAllList = Collections.emptyList();
        if (!userIdSet.isEmpty()) {
            Wrapper<SysUser> userWrapper = Wrappers.<SysUser>lambdaQuery()
                    .in(SysUser::getId, userIdSet)
                    .ne(SysUser::getType, "T");
            userAllList = userService.list(userWrapper);
        }

        Set<String> userIdAllSet = userAllList.stream()
                .map(SysUser::getId)
                .collect(Collectors.toSet());
        List<SysUser> userList = userAllList.stream()
                .filter(user -> "S".equals(user.getType()))
                .collect(Collectors.toList());
        userIdSet = userList.stream()
                .map(SysUser::getId)
                .collect(Collectors.toSet());

        // 1.7 过滤老师数据
        itemHoursAllList = itemHoursAllList.stream()
                .filter(itemHours -> userIdAllSet.contains(itemHours.getUserId()))
                .collect(Collectors.toList());
        itemHoursList = itemHoursList.stream()
                .filter(itemHours -> userIdAllSet.contains(itemHours.getUserId()))
                .collect(Collectors.toList());
        itemRegistrationList = itemRegistrationList.stream()
                .filter(itemRegistration -> userIdAllSet.contains(itemRegistration.getUserId()))
                .collect(Collectors.toList());

        // 1.8 学年数据
        ScWeeksEntity currentWeek = scWeeksService.getCurrentWeek();
        int currentYear = SchoolBussinessUtil.getCurrentYear(currentWeek);

        // 2. 统计全校数据
        SuperviseMonthlyReportDto.ItemStatistic schoolItemStatistic = statistic(userIdSet, itemList, itemFinishItemList, itemHoursItemList, itemHoursAllList, itemHoursList, itemRegistrationList);

        // 3. 按学院统计
        List<SuperviseMonthlyReportEntity> list = new LinkedList<>();

        List<DictModel> dictModelList = dictService.queryDictItemsByCode(ScEnum.STATISTIC_COLLEGE.getCode());
        for (DictModel dictModel : dictModelList) {
            String collegeId = dictModel.getValue();
            SysDepart college = departService.getById(collegeId);
            if (college == null) {
                log.warn("学院不存在[id={}]", collegeId);
                continue;
            }

            // 子部门
            List<String> depIds = ((SysDepartMapper) departService.getBaseMapper()).getDepidsByLikePids(Collections.singletonList(college.getPids()));
            Set<String> depIdSet = new HashSet<>(depIds);
            List<SysDepart> depList = Collections.emptyList();
            if (!depIds.isEmpty()) {
                Wrapper<SysDepart> depWrapper = Wrappers.<SysDepart>lambdaQuery()
                        .in(SysDepart::getId, depIds);
                depList = departService.list(depWrapper);
            }

            // 学院数据
            // 本科生
            List<SysUser> collegeUserAllList = userAllList.stream()
                    .filter(user -> depIdSet.contains(user.getCollegeId()))
                    .collect(Collectors.toList());
            List<SysUser> collegeUserList = collegeUserAllList.stream()
                    .filter(user -> "S".equals(user.getType()))
                    .collect(Collectors.toList());
            Set<String> collegeUserIdSet = collegeUserList.stream()
                    .map(SysUser::getId)
                    .collect(Collectors.toSet());
            // 项目
            List<ScItemEntity> collegeItemList = itemList.stream()
                    .filter(item -> depIdSet.contains(item.getBusinessDeptId()))
                    .collect(Collectors.toList());
            // 结项项目
            List<ScItemEntity> collegeItemFinishItemList = itemFinishItemList.stream()
                    .filter(item -> depIdSet.contains(item.getBusinessDeptId()))
                    .collect(Collectors.toList());
            // 学时关联项目
            List<ScItemEntity> collegeItemHoursItemList = itemHoursItemList.stream()
                    .filter(item -> depIdSet.contains(item.getBusinessDeptId()))
                    .collect(Collectors.toList());
            Set<String> collegeItemHoursItemIdSet = collegeItemHoursItemList.stream()
                    .map(ScItemEntity::getId)
                    .collect(Collectors.toSet());
            // 项目学时
            List<ScItemHoursEntity> collegeItemHoursList = itemHoursAllList.stream()
                    .filter(itemHours -> collegeItemHoursItemIdSet.contains(itemHours.getItemId()))
                    .collect(Collectors.toList());

            // 按学院统计
            SuperviseMonthlyReportDto.ItemStatistic collegeStatistic = statistic(collegeUserIdSet, collegeItemList, collegeItemFinishItemList, collegeItemHoursItemList, collegeItemHoursList, collegeItemHoursList, itemRegistrationList);

            // 按年级统计
            List<SuperviseMonthlyReportDto.GradePersonStatistic> gradePersonStatisticList = new LinkedList<>();
            for (int year = currentYear; year >= currentYear - 3; year--) {
                int finalYear = year;
                // 年级学生
                Set<String> gradeUserIdSet = collegeUserList.stream()
                        .filter(user -> NumberUtils.toInt(user.getGrade()) == finalYear)
                        .map(SysUser::getId)
                        .collect(Collectors.toSet());
                // 年级部门
                String pattern = (year - 2000) + "级";
                Set<String> gradeDepIdSet = depList.stream()
                        .filter(dep -> dep.getDepartName().contains(pattern) && !dep.getDepartName().contains("硕") && !dep.getDepartName().contains("博"))
                        .map(SysDepart::getId)
                        .collect(Collectors.toSet());

                SuperviseMonthlyReportDto.GradePersonStatistic gradePersonStatistic = new SuperviseMonthlyReportDto.GradePersonStatistic();
                gradePersonStatistic.setGrade(year + "级本科生");

                // 发布项目数
                long itemCount = collegeItemList.stream()
                        .filter(item -> item.getExamineStatus() >= ItemAuditStatusEnum.PUBLISH.getCode())
                        .filter(item -> gradeDepIdSet.contains(item.getBusinessDeptId()))
                        .count();
                gradePersonStatistic.setItemCount(String.valueOf(itemCount));

                // 参与项目人次
                //List<ScItemHoursEntity> gradeItemHourList = itemHoursAllList.stream()
                List<ScItemHoursEntity> gradeItemHourList = itemHoursList.stream()
                        .filter(itemHours -> gradeUserIdSet.contains(itemHours.getUserId()))
                        .collect(Collectors.toList());
                int participantCount = gradeItemHourList.size();
                gradePersonStatistic.setParticipantCount(String.valueOf(participantCount));

                // 获得学时数
                BigDecimal totalHour = gradeItemHourList.stream()
                        .map(ScItemHoursEntity::getHours)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                gradePersonStatistic.setTotalHour(getItemHours(totalHour));

                gradePersonStatisticList.add(gradePersonStatistic);
            }

            // 异常项目
            List<SuperviseMonthlyReportDto.AbnormalItem> abnormalItemList = new LinkedList<>();
            for (ScItemEntity item : collegeItemList) {
                String status = getAbnormalStatus(item.getExamineStatus());
                if (status == null) {
                    continue;
                }

                String module = dictService.queryDictTextByKey(ScEnum.ITEMMODULE.getCode(), item.getModule());

                SuperviseMonthlyReportDto.AbnormalItem abnormalItem = new SuperviseMonthlyReportDto.AbnormalItem();
                abnormalItem.setItemId(item.getId())
                        .setItemName(item.getItemName())
                        .setModule(module)
                        .setStatus(status)
                        .setPerson(item.getLinkMan())
                        .setContact(item.getTel())
                        .setCreateTime(DateFormatUtils.format(item.getCreateTime(), "yyyy-MM-dd"));
                abnormalItemList.add(abnormalItem);
            }
            if (abnormalItemList.isEmpty()) {
                SuperviseMonthlyReportDto.AbnormalItem abnormalItem = new SuperviseMonthlyReportDto.AbnormalItem();
                abnormalItem.setItemId("无")
                        .setItemName("无")
                        .setModule("无")
                        .setStatus("无")
                        .setPerson("无")
                        .setContact("无")
                        .setCreateTime("无");
                abnormalItemList.add(abnormalItem);
            }

            SuperviseMonthlyReportDto dto = new SuperviseMonthlyReportDto();
            dto.setStartDate(start)
                    .setEndDate(end)
                    .setCollegeId(collegeId)
                    .setCollegeName(college.getDepartName())
                    .setSchoolItemStatistic(schoolItemStatistic)
                    .setCollegeItemStatistic(collegeStatistic)
                    .setGradePersonStatisticList(gradePersonStatisticList)
                    .setAbnormalItemList(abnormalItemList);

            SuperviseMonthlyReportEntity entity = new SuperviseMonthlyReportEntity();
            entity.setTime(time)
                    .setCollegeId(collegeId)
                    .setContent(JSON.toJSONString(dto));
            list.add(entity);
        }

        this.saveBatch(list);

        sw.stop();
        XxlJobHelper.handleSuccess("月报定时任务完成，耗时 " + sw.getTime() + "ms");
    }

    private String getAbnormalStatus(int status) {
        switch (status) {
            case -1:
                return "驳回";
            case -3:
                return "异常结项";
            case -4:
                return "不同意举办";
            case 35:
                return "学时驳回";
            default:
                return null;
        }
    }

    private SuperviseMonthlyReportDto.ItemStatistic statistic(Set<String> userIdSet,
                                                              List<ScItemEntity> itemList,
                                                              List<ScItemEntity> itemFinishList,
                                                              List<ScItemEntity> itemHoursItemList,
                                                              List<ScItemHoursEntity> itemHoursAllList,
                                                              List<ScItemHoursEntity> itemHoursList,
                                                              List<ScItemRegistrationEntity> allItemRegistrationList) {
        SuperviseMonthlyReportDto.ItemStatistic dto = new SuperviseMonthlyReportDto.ItemStatistic();

        // 发布项目数（审核通过）
        List<ScItemEntity> publishItemList = itemList.stream()
                .filter(item -> item.getExamineStatus() >= ItemAuditStatusEnum.PUBLISH.getCode())
                .collect(Collectors.toList());
        int total = publishItemList.size();
        dto.setTotal(String.valueOf(total));

        // 结项项目数
        long finish = itemFinishList.size();
        dto.setFinish(String.valueOf(finish));

        // 各等级项目数
        // 校级及以上
        long xj = publishItemList.stream()
                .filter(item -> "international".equals(item.getActivityLevel())
                        || "country".equals(item.getActivityLevel())
                        || "provincial".equals(item.getActivityLevel())
                        || "school".equals(item.getActivityLevel()))
                .count();
        // 院级
        long yj = publishItemList.stream()
                .filter(item -> "college".equals(item.getActivityLevel()))
                .count();
        // 班级
        long bj = publishItemList.stream()
                .filter(item -> "class".equals(item.getActivityLevel()))
                .count();
        dto.setXj(String.valueOf(xj));
        dto.setYj(String.valueOf(yj));
        dto.setBj(String.valueOf(bj));

        // 驳回数&驳回率
        long reject = itemList.stream()
                .filter(item -> Objects.equals(item.getExamineStatus(), ItemAuditStatusEnum.REJECTNOUPDATE.getCode()))
                .count();
        BigDecimal rejectRate = BigDecimal.ZERO;
        if (total + reject != 0) {
            rejectRate = new BigDecimal(reject * 100).divide(new BigDecimal(total + reject), 2, RoundingMode.HALF_UP);
        }
        dto.setReject(String.valueOf(reject));
        dto.setRejectRate(rejectRate + "%");

        // 项目参与人次
        int totalParticipantCount = itemHoursList.size();
        dto.setParticipantCount(String.valueOf(totalParticipantCount));

        // 荣誉申报赋予学时
        BigDecimal honorTotalHour = itemHoursAllList.stream()
                .filter(itemHours -> ScItemHoursEntity.HOUR_DECLARATION_ITEM_ID.equals(itemHours.getItemId()))
                .map(ScItemHoursEntity::getHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        // 总赋予学时
        BigDecimal totalHour = itemHoursAllList.stream()
                .map(ScItemHoursEntity::getHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        // 本科生赋予学时数
        BigDecimal undergraduateHour = itemHoursAllList.stream()
                .filter(itemHours -> userIdSet.contains(itemHours.getUserId()))
                .map(ScItemHoursEntity::getHours)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        dto.setTotalHour(getItemHours(totalHour));
        dto.setUndergraduateHour(getItemHours(undergraduateHour));
        dto.setHonorHour(getItemHours(honorTotalHour));

        // 参与率
        BigDecimal participationRate = calcParticipationRate(itemHoursList, allItemRegistrationList);
        dto.setParticipationRate(participationRate + "%");

        List<DictModel> dictModelList = dictService.queryDictItemsByCode(ScEnum.ITEMMODULE.getCode());
        for (DictModel dictModel : dictModelList) {
            String module = dictModel.getValue();
            String moduleText = dictModel.getText() + "育";

            // 模块项目数据
            List<ScItemEntity> modulePublishItemList = publishItemList.stream()
                    .filter(item -> module.equals(item.getModule()))
                    .collect(Collectors.toList());
            // 模块学时数据
            Set<String> itemHoursItemIdSet = itemHoursItemList.stream()
                    .filter(item -> module.equals(item.getModule()))
                    .map(ScItemEntity::getId)
                    .collect(Collectors.toSet());
            List<ScItemHoursEntity> moduleItemHourList = itemHoursList.stream()
                    .filter(itemHours -> itemHoursItemIdSet.contains(itemHours.getItemId()))
                    .collect(Collectors.toList());

            // 发布项目数
            int moduleTotal = modulePublishItemList.size();

            // 结项项目数
            long moduleFinish = itemFinishList.stream()
                    .filter(item -> module.equals(item.getModule()))
                    .count();

            // 赋予学时数
            BigDecimal moduleTotalHour = moduleItemHourList.stream()
                    .map(ScItemHoursEntity::getHours)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            // 非项目学时
            moduleTotalHour = itemHoursAllList.stream()
                    .filter(itemHours -> !itemHoursItemIdSet.contains(itemHours.getItemId()) && module.equals(itemHours.getModule()))
                    .map(ScItemHoursEntity::getHours)
                    .reduce(moduleTotalHour, BigDecimal::add);

            // 参与人次
            int moduleTotalParticipantCount = moduleItemHourList.size();

            // 参与率
            BigDecimal moduleParticipationRate = calcParticipationRate(moduleItemHourList, allItemRegistrationList);

            SuperviseMonthlyReportDto.ItemModuleStatistic itemModuleStatistic = new SuperviseMonthlyReportDto.ItemModuleStatistic();
            itemModuleStatistic.setModule(moduleText);
            itemModuleStatistic.setItemCount(String.valueOf(moduleTotal));
            if (moduleTotal != 0 && moduleItemHourList.isEmpty()) {
                // 如发布项目数不为0，但赋予学时数和参与项目人次为0，则在发布项目数后加上文字“暂未赋予学时”
                itemModuleStatistic.setItemCount(itemModuleStatistic.getItemCount() + "（暂未赋予学时）");
            }
            itemModuleStatistic.setFinish(String.valueOf(moduleFinish));
            if (moduleFinish == 0 && !moduleItemHourList.isEmpty()) {
                // 如结项项目数为0，但赋予学时数和参与人次不为0，则添加（进行中，暂未结项）
                itemModuleStatistic.setFinish(itemModuleStatistic.getFinish() + "（进行中，暂未结项）");
            }
            itemModuleStatistic.setTotalHour(getItemHours(moduleTotalHour));
            itemModuleStatistic.setParticipantCount(String.valueOf(moduleTotalParticipantCount));
            itemModuleStatistic.setParticipationRate(moduleParticipationRate + "%");

            dto.getItemModuleStatisticList().add(itemModuleStatistic);
        }

        return dto;
    }

    public BigDecimal calcParticipationRate(List<ScItemHoursEntity> itemHoursList, List<ScItemRegistrationEntity> itemRegistrationList) {
        List<BigDecimal> participationRateList = new LinkedList<>();

        Set<String> hoursItemIdSet = itemHoursList.stream()
                .map(ScItemHoursEntity::getItemId)
                .collect(Collectors.toSet());
        for (String itemId : hoursItemIdSet) {
            // 计算各项目参与率
            long participantCount = itemHoursList.stream()
                    .filter(itemHours -> itemId.equals(itemHours.getItemId()))
                    .count();
            long applyCount = itemRegistrationList.stream()
                    .filter(itemRegistration -> itemId.equals(itemRegistration.getItemId()))
                    .count();
            BigDecimal participationRate = new BigDecimal(participantCount * 100).divide(new BigDecimal(applyCount), 2, RoundingMode.HALF_UP);
            participationRateList.add(participationRate);
        }

        BigDecimal participationRate = BigDecimal.ZERO;
        if (!participationRateList.isEmpty()) {
            // 参与率平均值
            BigDecimal sum = participationRateList.stream()
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            participationRate = sum.divide(new BigDecimal(participationRateList.size()), 2, RoundingMode.HALF_UP);
        }
        return participationRate;
    }

    private String getItemHours(BigDecimal d) {
        return d.setScale(1, RoundingMode.HALF_UP).toString();
    }
}
