package com.zs.create.modules.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.item.entity.ScItemCollectEntity;
import com.zs.create.modules.lib.entity.ScLibraryCollectNumVo;

import java.util.List;

/**
 * @Description 项目收藏Service层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-07 09:59:09
 * @Version: V1.0
 */
public interface ScItemCollectService extends IService<ScItemCollectEntity> {
    List<ScLibraryCollectNumVo> selectCollectNum();
}

