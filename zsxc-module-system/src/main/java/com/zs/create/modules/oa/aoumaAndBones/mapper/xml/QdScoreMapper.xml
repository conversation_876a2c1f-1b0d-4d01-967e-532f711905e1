<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.aoumaAndBones.mapper.QdScoreMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.aoumaAndBones.entity.QdScoreEntity" id="qdScoreMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="classId" column="class_id"/>
        <result property="score" column="score"/>
        <result property="suggest" column="suggest"/>
        <result property="selectionResult" column="selection_result"/>
        <result property="experience" column="experience"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="status" column="status"/>
        <result property="publicStatus" column="public_status"/>
    </resultMap>
    <update id="updateByClassId">
        update qd_score
        set public_status = '2'
        where class_id = #{classId}
        and public_status = '1'
    </update>

    <select id="queryPageList" resultType="com.zs.create.modules.oa.aoumaAndBones.entity.QdScoreEntity">
        SELECT
        s.*
        FROM
        qd_score s
        LEFT JOIN sys_user u ON s.user_id=u.username
        WHERE
        s.class_id=#{qdScore.classId}
        <if test="qdScore.userId != null and qdScore.userId != ''">
            and (s.user_name like CONCAT('%',#{qdScore.userId},'%') or s.user_id like CONCAT('%',#{qdScore.userId},'%'))
        </if>
        ORDER BY u.college DESC,u.grade DESC,id
    </select>


</mapper>