package com.zs.create.modules.item.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 关于项目评价
 *
 * <AUTHOR> @email 
 * @date 2023-02-13 09:25:25
 */
@Data
@TableName("sc_evaluation_dict")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_evaluation_dict对象", description="关于项目评价")
public class ScEvaluationDictEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	//启用中
	public static final int QYZ=2;
	//曾启用
	public static final int CQY=1;
	/**
	 * id
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "id")
	    private String id;
	/**
	 * 评价字典名称
	 */
	    @ApiModelProperty(value = "评价字典名称")
	    private String evaluationDictName;
	/**
	 * 创建人
	 */
	    @ApiModelProperty(value = "创建人")
	    private String createBy;
	/**
	 * 创建时间
	 */
	    @ApiModelProperty(value = "创建时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date createTime;
	/**
	 * 更新人
	 */
	    @ApiModelProperty(value = "更新人")
	    private String updateBy;
	/**
	 * 更新时间
	 */
	    @ApiModelProperty(value = "更新时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date updateTime;
	/**
	 * 0:未启用；1:启用中；3:曾启用
	 */
	    @ApiModelProperty(value = "0:未启用；2:启用中；1:曾启用")
	    private Integer status;

	/**
	 * 编号
	 */
	@ApiModelProperty(value = "编号")
	@TableField(exist = false)
	private String sort;

	/**
	 * 字典详细
	 */
	@ApiModelProperty(value = "字典详细")
	@TableField(exist = false)
	private List<ScEvaluationDictItemEntity> scEvaluationDictItemEntityList;

	/**
	 * 是否结项（包含异常结项）
	 */
	@ApiModelProperty(value = "是否结项")
	@TableField(exist = false)
	private String isEnd;
}
