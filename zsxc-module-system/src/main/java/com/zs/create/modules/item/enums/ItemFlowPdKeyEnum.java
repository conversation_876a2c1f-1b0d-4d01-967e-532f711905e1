package com.zs.create.modules.item.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * 机构类型对应不同审核流程的processDefinitionKey 的对应关系
 */
public enum ItemFlowPdKeyEnum {
    XST_SQ("校社团申请", "item_xst_sq" , "5")
    ,YST_SQ("院社团/协会申请", "item_ystxh_sq", "6")
    ,XJXH_SQ("校级协会申请", "item_xjxh_sq", "7")
    ,XXYH_SQ("校学研会申请", "item_xxyh_sq", "8")
    ,YXYH_SQ("院学研会申请", "item_yxyh_sq", "9")
    ,XMFZR_SQ("项目负责人申请", "item_xmfzr_sq", "")
    ,BJ_SQ("班级申请", "item_bj_sq", "4")
    ,TZB_SQ("团支部申请" , "item_tzb_sq" , "10")
    ,ZZZTZB_SQ("自组织团支部申请" , "item_tzb_sq" , "17")
    ,ZSTZB_SQ("直属团支部申请", "item_zstzb_sq", "11")
    ,XQNZX_SQ("校青年中心申请", "item_xqnzx_sq", "12")
    ,XSDZB_SQ("学生党支部申请", "item_dzb_sq", "13")
    ,LS_SQ("老师角色申请", "item_ls_sq", "14")
    ,QMDGBM_SQ("青马大骨班报名申请","qd_bm_sq","99");

    ItemFlowPdKeyEnum(String name, String pdKey , String orgType) {
        this.name = name;
        this.pdKey = pdKey;
        this.orgType = orgType;
    }
    private String name;

    private String pdKey;
    @EnumValue
    private String orgType;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPdKey() {
        return pdKey;
    }

    public void setPdKey(String pdKey) {
        this.pdKey = pdKey;
    }

    public String getOrgType() { return orgType; }

    public void setOrgType() { this.orgType = orgType; }

}
