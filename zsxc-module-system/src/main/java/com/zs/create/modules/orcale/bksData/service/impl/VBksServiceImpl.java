package com.zs.create.modules.orcale.bksData.service.impl;

import com.zs.create.modules.orcale.bksData.entity.VBksEntity;
import com.zs.create.modules.orcale.bksData.mapper.VBksMapper;
import com.zs.create.modules.orcale.bksData.service.VBksService;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**
 * @Description 本科生数据Service实现层
 *
 * <AUTHOR> @email 
 * @date 2023-03-17 16:07:59
 * @Version: V1.0
 */
@Service
public class VBksServiceImpl extends ServiceImpl<VBksMapper, VBksEntity> implements VBksService {

}
