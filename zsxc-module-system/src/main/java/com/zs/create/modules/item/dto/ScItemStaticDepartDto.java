package com.zs.create.modules.item.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Map;

/**
 * @description: 项目统计分析封装dto
 * @author: anshenghui
 * @create: 2021-05-17 16:38
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ScItemStaticDepartDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 机构名称/项目级别
     */
    @Excel(name = "机构名称", width = 15,orderNum = "0")
    private String name;

    /**
     * 组织项目数
     */
    @Excel(name = "组织项目数", width = 15, groupName = "德",orderNum = "1")
    private String itemsD = "0";

    /**
     * 组织项目数占比
     */
    @Excel(name = "组织项目数占比", width = 15, groupName = "德",orderNum = "2")
    private String itemsProcentD = "0%";

    /**
     * 赋予学时数
     */
    @Excel(name = "赋予学时数", width = 15, groupName = "德",orderNum = "3")
    private String hoursD = "0";

    /**
     * 赋予学时数占比
     */
    @Excel(name = "赋予学时数占比", width = 15, groupName = "德",orderNum = "4")
    private String hoursProcentD = "0%";

    /**
     * 参与人次
     */
    @Excel(name = "参与人次", width = 15, groupName = "德",orderNum = "5")
    private String personsD = "0";

    /**
     * 参与人次占比
     */
    @Excel(name = "参与人次占比", width = 15, groupName = "德",orderNum = "6")
    private String personsProcentD = "0%";

    /**
     * 组织项目数
     */
    @Excel(name = "组织项目数", width = 15, groupName = "智",orderNum = "7")
    private String itemsZ = "0";

    /**
     * 组织项目数占比
     */
    @Excel(name = "组织项目数占比", width = 15, groupName = "智",orderNum = "8")
    private String itemsProcentZ = "0%";

    /**
     * 赋予学时数
     */
    @Excel(name = "赋予学时数", width = 15, groupName = "智",orderNum = "9")
    private String hoursZ = "0";

    /**
     * 赋予学时数占比
     */
    @Excel(name = "赋予学时数占比", width = 15, groupName = "智",orderNum = "10")
    private String hoursProcentZ = "0%";

    /**
     * 参与人次
     */
    @Excel(name = "参与人次", width = 15, groupName = "智",orderNum = "11")
    private String personsZ = "0";

    /**
     * 参与人次占比
     */
    @Excel(name = "参与人次占比", width = 15, groupName = "智",orderNum = "12")
    private String personsProcentZ = "0%";

    /**
     * 组织项目数
     */
    @Excel(name = "组织项目数", width = 15, groupName = "体",orderNum = "13")
    private String itemsT = "0";

    /**
     * 组织项目数占比
     */
    @Excel(name = "组织项目数占比", width = 15, groupName = "体",orderNum = "14")
    private String itemsProcentT = "0%";

    /**
     * 赋予学时数
     */
    @Excel(name = "赋予学时数", width = 15, groupName = "体",orderNum = "15")
    private String hoursT = "0";

    /**
     * 赋予学时数占比
     */
    @Excel(name = "赋予学时数占比", width = 15, groupName = "体",orderNum = "16")
    private String hoursProcentT = "0%";

    /**
     * 参与人次
     */
    @Excel(name = "参与人次", width = 15, groupName = "体",orderNum = "17")
    private String personsT = "0";

    /**
     * 参与人次占比
     */
    @Excel(name = "参与人次占比", width = 15, groupName = "体",orderNum = "18")
    private String personsProcentT = "0%";

    /**
     * 组织项目数
     */
    @Excel(name = "组织项目数", width = 15, groupName = "美",orderNum = "19")
    private String itemsM = "0";

    /**
     * 组织项目数占比
     */
    @Excel(name = "组织项目数占比", width = 15, groupName = "美",orderNum = "20")
    private String itemsProcentM = "0%";

    /**
     * 赋予学时数
     */
    @Excel(name = "赋予学时数", width = 15, groupName = "美",orderNum = "21")
    private String hoursM = "0";

    /**
     * 赋予学时数占比
     */
    @Excel(name = "赋予学时数占比", width = 15, groupName = "美",orderNum = "22")
    private String hoursProcentM = "0%";

    /**
     * 参与人次
     */
    @Excel(name = "参与人次", width = 15, groupName = "美",orderNum = "23")
    private String personsM = "0";

    /**
     * 参与人次占比
     */
    @Excel(name = "参与人次占比", width = 15, groupName = "美",orderNum = "24")
    private String personsProcentM = "0%";

    /**
     * 组织项目数
     */
    @Excel(name = "组织项目数", width = 15, groupName = "劳",orderNum = "25")
    private String itemsL = "0";

    /**
     * 组织项目数占比
     */
    @Excel(name = "组织项目数占比", width = 15, groupName = "劳",orderNum = "26")
    private String itemsProcentL = "0%";

    /**
     * 赋予学时数
     */
    @Excel(name = "赋予学时数", width = 15, groupName = "劳",orderNum = "27")
    private String hoursL = "0";

    /**
     * 赋予学时数占比
     */
    @Excel(name = "赋予学时数占比", width = 15, groupName = "劳",orderNum = "28")
    private String hoursProcentL = "0%";

    /**
     * 参与人次
     */
    @Excel(name = "参与人次", width = 15, groupName = "劳",orderNum = "29")
    private String personsL = "0";

    /**
     * 参与人次占比
     */
    @Excel(name = "参与人次占比", width = 15, groupName = "劳",orderNum = "30")
    private String personsProcentL = "0%";

    public ScItemStaticDepartDto() {

    }

    /**
     * 为了统计分析的构造器
     * @param map  特定的map  为了统计分析的
     */
    public ScItemStaticDepartDto(Map<String, BigDecimal> map) {
        this.name = "合计";
        this.itemsD = map.get("ditems") == null ? "0" : map.get("ditems").toString();
        this.itemsProcentD = map.get("ditems") == null ? "0%" : "100%";
        this.hoursD = map.get("dhours")== null ? "0" : map.get("dhours").toString();
        this.hoursProcentD = map.get("dhours") == null ? "0%" : "100%";
        this.personsD = map.get("dpersons")== null ? "0" : map.get("dpersons").toString();
        this.personsProcentD = map.get("dpersons") == null ? "0%" : "100%";
        this.itemsZ = map.get("zitems")== null ? "0" : map.get("zitems").toString();
        this.itemsProcentZ = map.get("zitems") == null ? "0%" : "100%";
        this.hoursZ = map.get("zhours")== null ? "0" : map.get("zhours").toString();
        this.hoursProcentZ = map.get("zhours") == null ? "0%" : "100%";
        this.personsZ = map.get("zpersons")== null ? "0" : map.get("zpersons").toString();
        this.personsProcentZ = map.get("zpersons") == null ? "0%" : "100%";
        this.itemsT = map.get("titems")== null ? "0" : map.get("titems").toString();
        this.itemsProcentT = map.get("titems") == null ? "0%" : "100%";
        this.hoursT = map.get("thours")== null ? "0" : map.get("thours").toString();
        this.hoursProcentT = map.get("thours") == null ? "0%" : "100%";
        this.personsT = map.get("tpersons")== null ? "0" : map.get("tpersons").toString();
        this.personsProcentT = map.get("tpersons") == null ? "0%" : "100%";
        this.itemsM = map.get("mitems")== null ? "0" : map.get("mitems").toString();
        this.itemsProcentM = map.get("mitems") == null ? "0%" : "100%";
        this.hoursM = map.get("mhours")== null ? "0" : map.get("mhours").toString();
        this.hoursProcentM = map.get("mhours") == null ? "0%" : "100%";
        this.personsM = map.get("mpersons")== null ? "0" : map.get("mpersons").toString();
        this.personsProcentM = map.get("mpersons") == null ? "0%" : "100%";
        this.itemsL = map.get("litems")== null ? "0" : map.get("litems").toString();
        this.itemsProcentL = map.get("litems") == null ? "0%" : "100%";
        this.hoursL = map.get("lhours")== null ? "0" : map.get("lhours").toString();
        this.hoursProcentL = map.get("lhours") == null ? "0%" : "100%";
        this.personsL = map.get("lpersons")== null ? "0" : map.get("lpersons").toString();
        this.personsProcentL = map.get("lpersons") == null ? "0%" : "100%";
    }
}
