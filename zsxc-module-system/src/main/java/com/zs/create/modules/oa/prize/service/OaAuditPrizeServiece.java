package com.zs.create.modules.oa.prize.service;

public interface OaAuditPrizeServiece {

    /**
     * 根据角色查找审核人
     * @param role
     * @return
     */
    String auditPrizeByRoleUser(String role);


    /**
     * 团支部负责人
     * @param depId
     * @return
     */
    String getPersonInCharge(String depId);


    /**
     * 团支部指导老师
     * @param depId
     * @return
     */
    String getGuideTeachers(String depId);


    /**
     * 院团委负责人
     * @param applyUserId
     * @param role
     * @return
     */
    String getYouthLeagueCommittee(String applyUserId, String role);

    /**
     * 上级党组织（二课实施小组）
     * @param depId
     * @param role
     * @return
     */
    String getTwoCourseGroup(String depId, String role);

    /**
     * 获取二级机构负责人
     * @param depId
     * @return
     */
    String getTwoOrganization(String depId);

}
