package com.zs.create.modules.mq.entity;

import com.zs.create.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.annotation.Transient;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;

@Document("sys_message")
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class BrokerMessageLogDto implements Serializable {

    /**
     * 消息数据集合名称
     */
    public static final String SYS_MESSAGE_COLLECTION_NAME = "sys_message";
    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 消息标题(冗余字段，系统消息查询使用)
     */
    private String title ;
    /**
     * 发送消息的json数据
     */
    private String messageObject;

    /**
     * 消息状态 0 ：已发送 1发送成功 2.失败
     */
    @Dict(dicCode="msgSendStatus")
    private Integer status;

    private Integer delFlag;
    /**
     * 创建时间
     */
    private Date createTime ;

    /**
     * 修改时间
     */
    private Date updateTime ;


    /**
     * 消息业务类型（必填 发消息根据这个判断json对应实体类）
     * 1 通知公告
     * 2 系统消息
     */
    private String type ;

    /**
     * 发送者
     */
    private String sendUser;
    /**
     * 发布者id
     */
    private String sendUserId;

    /**
     * 消息是否展示   0: 不展示   1 展示
     */
    @Dict(dicCode = "msg_show")
    private Integer show;

    /**
     * 消息发送渠道 0系统消息 1短信 2邮件 3微信
     */
    private String msgType;

    /**
     * 通告发送对象类型（0 指定用户，1全体用户）
     */
    private String sendType;

    /**
     * 我的消息列表当前登录人是否已读
     */
    @Transient
    private String readFlag;

    /**
     * 消息发布时间--冗余字段 方便手机端通知公告排序
     */
    private Date publicTime;
    /**
     * 是否用于沟通平台,0-否,1-是
     */
    private String suggestionMsg;

    /**
     * 发布状态（0未发布，1已发布，2已撤销）
     */
    private Integer sendStatus;

    private String selectUserIds;

    /**
     * 删除按钮标识、0：展示；1：不展示
     */
    private String removeStatus;

    /**
     * 小程序跳转url
     */
    private String miniAppUrl;
}
