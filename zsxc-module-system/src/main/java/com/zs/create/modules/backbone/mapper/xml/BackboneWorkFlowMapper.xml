<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.backbone.mapper.BackboneWorkFlowMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.backbone.entity.BackboneWorkFlowEntity" id="backboneWorkFlowMap">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="userId" column="user_id"/>
        <result property="time" column="time"/>
        <result property="chargeId" column="charge_id"/>
        <result property="status" column="status"/>
    </resultMap>
    <select id="queryByApplyId" resultType="com.zs.create.modules.backbone.entity.BackboneWorkFlowEntity">
        SELECT * FROM backbone_work_flow
        WHERE apply_id = #{id} and status = #{status}
        <if test="userId != null and userId != ''">
            and user_id = #{userId}
        </if>
    </select>
</mapper>