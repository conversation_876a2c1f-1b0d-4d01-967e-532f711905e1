package com.zs.create.modules.communication.suggest.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 建议操作
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-03-04 15:08:41
 */
@Data
@TableName("cp_suggest_handle")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="cp_suggest_handle对象", description="建议操作")
public class CpSuggestHandleEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	public static final Integer GZ = 0;
	public static final Integer CA = 1;
	public static final Integer ZA = 2;
	/**
	 * 主键
	 */
	@TableId
	private String id;
	/**
	 * 投诉建议id
	 */
	private String suggestId;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 0--关注  1--踩  2--赞
	 */
	private Integer type;
	/**
	 * 删除标记
	 */
	private Integer delFlag;
	/**
	 * 创建人id
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

	/**
	 * 冗余字段  关注或者取消关注0  1
	 */
	@TableField(exist = false)
	private Integer todo;

}
