package com.zs.create.modules.statistic.entity;

import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;

/**
 * <AUTHOR>
 * @date 2022/6/7
 */
@Data
public class ItemHoursWarningTaskParam implements Serializable {

    private LocalDate date;
    private int interval = 4;

    public LocalDate getToday() {
        if (date != null) {
            return date;
        }
        return LocalDate.now();
    }
}
