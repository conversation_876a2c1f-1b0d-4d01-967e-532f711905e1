package com.zs.create.modules.system.entity;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/13
 */
@Accessors(chain = true)
@Data
public class ScientificqiValuePunishment implements Serializable {

    /**
     * 用户id
     */
    private String userId;
    /**
     * 扣除科气值
     */
    private int value;
    /**
     * 限制报名天数
     */
    private int days;
    /**
     * 原因
     */
    private String reason;
}
