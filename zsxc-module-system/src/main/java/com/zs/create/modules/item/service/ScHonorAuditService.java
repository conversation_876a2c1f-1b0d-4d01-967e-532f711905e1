package com.zs.create.modules.item.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.item.entity.ScHonorDeclarationEntity;
import com.zs.create.modules.item.entity.TaskQueryDTO;
import com.zs.create.modules.item.entity.TaskVo;

import java.util.List;
import java.util.Map;

public interface ScHonorAuditService {


    /**
     * 流程审核
     * @param taskId
     * @param auditNote
     * @param assignee
     */
    String auditHonorProcess(String taskId, String auditNote, String assignee, Map<String, Object> variables);


    Page<ScHonorDeclarationEntity> todoTaskPage(ScHonorDeclarationEntity entity, Integer pageNo, Integer pageSize);


    Page<ScHonorDeclarationEntity> historyTaskPage(ScHonorDeclarationEntity entity, Integer pageNo, Integer pageSize);


    Page<ScHonorDeclarationEntity> taskPage(ScHonorDeclarationEntity entity, Integer pageNo, Integer pageSize);

    List<ScHonorDeclarationEntity> taskPageList(ScHonorDeclarationEntity entity);
}
