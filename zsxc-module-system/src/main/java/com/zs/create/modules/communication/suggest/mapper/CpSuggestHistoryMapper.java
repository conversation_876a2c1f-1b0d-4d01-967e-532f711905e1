package com.zs.create.modules.communication.suggest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.communication.suggest.entity.CpSuggestEntity;
import com.zs.create.modules.communication.suggest.entity.CpSuggestHistoryEntity;
import com.zs.create.modules.communication.suggest.entity.CpSuggestVo;
import com.zs.create.modules.system.entity.SysRole;
import com.zs.create.modules.system.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description 投诉建议历史内容Mapper层
 *
 * <AUTHOR>
 * @email
 * @date 2021-11-29 15:08:41
 * @Version: V1.0
 */
public interface CpSuggestHistoryMapper extends BaseMapper<CpSuggestHistoryEntity> {


    void insertHistory(@Param("reply") CpSuggestHistoryEntity reply);

    IPage<CpSuggestHistoryEntity> pageList(@Param("page")Page<CpSuggestHistoryEntity> page, @Param("itemId")String itemId);
}
