package com.zs.create.modules.oa.prize.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.oa.prize.entity.OaPrizeLevelEntity;
import com.zs.create.modules.oa.prize.service.OaPrizeLevelService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * @Description 奖项级别Controller层
 *
 * <AUTHOR>
 * @email null
 * @date 2021-01-16 14:42:14
 * @Version: V1.0
 */
@Slf4j
@Api(tags="奖项级别")
@RestController
@RequestMapping("/prize/oaPrizeLevel")
public class OaPrizeLevelController {
    @Autowired
    private OaPrizeLevelService oaPrizeLevelService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "奖项级别-分页列表查询")
    @ApiOperation(value="奖项级别-分页列表查询", notes="奖项级别-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<OaPrizeLevelEntity>> queryPageList(OaPrizeLevelEntity oaPrizeLevel,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<OaPrizeLevelEntity>> result = new Result<>();
        QueryWrapper<OaPrizeLevelEntity> queryWrapper = QueryGenerator.initQueryWrapper(oaPrizeLevel, req.getParameterMap());
        Page<OaPrizeLevelEntity> page = new Page<>(pageNo, pageSize);
        IPage<OaPrizeLevelEntity> pageList = oaPrizeLevelService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 分页列表查询
     */
    @AutoLog(value = "根据奖项id查询奖项等级")
    @ApiOperation(value="根据奖项id查询奖项等级", notes="根据奖项id查询奖项等级")
    @GetMapping(value = "/getPrizeList")
    public Result<List<OaPrizeLevelEntity>> getPrizeList(@RequestParam(name="prizeId") String prizeId) {
        Result<List<OaPrizeLevelEntity>> result = new Result<>();
        QueryWrapper<OaPrizeLevelEntity> queryWrapper =new QueryWrapper<>();
        queryWrapper.eq("prize_id",prizeId);
        queryWrapper.lambda().orderByAsc(OaPrizeLevelEntity::getHours);
        List<OaPrizeLevelEntity> list=oaPrizeLevelService.list(queryWrapper);
        result.setSuccess(true);
        result.setResult(list);
        return result;
    }


    /**
     * 添加
     */
    @AutoLog(value = "奖项级别-添加")
    @ApiOperation(value="奖项级别-添加", notes="奖项级别-添加")
    @PostMapping(value = "/add")
    public Result<OaPrizeLevelEntity> add(@RequestBody OaPrizeLevelEntity oaPrizeLevel) {
        Result<OaPrizeLevelEntity> result = new Result<>();
        try {
            oaPrizeLevelService.save(oaPrizeLevel);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "奖项级别-编辑")
    @ApiOperation(value="奖项级别-编辑", notes="奖项级别-编辑")
    @PutMapping(value = "/edit")
    public Result<OaPrizeLevelEntity> edit(@RequestBody OaPrizeLevelEntity oaPrizeLevel) {
        Result<OaPrizeLevelEntity> result = new Result<>();
        OaPrizeLevelEntity oaPrizeLevelEntity = oaPrizeLevelService.getById(oaPrizeLevel.getId());
        if(oaPrizeLevelEntity==null) {
            result.error500("未找到对应实体");
        }else {
            boolean ok = oaPrizeLevelService.updateById(oaPrizeLevel);
            if(ok) {
                result.success("修改成功!");
            }
        }
        return result;
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "奖项级别-通过id删除")
    @ApiOperation(value="奖项级别-通过id删除", notes="奖项级别-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        try {
            oaPrizeLevelService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败",e.getMessage());
            return Result.error("删除失败!");
        }
		return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "奖项级别-批量删除")
    @ApiOperation(value="奖项级别-批量删除", notes="奖项级别-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<OaPrizeLevelEntity> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<OaPrizeLevelEntity> result = new Result<>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.oaPrizeLevelService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "奖项级别-通过id查询")
    @ApiOperation(value="奖项级别-通过id查询", notes="奖项级别-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OaPrizeLevelEntity> queryById(@RequestParam(name="id",required=true) String id) {
        Result<OaPrizeLevelEntity> result = new Result<>();
        OaPrizeLevelEntity oaPrizeLevel = oaPrizeLevelService.getById(id);
        if(oaPrizeLevel==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(oaPrizeLevel);
            result.setSuccess(true);
        }
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<OaPrizeLevelEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                OaPrizeLevelEntity oaPrizeLevel = JSON.parseObject(deString, OaPrizeLevelEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(oaPrizeLevel, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<OaPrizeLevelEntity> pageList = oaPrizeLevelService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "奖项级别列表");
        mv.addObject(NormalExcelConstants.CLASS, OaPrizeLevelEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("奖项级别列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<OaPrizeLevelEntity> listOaPrizeLevels = ExcelImportUtil.importExcel(file.getInputStream(), OaPrizeLevelEntity.class, params);
                oaPrizeLevelService.saveBatch(listOaPrizeLevels);
                return Result.ok("文件导入成功！数据行数:" + listOaPrizeLevels.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
