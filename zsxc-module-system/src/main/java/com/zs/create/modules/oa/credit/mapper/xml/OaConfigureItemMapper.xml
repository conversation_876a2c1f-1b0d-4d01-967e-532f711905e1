<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.credit.mapper.OaConfigureItemMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.credit.entity.OaConfigureItemEntity" id="oaConfigureItemMap">
        <result property="id" column="id"/>
        <result property="itemId" column="item_id"/>
        <result property="courseId" column="course_id"/>
        <result property="itemName" column="item_name"/>
        <result property="module" column="module"/>
        <result property="form" column="form"/>
        <result property="linkMan" column="link_man"/>
        <result property="tel" column="tel"/>
        <result property="examineStatus" column="examine_status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="itemTime" column="item_time"/>
    </resultMap>

    <select id="itemFinishlist" resultType="com.zs.create.modules.item.entity.ScItemEntity">
        select a.* from sc_item a
        inner join sc_hours_directory b
        on a.id=b.item_id
        where a.examine_status in ('34','40')
        and a.id not in(SELECT item_id from oa_configure_item where del_flag='0')
        and b.status='10'
        and a.item_category='0'
        <if test="scItemEntity.itemName != null and scItemEntity.itemName!=''">
            and a.`item_name` like concat('%',#{scItemEntity.itemName} ,'%')
        </if>
        <if test="scItemEntity.module != null and scItemEntity.module!=''">
            and a.`module` like concat('%',#{scItemEntity.module} ,'%')
        </if>
        <if test="scItemEntity.form != null and scItemEntity.form!=''">
            and a.`form` like concat('%',#{scItemEntity.form} ,'%')
        </if>
        ORDER BY a.create_time DESC
    </select>

</mapper>