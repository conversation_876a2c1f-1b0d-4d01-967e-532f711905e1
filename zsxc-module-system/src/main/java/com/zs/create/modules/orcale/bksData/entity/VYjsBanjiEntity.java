package com.zs.create.modules.orcale.bksData.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 信息中心研究生数据
 *
 * <AUTHOR> @email
 * @date 2023-03-21 14:52:54
 */
@Data
@TableName("v_yjs_banji")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "v_yjs_banji对象", description = "信息中心研究生数据")
public class VYjsBanjiEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "")
    private String code;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String grade;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String deptCode;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String zylx;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String code1;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String bzrCode;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String bzrName;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String name;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String engname;
    /**
     *
     */
    @ApiModelProperty(value = "")
    private String fullname;

}
