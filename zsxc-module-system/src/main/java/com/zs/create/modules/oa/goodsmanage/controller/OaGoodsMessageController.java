package com.zs.create.modules.oa.goodsmanage.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.communication.suggest.mapper.CpSuggestMapper;
import com.zs.create.modules.oa.goodsmanage.entity.OaGoodsBorrowEntity;
import com.zs.create.modules.oa.goodsmanage.entity.OaGoodsMessageEntity;
import com.zs.create.modules.oa.goodsmanage.service.OaGoodsBorrowService;
import com.zs.create.modules.oa.goodsmanage.service.OaGoodsMessageService;
import com.zs.create.modules.system.entity.SysUser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * @Description 物资管理Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-18 09:56:34
 * @Version: V1.0
 */
@Slf4j
@Api(tags="物资管理")
@RestController
@RequestMapping("/goodsManage/oaGoodsMessage")
public class OaGoodsMessageController {
    @Autowired
    private OaGoodsMessageService oaGoodsMessageService;
    @Autowired
    private OaGoodsBorrowService oaGoodsBorrowService;
    @Autowired
    private CpSuggestMapper cpSuggestMapper;
    /**
     * 分页列表查询
     */
    @AutoLog(value = "物资管理-分页列表查询")
    @ApiOperation(value="物资管理-分页列表查询", notes="物资管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<OaGoodsMessageEntity>> queryPageList(OaGoodsMessageEntity oaGoodsMessage,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<OaGoodsMessageEntity>> result = new Result<IPage<OaGoodsMessageEntity>>();
        QueryWrapper<OaGoodsMessageEntity> queryWrapper = new QueryWrapper<OaGoodsMessageEntity>();
        Page<OaGoodsMessageEntity> page = new Page<OaGoodsMessageEntity>(pageNo, pageSize);
        if(null !=oaGoodsMessage.getGoodsName()&& ""!=oaGoodsMessage.getGoodsName()){
            queryWrapper.like("goods_name",oaGoodsMessage.getGoodsName());
        }
        queryWrapper.orderByDesc("create_time");
        IPage<OaGoodsMessageEntity> pageList = oaGoodsMessageService.page(page, queryWrapper);
      //  List<OaGoodsMessageEntity> list =  pageList.getRecords();
        //循环获取剩余数量
        for(int i=0;i < pageList.getRecords().size() ;i++ ){
            OaGoodsMessageEntity oaGoodsMessageEntity = pageList.getRecords().get(i);
            OaGoodsBorrowEntity oaGoodsBorrow = oaGoodsBorrowService.getSurplusNumById(oaGoodsMessageEntity.getId());
            if(oaGoodsBorrow!=null){
                oaGoodsMessageEntity.setSurplusNum(oaGoodsMessageEntity.getGoodsNum()-oaGoodsBorrow.getApplyNum());
            }else {
                oaGoodsMessageEntity.setSurplusNum(oaGoodsMessageEntity.getGoodsNum());
            }
        }
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "物资管理-添加")
    @ApiOperation(value="物资管理-添加", notes="物资管理-添加")
    @PostMapping(value = "/add")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<OaGoodsMessageEntity> add(@RequestBody OaGoodsMessageEntity oaGoodsMessage) {
            Result<OaGoodsMessageEntity> result = new Result<OaGoodsMessageEntity>();
            oaGoodsMessage.setDelFlag(DelFlagEnum.NO_DEL.getCode());
            oaGoodsMessageService.save(oaGoodsMessage);
            result.success("提交成功！");
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "物资管理-编辑")
    @ApiOperation(value="物资管理-编辑", notes="物资管理-编辑")
    @PutMapping(value = "/edit")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<OaGoodsMessageEntity> edit(@RequestBody OaGoodsMessageEntity oaGoodsMessage) {
        Result<OaGoodsMessageEntity> result = new Result<OaGoodsMessageEntity>();
        OaGoodsMessageEntity oaGoodsMessageEntity = oaGoodsMessageService.getById(oaGoodsMessage.getId());

        if(oaGoodsMessageEntity==null) {
           return result.error500("未找到对应实体");
        }else {
            //查找借出数量
            OaGoodsBorrowEntity oaGoodsBorrow = oaGoodsBorrowService.getSurplusNumById(oaGoodsMessageEntity.getId());
            if(oaGoodsBorrow !=null){
                if(oaGoodsMessageEntity.getGoodsNum() < oaGoodsBorrow.getApplyNum()){
                    return  result.error500("提交失败,物品数量不可少于已借出数量!");
                }
            }
            boolean ok = oaGoodsMessageService.updateById(oaGoodsMessage);
            return ok ? result.success("提交成功!") : result.error500("提交失败!");
        }
    }


    /**
     * 编辑
     */
    @AutoLog(value = "物资管理-启用禁用")
    @ApiOperation(value="物资管理-启用禁用", notes="物资管理-启用禁用")
    @PutMapping(value = "/isUse")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<OaGoodsMessageEntity> isUse(@RequestBody OaGoodsMessageEntity oaGoodsMessage) {
        Result<OaGoodsMessageEntity> result = new Result<OaGoodsMessageEntity>();
        OaGoodsMessageEntity oaGoodsMessageEntity = oaGoodsMessageService.getById(oaGoodsMessage.getId());
        if(oaGoodsMessageEntity==null) {
            return result.error500("未找到对应实体");
        }else {
            //启用
            if(oaGoodsMessage.getIsUse() == 0 ){
                boolean ok = oaGoodsMessageService.updateById(oaGoodsMessage);
                if(ok){
                    return  result.success("启用成功!");
                }else{
                    return  result.success("启用失败!");
                }
            }else{
                boolean ok = oaGoodsMessageService.updateById(oaGoodsMessage);
                if(ok){
                    return  result.success("禁用成功!");
                }else{
                    return  result.success("禁用失败!");
                }
            }

        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "物资管理-通过id删除")
    @ApiOperation(value="物资管理-通过id删除", notes="物资管理-通过id删除")
    @DeleteMapping(value = "/delete")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        //获取物品借出数量,若有借,则不允许删除
        OaGoodsBorrowEntity oaGoodsBorrow = oaGoodsBorrowService.getBorrowByGoodsId(id);
        if(oaGoodsBorrow ==null){
            oaGoodsMessageService.removeById(id);
            return Result.ok("删除成功!");
        }else{
            return Result.error("存在物品被借用记录,不能删除!");
        }


    }

    /**
      *  批量删除
     */
    @AutoLog(value = "物资管理-批量删除")
    @ApiOperation(value="物资管理-批量删除", notes="物资管理-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<OaGoodsMessageEntity> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<OaGoodsMessageEntity> result = new Result<OaGoodsMessageEntity>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.oaGoodsMessageService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "物资管理-通过id查询")
    @ApiOperation(value="物资管理-通过id查询", notes="物资管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OaGoodsMessageEntity> queryById(@RequestParam(name="id",required=true) String id) {
        Result<OaGoodsMessageEntity> result = new Result<OaGoodsMessageEntity>();
        OaGoodsMessageEntity oaGoodsMessage = oaGoodsMessageService.getById(id);

        if(oaGoodsMessage==null) {
            result.error500("未找到对应实体");
        }else {
            OaGoodsBorrowEntity oaGoodsBorrow = oaGoodsBorrowService.getSurplusNumById(id);
            if(oaGoodsBorrow!=null){
                oaGoodsMessage.setSurplusNum(oaGoodsMessage.getGoodsNum()-oaGoodsBorrow.getApplyNum());
            }else {
                oaGoodsMessage.setSurplusNum(oaGoodsMessage.getGoodsNum());
            }

            result.setResult(oaGoodsMessage);
            result.setSuccess(true);
        }
        return result;
    }
    @AutoLog(value = "物资管理-物资借用历史分页列表查询")
    @ApiOperation(value="物资管理-物资借用历史分页列表查询", notes="物资管理-物资借用历史分页列表查询")
    @GetMapping(value = "/borrowHistoryList")
    public Result<IPage<OaGoodsBorrowEntity>> queryBorrowHistoryList(OaGoodsMessageEntity oaGoodsMessage,
                                                            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                            HttpServletRequest req) {
        Result<IPage<OaGoodsBorrowEntity>> result = new Result<IPage<OaGoodsBorrowEntity>>();
        QueryWrapper<OaGoodsBorrowEntity> queryWrapper = new QueryWrapper<OaGoodsBorrowEntity>();
        Page<OaGoodsBorrowEntity> page = new Page<OaGoodsBorrowEntity>(pageNo, pageSize);
        queryWrapper.eq("goods_id", oaGoodsMessage.getId());
        queryWrapper.eq("del_flag", 0);
        queryWrapper.orderByDesc("create_time");
        IPage<OaGoodsBorrowEntity> pageList = oaGoodsBorrowService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }
    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<OaGoodsMessageEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                OaGoodsMessageEntity oaGoodsMessage = JSON.parseObject(deString, OaGoodsMessageEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(oaGoodsMessage, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<OaGoodsMessageEntity> pageList = oaGoodsMessageService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "物资管理列表");
        mv.addObject(NormalExcelConstants.CLASS, OaGoodsMessageEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("物资管理列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<OaGoodsMessageEntity> listOaGoodsMessages = ExcelImportUtil.importExcel(file.getInputStream(), OaGoodsMessageEntity.class, params);
                oaGoodsMessageService.saveBatch(listOaGoodsMessages);
                return Result.ok("文件导入成功！数据行数:" + listOaGoodsMessages.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }

    @AutoLog(value = "获取仓管员列表下拉框")
    @ApiOperation(value="获取仓管员列表下拉框", notes="获取仓管员列表下拉框")
    @GetMapping(value = "/queryWareHousePersonList")
    public Result<List<SysUser>> queryPersonList(HttpServletRequest req) {
        Result<List<SysUser>> result = new Result<List<SysUser>>();
        List<SysUser> list =  cpSuggestMapper.getUserListByRoleCode("role_warehouse_admin");
        result.setSuccess(true);
        result.setResult(list);
        return result;
    }
}
