package com.zs.create.modules.score.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/13
 */
@ApiModel(value = "静态成绩单下载参数", description = "静态成绩单下载参数")
@Accessors(chain = true)
@Data
public class ScScoreReportCardDownloadDTO implements Serializable {

    /**
     * 学年
     */
    @ApiModelProperty(value = "学年")
    private String xn;
    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private List<String> userIdList;
}

