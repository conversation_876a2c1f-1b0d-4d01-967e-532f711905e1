package com.zs.create.modules.workflow.deploy.service.impl;

import com.zs.create.modules.workflow.common.bpmn.BpmnModelHandler;
import com.zs.create.modules.workflow.common.entity.ActCommTaskAssignee;
import com.zs.create.modules.workflow.common.entity.BpmnYiCHangAction;
import com.zs.create.modules.workflow.common.entity.ExclusiveGatewayConfig;
import com.zs.create.modules.workflow.common.enums.BpmnGateWayTypeEumn;
import com.zs.create.modules.workflow.common.exception.DeployException;
import com.zs.create.modules.workflow.deploy.service.IProcessDeployService;
import com.zs.create.util.MonogoUtil;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.Deployment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> guodl
 * @Date: 2019/8/8 09:04
 * @Description:流程部署相关的service, 业务逻辑中可以重新继承该方法重写
 * 指定bean的名称为：deployService
 */
@Slf4j
@Service(value = "deployService")
public class ProcessDeployService implements IProcessDeployService {
    @Autowired
    RepositoryService repositoryService;
    @Autowired
    JdbcTemplate jdbcTemplate;
    @Autowired
    private MonogoUtil monogoUtil;

    private static final String COLLECTION_NAME = "workflow_model";

    /**
     * 调用流程部署
     *
     * @param handler bmpn模型处理器
     * @return Deployment
     */
    @Override
    @Transactional
    public Deployment invokeWorkFlowDeploy(BpmnModelHandler handler) {
        if (null == handler) {
            throw new DeployException("bpmn 模型处理器【BpmnModelHandler】为空");
        }
        if (null == handler.getBpmnModel()) {
            throw new DeployException("bpmn 模型对象【BpmnModelHandler BpmnModel】为空");
        }
        //流程部署
        Deployment deployment = null;
        try {
            deployment = repositoryService.createDeployment()
                    .addBpmnModel(handler.getProcessKey() + ".bpmn", handler.getBpmnModel())
                    .name(handler.getProcessName())
                    .deploy();
        } catch (Exception e) {
            e.printStackTrace();
            throw new DeployException("流程部署失败【" + handler.getProcessKey() + "】", e);
        }
        //将流程中的任务节点办理人信息入库，流程执行时调用环节的办理人
        List<ActCommTaskAssignee> taskAssigneeList = handler.getTaskAssigneeList();
        if (null == taskAssigneeList || taskAssigneeList.size() == 0) {
            throw new DeployException("流程部署失败，环节未指定办理人");
        }
        try {
            //保存环节办理人
            saveNodeUser(taskAssigneeList, deployment.getId());
            List<List<BpmnYiCHangAction>> bpmnYiCHangActionList = handler.getBpmnYiCHangActionList();
            //保存异常动作
            saveYichangAction(bpmnYiCHangActionList, deployment.getId());
            //保存网关信息
            //将流程中的排他网关条件入库，流程执行时调用网关走向
            List<List<ExclusiveGatewayConfig>> bpmnGateConditionList = handler.getBpmnGateConditionList();
            saveGateWay(bpmnGateConditionList, deployment.getId());
        } catch (Exception e) {
            throw new DeployException(e.getMessage());
        }

        log.info("流程成功部署id【{}】名称【{}】", deployment.getId(), deployment.getName());
        return deployment;
    }

    /**
     * 获取部署流程图
     *
     * @param processDefId
     * @return
     */
    @Override
    public InputStream getDeployPic(String processDefId) {
        InputStream processDiagram = repositoryService
                .getProcessDiagram(processDefId);
        return processDiagram;
    }

    /**
     * 保存环节办理人
     *
     * @param taskAssigneeList 任务办理人list
     * @param deployId         流程部署ID
     */
    public void saveNodeUser(List<ActCommTaskAssignee> taskAssigneeList, String deployId) {
        if (null != taskAssigneeList && taskAssigneeList.size() > 0) {
            StringBuffer sql = new StringBuffer("insert into act_comm_task_assignee(deployment_id,activiti_id,activiti_name,assignee_type,assignee_val) VALUES (?,?,?,?,?)");
            Object[] params = null;
            List<Object[]> list = new ArrayList<>();
            for (ActCommTaskAssignee t : taskAssigneeList) {

                if (null == t || t.getAssigneeType() == null
                        || "".equals(t.getAssigneeType()) || t.getAssigneeVal() == null || "".equals(t.getAssigneeVal())) {
                    throw new DeployException("流程部署失败，【" + t.getActivitiName() + "】未指定办理人");
                }
                params = new Object[]{deployId, t.getActivitiId(), t.getActivitiName(), t.getAssigneeType(), t.getAssigneeVal()};
                list.add(params);
            }
            try {
                if (null != list && list.size() > 0) {
                    jdbcTemplate.batchUpdate(sql.toString(), list);
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new DeployException("流程部署失败【环节办理人入库失败】", e);
            }

        }
    }

    /**
     * 保存异常动作信息
     *
     * @param bpmnYiCHangActionList
     * @param deployId
     */
    public void saveYichangAction(List<List<BpmnYiCHangAction>> bpmnYiCHangActionList, String deployId) {
        //将流程中的驳回等信息入库，在执行驳回、挂起等异常方法时调用
        if (null != bpmnYiCHangActionList && bpmnYiCHangActionList.size() > 0) {
            StringBuffer sql = new StringBuffer(" insert into act_comm_yichang_action_config(deployment_id,task_node_id,target_node_id,target_node_name,condition_,action_type) values(?,?,?,?,?,?)");
            Object[] params = null;
            List<Object[]> list = new ArrayList<>();
            for (int i = 0; i < bpmnYiCHangActionList.size(); i++) {
                List<BpmnYiCHangAction> bpmnYiCHangActions = bpmnYiCHangActionList.get(i);
                if (null != bpmnYiCHangActions && bpmnYiCHangActions.size() > 0) {
                    for (BpmnYiCHangAction bpmnYiCHangAction : bpmnYiCHangActions) {
                        if (null == bpmnYiCHangAction || null == bpmnYiCHangAction.getCondition() || "".equals(bpmnYiCHangAction.getCondition())) {
                            throw new DeployException("流程部署失败【请指定驳回等动作的执行条件】");
                        }
                        params = new Object[]{deployId, bpmnYiCHangAction.getTaskNodeId(),
                                bpmnYiCHangAction.getTargetNodeId(), bpmnYiCHangAction.getTargetNodeName(),
                                bpmnYiCHangAction.getCondition(), bpmnYiCHangAction.getActionType()};
                        list.add(params);
                    }
                }
            }

            try {
                if (null != list && list.size() > 0) {
                    jdbcTemplate.batchUpdate(sql.toString(), list);
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new DeployException("流程部署失败【驳回信息入库失败】", e);
            }

        }
    }

    /**
     * 保存网关信息
     *
     * @param bpmnGateConditionList
     * @param deployId
     */
    public void saveGateWay(List<List<ExclusiveGatewayConfig>> bpmnGateConditionList, String deployId) {
        if (null != bpmnGateConditionList && bpmnGateConditionList.size() > 0) {
            StringBuffer sql = new StringBuffer(" insert into act_comm_gateway_config(deployment_id,gateway_id,gateway_type,target_node_id,condition_) values(?,?,?,?,?)");
            Object[] params = null;
            List<Object[]> list = new ArrayList<>();
            for (int i = 0; i < bpmnGateConditionList.size(); i++) {
                List<ExclusiveGatewayConfig> bpmnGateConditions = bpmnGateConditionList.get(i);
                if (null != bpmnGateConditions && bpmnGateConditions.size() > 0) {
                    for (ExclusiveGatewayConfig config : bpmnGateConditions) {
                        if (null == config.getTo() || "".equals(config.getTo())) {
                            throw new DeployException("网关需要有目标节点！");
                        }
                        if (null == config.getCondition() || "".equals(config.getCondition())) {
                            throw new DeployException("网关未指定执行条件！");
                        }

                        params = new Object[]{deployId, config.getGateWayId(), BpmnGateWayTypeEumn.ExclusiveGateway.toString(), config.getTo(), config.getCondition()};
                        list.add(params);
                    }
                }
            }

            try {
                if (null != list && list.size() > 0) {
                    jdbcTemplate.batchUpdate(sql.toString(), list);
                }
            } catch (Exception e) {
                e.printStackTrace();
                throw new DeployException("流程部署失败【网关信息入库失败】", e);
            }

        }
    }
}

