package com.zs.create.modules.oa.prize.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.modules.oa.prize.entity.OaPrizeEntity;
import com.zs.create.modules.oa.prize.entity.OaPrizeImportUsersEntity;
import com.zs.create.modules.oa.prize.mapper.OaPrizeImportUsersMapper;
import com.zs.create.modules.oa.prize.service.OaPrizeImportUsersService;
import com.zs.create.modules.system.entity.SysUser;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Description 奖项发起导入人员范围Service实现层
 *
 * <AUTHOR> @email 
 * @date 2023-03-01 09:05:32
 * @Version: V1.0
 */
@Service
public class OaPrizeImportUsersServiceImpl extends ServiceImpl<OaPrizeImportUsersMapper, OaPrizeImportUsersEntity> implements OaPrizeImportUsersService {

    @Override
    public OaPrizeImportUsersEntity getByPrizeIdAndUserId(String prizeId, String userId,String uuidKey) {
        return this.baseMapper.getByPrizeIdAndUserId(prizeId,userId,uuidKey);
    }

    @Override
    public List<SysUser> qryUsersByPrize(OaPrizeEntity oaPrizeEntity) {
        return this.baseMapper.qryUsersByPrize(oaPrizeEntity);
    }
}
