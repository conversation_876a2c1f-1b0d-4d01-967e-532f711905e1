package com.zs.create.modules.item.asynchandel;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.entity.ScItemHoursEntity;
import com.zs.create.modules.item.entity.ScItemSignEntity;
import com.zs.create.modules.item.entity.ScItemWorksEntity;
import com.zs.create.modules.item.service.ScItemHoursService;
import com.zs.create.modules.item.service.ScItemService;
import com.zs.create.modules.item.service.ScItemSignService;
import com.zs.create.modules.item.service.ScItemWorksService;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @description:   处理老数据的接口
 * @author: anshenghui
 * @create: 2021-05-17 14:45
 **/
@RestController
@RequestMapping("/handel")
public class handelOldData {

    @Autowired
    private ScItemService scItemService;

    @Autowired
    private ScItemHoursService scItemHoursService;

    @Autowired
    private ScItemSignService scItemSignService;

    @Autowired
    private ScItemWorksService scItemWorksService;

    @ApiOperation(value = "处理老数据", notes = "处理老数据")
    @GetMapping(value = "/oldData")
    public String handelOldData() {
//        QueryWrapper<ScItemEntity> queryWrapper = new QueryWrapper<>();
//        queryWrapper.eq("id", "48e6211f937c614c0513d48891f28054");
        List<ScItemEntity> list = scItemService.list();
        for (int i = 0; i < list.size(); i++) {
            ScItemEntity scItemEntity = list.get(i);
            List<ScItemHoursEntity> scItemHoursEntities = scItemHoursService.list(new QueryWrapper<ScItemHoursEntity>().eq("del_flag", 0).eq("item_id", scItemEntity.getId()).eq("status", 10));
            BigDecimal bigDecimal = new BigDecimal(0);
            for (ScItemHoursEntity scItemHoursEntity : scItemHoursEntities) {
                bigDecimal = bigDecimal.add(scItemHoursEntity.getHours());
            }
//            int count = scItemSignService.count(new QueryWrapper<ScItemSignEntity>().eq("item_id", scItemEntity.getId()).eq("type", 1));
            int works = scItemWorksService.count(new QueryWrapper<ScItemWorksEntity>().eq("item_id", scItemEntity.getId()));
            int sings = 0;
            List<ScItemSignEntity> daos = scItemSignService.list(new QueryWrapper<ScItemSignEntity>().eq("item_id", scItemEntity.getId()).eq("type", 0));
            List<ScItemSignEntity> tuis = scItemSignService.list(new QueryWrapper<ScItemSignEntity>().eq("item_id", scItemEntity.getId()).eq("type", 1));
            List<String> tuiUserCodes = new ArrayList<>();
            for (ScItemSignEntity scItemSignEntity : tuis) {
                tuiUserCodes.add(scItemSignEntity.getUserCode());
            }
            Set<String> set = new HashSet<>();
            for (ScItemSignEntity dao : daos) {
                set.add(dao.getUserCode());
            }
            for (String s : set) {
                for (String tuiUserCode : tuiUserCodes) {
                    if (tuiUserCode.equals(s)) {
                        sings++;
                        break;
                    }
                }
            }
            scItemService.updateHoursAndPersons(scItemEntity.getId(), bigDecimal, works + sings);
            System.out.println("处理至第*****" + i);
        }
        return "chenggong";
    }
}
