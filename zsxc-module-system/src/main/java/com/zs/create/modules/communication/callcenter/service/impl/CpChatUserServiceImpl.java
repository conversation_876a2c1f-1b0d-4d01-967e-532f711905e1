package com.zs.create.modules.communication.callcenter.service.impl;

import com.zs.create.modules.communication.callcenter.entity.CpChatUserEntity;
import com.zs.create.modules.communication.callcenter.entity.dto.ChatUserDTO;
import com.zs.create.modules.communication.callcenter.repository.CpChatMessageRepository;
import com.zs.create.modules.communication.callcenter.repository.CpChatUserRepository;
import com.zs.create.modules.communication.callcenter.service.ICpChatUserService;
import com.zs.create.modules.weixin.dto.WxUserInfoDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.aggregation.Aggregation.*;

/**
 * @className: CpChatUserServiceImpl
 * @description:  聊天对象service
 * @author: hy
 * @date: 2020-12-1
 **/
@Service
public class CpChatUserServiceImpl implements ICpChatUserService {
    @Autowired
    CpChatUserRepository cpChatUserRepository;
    @Autowired
    CpChatMessageRepository cpChatMessageRepository;
    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    SysWeixinUserService sysWeixinUserService;

    /**
     * 获取聊天人员列表
     * @param reciveUserId
     * @return
     */
    @Override
    public List<CpChatUserEntity> listChatUser(String reciveUserId) {
        CpChatUserEntity cpChatUserEntity = new CpChatUserEntity()
                .setReciveUserId(reciveUserId);
        Example<CpChatUserEntity> example = Example.of(cpChatUserEntity);
        return cpChatUserRepository.findAll(example);
    }

    /**
     * 获取聊天人员列表 包含 未读数条数字段
     * @param  reciveUserId
     * @return
     */
    @Override
    public List<ChatUserDTO> listChatUserVo(String reciveUserId){
        List<CpChatUserEntity> cpChatUsers
                = this.listChatUser(reciveUserId);
        Set<String> sendUserIds = cpChatUsers.parallelStream()
                .map(d -> d.getSendUserId()).collect(Collectors.toSet());
        if(CollectionUtils.isEmpty(sendUserIds)) return new ArrayList<>();
        List<Map> unReadCountList = this.listUnReadMessageCount(reciveUserId, sendUserIds);
          List<ChatUserDTO> chatUsers = new LinkedList<>();
        cpChatUsers.forEach(chatUser-> {
            ChatUserDTO chatUserDTO = new ChatUserDTO();
            BeanUtils.copyProperties(chatUser , chatUserDTO);
            unReadCountList.forEach(unReadCnt->{
                if(chatUser.getSendUserId().equals(unReadCnt.get("_id"))){
                    chatUserDTO.setUnReadCount((Integer) unReadCnt.get("count"));
                }
            });
            //组装发送人 头像  nickName
            WxUserInfoDTO wxUserInfoDTO = sysWeixinUserService.getWxUserInfoDTO(chatUser.getSendUserId());
            if(null != wxUserInfoDTO){
                chatUserDTO.setHeadImg(wxUserInfoDTO.getHeadImg()).setNickName(wxUserInfoDTO.getNickName());
            }
            chatUsers.add(chatUserDTO);
        });
        return chatUsers;
    }

    @Override
    public Boolean deleteChartUser(String chatUserId, String kfUserId) {
        CpChatUserEntity cpChatUserEntity = new CpChatUserEntity().setSendUserId(chatUserId)
                .setReciveUserId(kfUserId);
         cpChatUserRepository.delete(cpChatUserEntity);
        return Boolean.TRUE;
    }

    public List<Map> listUnReadMessageCount(String reciveUserId , Set<String> sendUserIds){
        Criteria criteria = Criteria.where("sendUserId")
                .in(sendUserIds).and("reciveUserId").is(reciveUserId).and("read").is("0");
                Aggregation unReadNumAgg = newAggregation(
                match(criteria), //搜索条件
                project().and("sendUserId").as("sendUserId"), //查询字段
                group("sendUserId").count().as("count"), //分组字段
                //project().and("_id").as("sendUserId").and("count"), //查询字段
                sort(Sort.Direction.DESC , "sendUserId")
        );

        return mongoTemplate.aggregate(unReadNumAgg, "cp_chat_message", Map.class)
                .getMappedResults();

    }

    @Override
    public CpChatUserEntity save(CpChatUserEntity cpChatUser) {
        return cpChatUserRepository.save(cpChatUser);
    }
}
