package com.zs.create.modules.oa.budget.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.DictModel;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.item.service.ScItemBudgetService;
import com.zs.create.modules.item.service.ScItemService;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.oa.budget.entity.OaBudgetDeptEntity;
import com.zs.create.modules.oa.budget.entity.OaBudgetItemEntity;
import com.zs.create.modules.oa.budget.entity.OaBudgetProjectEntity;
import com.zs.create.modules.oa.budget.entity.dto.BudgetAuditDTO;
import com.zs.create.modules.oa.budget.entity.dto.OaBudgetDeptDTO;
import com.zs.create.modules.oa.budget.entity.dto.OaBudgetQueryDTO;
import com.zs.create.modules.oa.budget.entity.dto.OaBudgetTaskQueryDTO;
import com.zs.create.modules.oa.budget.entity.vo.OaBudgetTaskVo;
import com.zs.create.modules.oa.budget.enums.BudgetAuditStatusEnum;
import com.zs.create.modules.oa.budget.mapper.OaBudgetDeptMapper;
import com.zs.create.modules.oa.budget.service.OaBudgetDeptService;
import com.zs.create.modules.oa.budget.service.OaBudgetItemService;
import com.zs.create.modules.oa.budget.service.OaBudgetProjectService;
import com.zs.create.modules.oa.budget.utils.OaBudgetAuditUtils;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.system.service.ISysDictService;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import com.zs.create.modules.workflow.instance.service.impl.ProcessInstanceService;
import com.zs.create.modules.workflow.service.IWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ActivitiTaskAlreadyClaimedException;
import org.activiti.engine.IdentityService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 预算Service实现层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-18 10:10:04
 * @Version: V1.0
 */
@Service
@Slf4j
public class OaBudgetDeptServiceImpl extends ServiceImpl<OaBudgetDeptMapper, OaBudgetDeptEntity> implements OaBudgetDeptService {
    @Autowired
    OaBudgetDeptMapper oaBudgetDeptMapper;
    @Autowired
    OaBudgetProjectService oaBudgetProjectService;
    @Autowired
    OaBudgetItemService oaBudgetItemService;
    @Autowired
    IdentityService identityService;
    @Autowired
    RuntimeService runtimeService;
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    TaskService taskService;
    @Autowired
    @Lazy
    ScItemService scItemService;
    @Autowired
    ProcessInstanceService processInstanceService;
    @Autowired
    ISysDictService sysDictService;
    @Autowired
    ScItemBudgetService scItemBudgetService;
    @Autowired
    SysWeixinUserService sysWeixinUserService;
    @Autowired
    WxMessageSender wxMessageSender;
    @Autowired
    IWorkflowService workflowService;
    @Autowired
    private CampusAppService campusAppService;
    @Value("${gzhAppid}")
    String appid;
    @Override
    public Page<OaBudgetDeptEntity> budgetDeptPage(OaBudgetQueryDTO oaBudgetQueryDTO,
                                                   Integer pageNo, Integer pageSize) {
        Page<OaBudgetDeptEntity> page = new Page<>(pageNo, pageSize);
        List<OaBudgetDeptEntity> oaBudgetDeptList = oaBudgetDeptMapper.listBudget(page, oaBudgetQueryDTO);

        if(!CollectionUtils.isEmpty(oaBudgetDeptList)){
            for(OaBudgetDeptEntity oaBudgetDeptEntity : oaBudgetDeptList){
                BigDecimal chargeAmount = scItemBudgetService.getBudgetChargeAmount(oaBudgetDeptEntity.getDeptId()
                        , oaBudgetDeptEntity.getXn(), oaBudgetDeptEntity.getXq());

                oaBudgetDeptEntity.setChargeAmount(chargeAmount == null ? null
                        : chargeAmount.setScale(2,   BigDecimal.ROUND_HALF_UP));
            }
        }
        page.setRecords(oaBudgetDeptList);
        return page;

    }

    /**
     *
     * @param oaBudgetDeptDTO
     * @return
     */
    private BigDecimal caculateBudgetAmount(OaBudgetDeptDTO oaBudgetDeptDTO){
        Assert.notNull(oaBudgetDeptDTO , "计算总价方法 OaBudgetDeptDTO 对象为空");
        BigDecimal ammount = BigDecimal.ZERO;
      List<OaBudgetProjectEntity> budgetProjects = oaBudgetDeptDTO.getBudgetProjectList();
      if(!CollectionUtils.isEmpty(budgetProjects)){
          for (OaBudgetProjectEntity project : budgetProjects) {
                  if (!CollectionUtils.isEmpty(project.getBudgetItemList())) {
                      ammount = project.getBudgetItemList().stream().map(OaBudgetItemEntity::getSumPrize)
                              .reduce(ammount, BigDecimal::add);
                  }
              }
          }

      return ammount;
    }




    public String getPdKey(String businessDeptId ) {
        SysDepart depart = sysDepartService.getById(businessDeptId);
        if (null == depart) throw new ZsxcBootException("申请项目所属机构不存在");
        String processDefinitionKey = OaBudgetAuditUtils.getPdKey(depart.getOrgType());
        if (StringUtils.isEmpty(processDefinitionKey))
            throw new ZsxcBootException("机构类型不存在对应流程,联系管理员核对流程");
        return processDefinitionKey;
    }

    @Override
    @Transactional
    public Boolean saveBudget(OaBudgetDeptDTO oaBudgetDeptDTO) {
        Wrapper<OaBudgetDeptEntity> wrapper = new QueryWrapper<OaBudgetDeptEntity>()
                .eq("xn" , oaBudgetDeptDTO.getXn()).eq("xq" , oaBudgetDeptDTO.getXq())
                .eq("dept_id" , oaBudgetDeptDTO.getDeptId()).eq("del_flag" , 0);
        if(this.count(wrapper)>0) throw new ZsxcBootException("该部门本学期已存在预算数据");

        OaBudgetDeptEntity oaBudgetDeptEntity = new OaBudgetDeptEntity();
        BeanUtils.copyProperties(oaBudgetDeptDTO , oaBudgetDeptEntity);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if(sysUser == null) sysUser = new LoginUser();
        oaBudgetDeptEntity.setCreateUserName(sysUser.getRealname());
        oaBudgetDeptEntity.setBudgetAmount(this.caculateBudgetAmount(oaBudgetDeptDTO));

        //部门预算金额不能超出星级金额限制
        SysDepart sysDepart=sysDepartService.getById(oaBudgetDeptDTO.getDeptId());
        if (StringUtils.isBlank(sysDepart.getStar())) throw new ZsxcBootException("当前部门暂无星级配置");
        DictModel dictModel = sysDictService.queryDictByKey("star_funds", sysDepart.getStar());
        if (oaBudgetDeptEntity.getBudgetAmount().compareTo(new BigDecimal(dictModel.getText()))==1){
            throw new ZsxcBootException(dictModel.getDescription()+"社团预算金额上限为："+dictModel.getText());
        }

        Boolean budgetDeptRes = this.save(oaBudgetDeptEntity);
        if(!budgetDeptRes) throw new ZsxcBootException("保存失败");
        List<OaBudgetProjectEntity> budgetProjectList = oaBudgetDeptDTO.getBudgetProjectList();
        if(!CollectionUtils.isEmpty(budgetProjectList)){
            Long budgetId = oaBudgetDeptEntity.getId();
            budgetProjectList = budgetProjectList.stream().map(project->project.setBudgetId(budgetId))
                    .collect(Collectors.toList());
            oaBudgetProjectService.saveBatch(budgetProjectList);
            budgetProjectList.forEach(project->{
                List<OaBudgetItemEntity> budgetItemList = project.getBudgetItemList();
                if(!CollectionUtils.isEmpty(budgetItemList)){
                    Long projectId = project.getId();
                    budgetItemList.forEach(item->{
                        item.setProjectId(projectId);
                        item.setBudgetId(budgetId);
                    });
                    oaBudgetItemService.saveBatch(budgetItemList);
                }
            });
        }
        //创建流程变量
        Map<String, Object> variables = new HashMap<>(2);
        variables.put("businessDeptId", oaBudgetDeptDTO.getDeptId());
        //启动流程实例
        identityService.setAuthenticatedUserId(sysUser.getId());
        String pdKey = this.getPdKey(oaBudgetDeptDTO.getDeptId());
        ProcessInstance processInstance =
                runtimeService.startProcessInstanceByKey(pdKey, String.valueOf(oaBudgetDeptEntity.getId()), variables);

        // 修改申请状态 为申请中
        oaBudgetDeptEntity.setProcessInstanceId(processInstance.getId())
                .setApplyStatus(BudgetAuditStatusEnum.APPLY.getCode());
        this.updateById(oaBudgetDeptEntity);
        //自动审核第一个申请任务
        List<Task> tasks = taskService.createTaskQuery().active()
                .processInstanceId(processInstance.getId())
                .orderByTaskCreateTime().asc().list();//通过流程实例获取正在执行的任务
        if (!CollectionUtils.isEmpty(tasks)) {
            for (Task task : tasks) {
                BudgetAuditDTO budgetAuditDTO = new BudgetAuditDTO();
                budgetAuditDTO.setTaskId(task.getId()).setAuditNote("预算申请" );
                this.auditBudgetProcess( budgetAuditDTO,sysUser.getId() , null ,Boolean.TRUE );
            }
        }
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstance.getId()));
        return Boolean.TRUE;
    }



    /**
     * 根据task 获取业务id
     * @param task
     * @return
     */
    private String getTaskBusinessKey(Task task){
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId()).active().singleResult();
        return processInstance.getBusinessKey();
    }

    @Override
    @Transactional
    public String auditBudgetProcess(BudgetAuditDTO budgetAuditDTO,
                                   String assignee, Map<String, Object> variables , Boolean autoAuditFirst) {
        String taskId=budgetAuditDTO.getTaskId();
        String auditNote=budgetAuditDTO.getAuditNote();
        Task task = taskService.createTaskQuery().taskId(taskId).active().singleResult();
        if (null == task) throw new ZsxcBootException("当前任务不存在或已经被审核");
        String businessKey = getTaskBusinessKey(task);
        try {
            taskService.claim(taskId, assignee);
            taskService.addComment(taskId, task.getProcessInstanceId(), auditNote);
            //modify by hy
            taskService.setVariablesLocal(taskId , variables);
            taskService.complete(taskId, variables);
            //流程结束修改状态
            modifyStatus4ProcessEndAndSendWxMes(budgetAuditDTO,task.getProcessInstanceId() , assignee , businessKey , variables,auditNote);
            return task.getProcessInstanceId();
            // 结束
        } catch (ActivitiTaskAlreadyClaimedException e) {
            throw new ZsxcBootException("任务已被审核");
        }
    }


    public Boolean judgeProcessIsEnd(String processInstanceId){
        return  processInstanceService.judgeProcessIsEnd(processInstanceId);
    }

    public void modifyStatus4ProcessEndAndSendWxMes(BudgetAuditDTO budgetAuditDTO, String processInstanceId , String assignee , String businessKey , Map variables, String auditNote){
        OaBudgetDeptEntity one = this.getById(businessKey);
        Assert.notNull(one , "OaBudgetDeptEntity不存在");
        if(judgeProcessIsEnd(processInstanceId)){ //流程结束
            if(null != variables &&
                    BudgetAuditDTO.PROCESS_REJECT.equals(variables.get("status"))){ //驳回结束
                one.setApplyStatus(BudgetAuditStatusEnum.REJECT.getCode());
            } else  { //审核通过
                one.setApplyStatus(BudgetAuditStatusEnum.PUBLISH.getCode());
            }
            this.updateById(one);
            sendAuditResult2BudgetCreateUser(one , budgetAuditDTO.getStatus() , assignee , auditNote);
        }else{ //流程未结束
            processNotEndMes(processInstanceId , one);
        }
    }

    /**
     * 流程未结束消息提醒
     * @param processInstanceId
     * @param oaBudgetDeptEntity
     */
    public void processNotEndMes(String processInstanceId , OaBudgetDeptEntity oaBudgetDeptEntity ){
        Task currentTask = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .active().singleResult();
        List<String> taskCandidates = workflowService.getTaskUser(currentTask.getId());
        if(!CollectionUtils.isEmpty(taskCandidates)){
            for(String reciveUserId : taskCandidates){
                sendTodoTaskMes2candidateUser(oaBudgetDeptEntity ,reciveUserId);
            }
        }

    }

    /**
     * 给代办人发送待办任务消息
     * @param item
     * @param reciveUserId
     */
    public void sendTodoTaskMes2candidateUser(OaBudgetDeptEntity item , String reciveUserId){
        if(null != item){
            String title = "预算审核提醒";
            StringBuilder contentBuilder = new StringBuilder("有一条新的社团预算申请，等待您的审核，请及时处理");
            String remark = "点击可查看详情";

            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(reciveUserId, appid);
            if(null == wxMesUserInfo){
                log.error("项目预算给代办人发送待办任务消息，未找到接收人：{} 的信息", reciveUserId);
                return;
            }
            String openId = wxMesUserInfo.getOpenId();
            String theme = wxMesUserInfo.getRealname() + "/"+ wxMesUserInfo.getUsername()+",您有一条新的工作提醒";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setBizType("1000")
                    .setMiniAppUrl("pagesB/projectreview/projectreview")  //todo
                    .setUserId(reciveUserId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }


    /**
     * 给预算创建人发送审核结果消息
     * @param item
     * @param auditResult
     * @param assignee
     */
    public  void sendAuditResult2BudgetCreateUser(OaBudgetDeptEntity item , Integer auditResult , String assignee , String auditNote){
        if(null != item){
            String createUserId = item.getCreateBy();
            String title = "预算审核提醒";
            StringBuilder contentBuilder = new StringBuilder();
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(createUserId, appid);
            if(null == wxMesUserInfo){
                log.error("给预算创建人发送审核结果消息，未找到接收人：{} 的信息", createUserId);
                return;
            }
            String theme = wxMesUserInfo.getRealname() + "/"+ wxMesUserInfo.getUsername()+",您有一条新的工作提醒";
            String openId = wxMesUserInfo.getOpenId();
            if(BudgetAuditDTO.PROCESS_PASS.equals(auditResult)){
                contentBuilder.append("您申请的社团预算申请,已经审核通过");

            }else{
                contentBuilder.append("您申请的社团预算申请,已经被驳回，驳回理由:" + auditNote);
            }

            String remark = "点击可查看详情";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(item.getCreateBy())
                    .setCreateDate(new Date())
                    .setBizType("1000")
                    .setMiniAppUrl("pagesC/budgets/budgetsAudit")
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);

        }

    }


    @Override
    public OaBudgetDeptEntity getBudgetById(Long id) {
        OaBudgetDeptEntity one = this.getById(id);
        if(one != null){
            SysDepart dept = sysDepartService.getById(one.getDeptId());
            one.setDepartName(dept==null?"" : dept.getDepartName());
            one.setBudgetProjectList(Collections.emptyList());//初始化空数组
            QueryWrapper<OaBudgetProjectEntity> projectWrapper = new QueryWrapper<>();
            projectWrapper.eq("budget_id" , one.getId()).eq("del_flag" ,  0);
            List<OaBudgetProjectEntity> projectList = oaBudgetProjectService.list(projectWrapper);
            if(!CollectionUtils.isEmpty(projectList)){
                //处理project form 和module
                projectList.forEach(project->{
                    if(project.getForm()!=null)
                        project.setForm_text( sysDictService.queryDictTextByKey("item_form", project.getForm()));
                    if(project.getModule()!=null)
                        project.setModule_text( sysDictService.queryDictTextByKey("item_module", project.getModule()));
                });
                Set<Long> projectIds = projectList.parallelStream()
                        .map(OaBudgetProjectEntity::getId).collect(Collectors.toSet());
                if(!CollectionUtils.isEmpty(projectIds)){
                    QueryWrapper<OaBudgetItemEntity> itemWrapper = new QueryWrapper<>();
                    itemWrapper.in("project_id" , projectIds);
                    List<OaBudgetItemEntity> itemList = oaBudgetItemService.list(itemWrapper);
                    if(!CollectionUtils.isEmpty(itemList)){
                        // 按照projectId 分组 明细
                        Map<Long, List<OaBudgetItemEntity>> itemGroupByProjectId =
                                itemList.parallelStream().collect(Collectors.groupingBy(d -> d.getProjectId()));
                        projectList.forEach(project-> {
                            //初始化itemList
                            project.setBudgetItemList(Collections.EMPTY_LIST);
                            itemGroupByProjectId.forEach((projectId , items) ->{
                                if(project.getId().equals(projectId)) project.setBudgetItemList(items);
                            });
                        });

                    }
                }
                one.setBudgetProjectList(projectList);
            }
        }
        return one;
    }

    @Override
    @Transactional
    public Boolean delById(Long id) {
        OaBudgetDeptEntity one = this.getById(id);
        if(one == null) throw new ZsxcBootException("id参数不正确");
        if(one.getApplyStatus()==null || !one.getApplyStatus().equals(BudgetAuditStatusEnum.REJECT.getCode())){
            throw new ZsxcBootException("非申请驳回状态不可删除");
        }
            QueryWrapper projectWrapper = new QueryWrapper();
            projectWrapper.select("id").eq("budget_id" ,one.getId());
            List<OaBudgetProjectEntity> projects = oaBudgetProjectService.list(projectWrapper);
            Set<Long> projectIdSet = projects.parallelStream()
                    .map(OaBudgetProjectEntity::getId).collect(Collectors.toSet());
            if(!CollectionUtils.isEmpty(projectIdSet)){
                QueryWrapper itemWrapper = new QueryWrapper();
                itemWrapper.in("project_id" , projectIdSet);
                oaBudgetItemService.remove(itemWrapper);
                oaBudgetProjectService.removeByIds(projectIdSet);
            }

        return this.removeById(one.getId());
    }

    @Override
    public Page<OaBudgetTaskVo> taskPage(OaBudgetTaskQueryDTO taskQueryDTO, Integer pageNo, Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if(null == sysUser) sysUser = new LoginUser();
        taskQueryDTO.setAssignee(sysUser.getId());
        Page<OaBudgetTaskVo> taskVoPage = null;
        if(OaBudgetTaskQueryDTO.QUERY_HISTORY.equals(taskQueryDTO.getQueryType())){
            taskVoPage = this.historyTaskPage(taskQueryDTO, pageNo, pageSize);
        }
        if(OaBudgetTaskQueryDTO.QUERY_TODO.equals(taskQueryDTO.getQueryType())){
            taskVoPage = this.todoTaskPage(taskQueryDTO , pageNo ,pageSize);;
        }
        return taskVoPage;
    }

    /**
     * 待办任务
     * @param taskQueryDTO
     * @param pageNo
     * @param pageSize
     * @return
     */
    private Page<OaBudgetTaskVo> todoTaskPage(OaBudgetTaskQueryDTO taskQueryDTO, Integer pageNo, Integer pageSize) {
        Page<OaBudgetTaskVo> page = new Page<>(pageNo , pageSize);
        page.setRecords(oaBudgetDeptMapper.todoTaskPage(page,taskQueryDTO));
        return page;
    }

    /**
     * 历史任务
     * @param taskQueryDTO
     * @param pageNo
     * @param pageSize
     * @return
     */
    private Page<OaBudgetTaskVo> historyTaskPage(OaBudgetTaskQueryDTO taskQueryDTO, Integer pageNo, Integer pageSize) {
        Page<OaBudgetTaskVo> page = new Page<>(pageNo , pageSize);
        page.setRecords(oaBudgetDeptMapper.historyTaskPage(page,taskQueryDTO));
        return page;
    }
}
