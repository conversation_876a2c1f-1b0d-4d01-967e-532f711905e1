<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.prize.mapper.OaPrizeMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.prize.entity.OaPrizeEntity" id="oaPrizeMap">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="content" column="content"/>
        <result property="module" column="module"/>
        <result property="departId" column="depart_id"/>
        <result property="nj" column="nj"/>
        <result property="st" column="st"/>
        <result property="et" column="et"/>
        <result property="materialName" column="material_name"/>
        <result property="materialUrl" column="material_url"/>
        <result property="auditId" column="audit_id"/>
        <result property="auditName" column="audit_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="isAffirm" column="is_affirm"/>
    </resultMap>

    <!--<select id="myInPublicPrize" resultMap="oaPrizeMap">
         SELECT * FROM oa_prize
         where id in(SELECT prize_id FROM oa_declare_user where user_id=#{userId}
         AND NOW()>= publicity_st and NOW()&lt;= publicity_et)
    </select>

    <select id="myNotInPublicPrize" resultMap="oaPrizeMap">
         SELECT * FROM oa_prize a
         where a.id in (SELECT prize_id FROM oa_declare_user where user_id !=#{userId}
         AND NOW()>= publicity_st and NOW()&lt;= publicity_et)
    </select>-->

    <select id="myInPublicPrize" resultMap="oaPrizeMap">
        SELECT oap.* ,oad.publicity_et as publicity_et
        FROM oa_prize oap
        LEFT JOIN oa_declare_user oad ON oap.id=oad.prize_id
        where NOW()>= oad.publicity_st
          and NOW()&lt;= oad.publicity_et
          AND oad.user_id= #{userId}
        group by oap.id
    </select>

    <select id="myNotInPublicPrize" resultMap="oaPrizeMap">
        SELECT oap.* ,oad.publicity_et as publicity_et
        FROM oa_prize oap
        LEFT JOIN oa_declare_user oad ON oap.id=oad.prize_id
        where NOW()>= oad.publicity_st
          and NOW()&lt;= oad.publicity_et
          AND oap.id not in (
            SELECT oap.id
            FROM oa_prize oap
                     LEFT JOIN oa_declare_user oad ON oap.id=oad.prize_id
            where NOW()>= oad.publicity_st
              and NOW()&lt;= oad.publicity_et
              AND oad.user_id= #{userId}
            )
        group by oap.id
    </select>

    <select id="qryByEndTime" resultType="com.zs.create.modules.oa.prize.entity.OaPrizeEntity">
        select *
        from oa_prize
        where
            type = '1'
        and et &gt;= #{todayStart}
        and et &lt;= #{todayEnd}
    </select>
</mapper>