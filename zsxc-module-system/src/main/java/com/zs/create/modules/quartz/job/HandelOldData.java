package com.zs.create.modules.quartz.job;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.entity.ScItemHoursEntity;
import com.zs.create.modules.item.entity.ScItemSignEntity;
import com.zs.create.modules.item.entity.ScItemWorksEntity;
import com.zs.create.modules.item.service.ScItemHoursService;
import com.zs.create.modules.item.service.ScItemService;
import com.zs.create.modules.item.service.ScItemSignService;
import com.zs.create.modules.item.service.ScItemWorksService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @description:
 * @author: ansheng<PERSON>
 * @create: 2021-05-22 14:46
 **/
public class HandelOldData implements Job {

    @Autowired
    private ScItemService scItemService;

    @Autowired
    private ScItemHoursService scItemHoursService;

    @Autowired
    private ScItemSignService scItemSignService;

    @Autowired
    private ScItemWorksService scItemWorksService;

    @Override
    public void execute(JobExecutionContext jobExecutionContext) throws JobExecutionException {
        List<ScItemEntity> list = scItemService.list();
        for (int i = 0; i < list.size(); i++) {
            ScItemEntity scItemEntity = list.get(i);
            List<ScItemHoursEntity> scItemHoursEntities = scItemHoursService.list(new QueryWrapper<ScItemHoursEntity>().eq("del_flag", 0).eq("item_id", scItemEntity.getId()).eq("status", 10));
            BigDecimal bigDecimal = new BigDecimal(0);
            for (ScItemHoursEntity scItemHoursEntity : scItemHoursEntities) {
                bigDecimal = bigDecimal.add(scItemHoursEntity.getHours());
            }
//            int count = scItemSignService.count(new QueryWrapper<ScItemSignEntity>().eq("item_id", scItemEntity.getId()).eq("type", 1));
            int works = scItemWorksService.count(new QueryWrapper<ScItemWorksEntity>().eq("item_id", scItemEntity.getId()));
            int sings = 0;
            List<ScItemSignEntity> daos = scItemSignService.list(new QueryWrapper<ScItemSignEntity>().eq("item_id", scItemEntity.getId()).eq("type", 0));
            List<ScItemSignEntity> tuis = scItemSignService.list(new QueryWrapper<ScItemSignEntity>().eq("item_id", scItemEntity.getId()).eq("type", 1));
            List<String> tuiUserCodes = new ArrayList<>();
            for (ScItemSignEntity scItemSignEntity : tuis) {
                tuiUserCodes.add(scItemSignEntity.getUserCode());
            }
            Set<String> set = new HashSet<>();
            for (ScItemSignEntity dao : daos) {
                set.add(dao.getUserCode());
            }
            for (String s : set) {
                for (String tuiUserCode : tuiUserCodes) {
                    if (tuiUserCode.equals(s)) {
                        sings++;
                        break;
                    }
                }
            }
            scItemService.updateHoursAndPersons(scItemEntity.getId(), bigDecimal, works + sings);
            System.out.println("处理至第*****" + i);
        }
    }
}
