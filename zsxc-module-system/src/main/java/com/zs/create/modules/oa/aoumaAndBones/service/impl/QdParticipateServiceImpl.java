package com.zs.create.modules.oa.aoumaAndBones.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.base.enums.QdStatusEnum;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.DictModel;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.item.entity.ItemAuditDTO;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.oa.aoumaAndBones.dto.UserjoinReqBO;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdClassEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdParticipateEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdPublicEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdResumeEntity;
import com.zs.create.modules.oa.aoumaAndBones.mapper.QdClassMapper;
import com.zs.create.modules.oa.aoumaAndBones.mapper.QdParticipateMapper;
import com.zs.create.modules.oa.aoumaAndBones.service.*;
import com.zs.create.modules.oa.aoumaAndBones.util.participateImportHandler;
import com.zs.create.modules.paramdesign.entity.ScWeeksEntity;
import com.zs.create.modules.paramdesign.mapper.ScWeeksMapper;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.entity.SysUserDepart;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import com.zs.create.modules.system.mapper.SysUserDepartMapper;
import com.zs.create.modules.system.service.ISysUserService;
import com.zs.create.modules.system.util.DictUtils;
import com.zs.create.modules.system.util.ExcelStyleUtil;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.ProcessEngine;
import org.activiti.engine.ProcessEngines;
import org.activiti.engine.runtime.ProcessInstance;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.util.Assert;
import org.apache.shiro.util.CollectionUtils;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @Description 青马大骨班Service实现层
 * @email
 * @date 2022-11-03 10:19:07
 * @Version: V1.0
 */
@Slf4j
@Service
public class QdParticipateServiceImpl extends ServiceImpl<QdParticipateMapper, QdParticipateEntity> implements QdParticipateService {

    @Resource
    private QdParticipateMapper qdParticipateMapper;

    @Resource
    private QdClassMapper qdClassMapper;

    @Resource
    private QdResumeService qdResumeService;

    @Autowired
    private ISysUserService sysUserService;

    @Resource
    private SysUserDepartMapper sysUserDepartMapper;

    @Resource
    private QdRegistrationReviewService qdRegistrationReviewService;

    @Resource
    private SysDepartMapper sysDepartMapper;

    @Resource
    private ScWeeksMapper scWeeksMapper;

    @Resource
    private QdClassService qdClassService;

    @Autowired
    private SysWeixinUserService sysWeixinUserService;
    @Autowired
    private CampusAppService campusAppService;
    @Autowired
    @Lazy
    private WxMessageSender wxMessageSender;

    @Value("${gzhAppid}")
    private String appid;

    @Lazy
    @Autowired
    private QdPublicService qdPublicService;

    private static final Integer GRADUATE = 5;

    private static final String ROLE_XTWSJ = "role_xtwsj";

    /**
     * 个人报名列表查看
     *
     * @param qdParticipate
     * @return
     */
    @Override
    public IPage<UserjoinReqBO> qryUserjoin(QdParticipateEntity qdParticipate, Integer pageNo, Integer pageSize) {
        log.info("个人报名列表查看,入参{}", qdParticipate);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        //自行构造查询参数
        Page<UserjoinReqBO> page = new Page<UserjoinReqBO>(pageNo, pageSize);
        //IPage<UserjoinReqBO> resUserjoin = new Page<>();
        //查询登陆人所属的部门id列表
        QueryWrapper<SysUserDepart> departQueryWrapper = new QueryWrapper<>();
        departQueryWrapper.eq("user_id", sysUser.getId());
        List<String> userDepartList = sysUserDepartMapper.selectList(departQueryWrapper).stream().map(SysUserDepart::getDepId).collect(Collectors.toList());
        //查询登陆人用户信息
        SysUser userInfo = sysUserService.getUserById(sysUser.getId());

        //获取当前学期学年
        ScWeeksEntity scWeeksEntity = scWeeksMapper.selectCurrentWeek();
        if (Objects.isNull(scWeeksEntity)) {
            return page;
        }

        //当前用户所在年级  2012级学生就是2012年进校  大一
        String grade = userInfo.getGrade();
        SimpleDateFormat format = new SimpleDateFormat("yyyy");
        Integer year = Integer.valueOf(format.format(new Date()));
        int currentNj = 0;
        if ("G".equals(userInfo.getType())){
            currentNj = GRADUATE;
        }else if ("S".equals(userInfo.getType())){
            if (StringUtils.isNotEmpty(grade)) {
                if ("1".equals(scWeeksEntity.getXq())) {
                    //上学期年级计算
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("MM");
                    int month = Integer.parseInt(simpleDateFormat.format(new Date()));
                    if (month > 6) {
                        currentNj = year - Integer.parseInt(grade) + 1;
                    } else {
                        currentNj = year - Integer.parseInt(grade);
                    }
                } else {
                    //下学期年级计算
                    currentNj = year - Integer.parseInt(grade);
                }
            }
        }


        //按条件查询班级
        List<UserjoinReqBO> userjoinReqBOS = qdClassMapper.qryUserjoin(qdParticipate);
        if (org.springframework.util.CollectionUtils.isEmpty(userjoinReqBOS)){
            return page;
        }

        //查询登陆人所有的报名信息
        QueryWrapper<QdParticipateEntity> participateQueryWrapper = new QueryWrapper<>();
        participateQueryWrapper.eq("user_id", sysUser.getId());
        Map<String, QdParticipateEntity> qdParticipateEntityMap = qdParticipateMapper.selectList(participateQueryWrapper).stream().collect(Collectors.toMap(QdParticipateEntity::getClassId, t -> t,(key1,key2)->key1));

        //用于stream流中，不可变
        final int finalCurrentNj = currentNj;
        List<String> classIds = new ArrayList<>();
        userjoinReqBOS.forEach(item -> {
            boolean identify = true;
            //判断班级是否选择报名部门范围或者是否选择报名年级范围
            if(!StringUtils.isEmpty(item.getDeptId())){
                List<String> deptIds = Arrays.stream(item.getDeptId().split(",")).collect(Collectors.toList());
                //如果size>0 则登陆人所属部门在班级报名范围内
                int size = (int) deptIds.stream().map(deptId ->
                        userDepartList.stream().filter(uDeptId
                                -> Objects.nonNull(deptId) && Objects.nonNull(uDeptId) && Objects.equals(deptId, uDeptId)).findAny().orElse(null))
                        .filter(Objects::nonNull).count();
                //存在部门报名范围，登陆人不在范围内，不展示
                if(size<=0){
                    identify = false;
                }
            }
            if (!StringUtils.isEmpty(item.getNj())){
                //存在年级报名范围，登陆人不在范围内，不展示
                if (!item.getNj().contains(String.valueOf(finalCurrentNj))){
                    identify = false;
                }
            }
            QdParticipateEntity qdParticipateEntity = qdParticipateEntityMap.get(item.getClassId());

            if (identify || (Objects.nonNull(qdParticipateEntity) && qdParticipateEntity.getApplyType().equals(QdParticipateEntity.APPLY_STATUS))){
                classIds.add(item.getClassId());
            }

        });
        if (CollectionUtils.isEmpty(classIds)){
            //没有可报名范围内的班级
            return page;
        }

        //根据登陆人可报名范围查询展示班级信息
        IPage<UserjoinReqBO> resUserjoin = qdClassMapper.qryUserjoinByPage(page,classIds);
        //补充数据
        resUserjoin.getRecords().stream().forEach(item ->{
            QdParticipateEntity qdParticipateEntity = qdParticipateEntityMap.get(item.getClassId());
            if (!Objects.isNull(qdParticipateEntity)) {
                item.setApplyId(qdParticipateEntity.getId());
                item.setApplyStatus(qdParticipateEntity.getApplyStatus());
                item.setApplyStatusName(QdStatusEnum.codeOf(qdParticipateEntity.getApplyStatus()).getDesc());
                item.setProcessInstanceId(qdParticipateEntity.getProcessInstanceId());
            } else {
                item.setApplyStatus(QdStatusEnum.wbm_status.getCode());
                item.setApplyStatusName(QdStatusEnum.wbm_status.getDesc());
            }
        });
        log.info("个人报名列表查看,出参[{}]", resUserjoin.getRecords());
        return resUserjoin;
    }


    public OutputStream exportUserExcel(HttpServletResponse response) {
        Result<String> result = new Result<>();
        List<ExcelExportEntity> entity = new ArrayList();
        entity.add(new ExcelExportEntity("姓名", "realname"));
        entity.add(new ExcelExportEntity("性别", "sex"));
        entity.add(new ExcelExportEntity("学号", "username"));
        entity.add(new ExcelExportEntity("联系方式", "mobile"));
        entity.add(new ExcelExportEntity("QQ", "QQ"));
        entity.add(new ExcelExportEntity("政治面貌", "politic"));
        entity.add(new ExcelExportEntity("入党申请", "application_time"));
        entity.add(new ExcelExportEntity("现任职务", "post"));
        entity.add(new ExcelExportEntity("获奖情况", "awards"));
        entity.add(new ExcelExportEntity("团校结业", "twFlash"));
        entity.add(new ExcelExportEntity("班级选择", "classChoice"));
        Collection dataList = new ArrayList();

        Map<String, Object> map = new LinkedHashMap<>();
        map.put("realname", null);
        map.put("sex", null);
        map.put("username", null);
        map.put("mobile", null);
        map.put("QQ", null);
        map.put("politic", null);
        map.put("application_time", null);
        map.put("post", null);
        map.put("awards", null);
        map.put("twFlash", null);
        map.put("classChoice", null);
        dataList.add(map);
        response.setContentType("application/OCTET-STREAM;charset=UTF-8");

        String filename = new String(("报名人员导入" + System.currentTimeMillis() + ".xls").getBytes(), StandardCharsets.ISO_8859_1);
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            ExportParams ep = new ExportParams();
            ep.setStyle(ExcelStyleUtil.class);

            Workbook workbook = ExcelExportUtil.exportExcel(ep, entity, dataList);

            /**设置单元格格式为文本格式*/
            CellStyle cellStyle = workbook.createCellStyle();
            DataFormat dataFormat = workbook.createDataFormat();
            cellStyle.setDataFormat(dataFormat.getFormat("@"));
            Sheet sheet = workbook.getSheetAt(0);
            //sheet.setDefaultColumnStyle(0, cellStyle);//设置首列格式为"文本"
            for (int i = 0; i < map.size(); i++) {
                sheet.setDefaultColumnStyle(i, cellStyle);
            }
            for (ExcelExportEntity excelExportEntity : entity) {
                for (int i = 0; i < excelExportEntity.getName().length(); i++) {
                    sheet.autoSizeColumn(i);
                    sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 17 / 10);
                }
            }
            workbook.write(out);
            return out;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != out)
                    out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return out;
    }


    /**
     * 我要报名
     * @param qdParticipate
     * @return
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Result<QdParticipateEntity> addOne(QdParticipateEntity qdParticipate) {
        Result<QdParticipateEntity> result = new Result<QdParticipateEntity>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        //设置创建人
        qdParticipate.setCreateBy(sysUser.getId());
        qdParticipate.setCreateTime(new Date());
        qdParticipate.setUpdateBy(sysUser.getId());
        qdParticipate.setUpdateTime(new Date());

        //暂存
        if (QdStatusEnum.zc_status.getCode().equals(qdParticipate.getApplyStatus())){
            //填写了个人简介，存到简介表
            if (!StringUtils.isEmpty(qdParticipate.getResumeInfo())){
                QdResumeEntity qdResumeEntity = new QdResumeEntity();
                qdResumeEntity.setContent(qdParticipate.getResumeInfo());
                qdResumeEntity.setCreateBy(sysUser.getId());
                qdResumeEntity.setCreateTime(new Date());
                boolean save = qdResumeService.save(qdResumeEntity);
                if (!save){throw new ZsxcBootException("个人简介数据不符合规范！");}
                qdParticipate.setResumeId(qdResumeEntity.getId());
            }
            this.save(qdParticipate);
            result.success("暂存成功！");
            return result;
        }

        //判断是否在报名时间内
        QdClassEntity qdClassEntity = qdClassService.getById(qdParticipate.getClassId());
        long nowDate = System.currentTimeMillis();
        if (qdClassEntity.getApplyStarttime().getTime() > nowDate) {
            throw new ZsxcBootException("报名还未开始，不允许报名！");

        }else if (qdClassEntity.getApplyEndtime().getTime() < nowDate) {
            throw new ZsxcBootException("报名已经结束，不允许继续报名！");
        }

        long time = 0;
        if (!StringUtils.isEmpty(qdParticipate.getApplicationTime())){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
            try {
                Date date = simpleDateFormat.parse(qdParticipate.getApplicationTime());
                time = date.getTime();
            }catch (Exception e){
                log.error("时间转换错误",e);
            }
            if (time > nowDate) {
                throw new ZsxcBootException("入党申请时间不能大于当前时间！");
            }
        }

        //根据用户id和班级id查询报名记录
        QdParticipateEntity qdParticipateEntity =  baseMapper.qryRegistrationRecord(qdParticipate);
        if (!Objects.isNull(qdParticipateEntity)) {throw new ZsxcBootException("已在该班级报过名，请勿重复报名。");}

        if (StringUtils.isEmpty(qdParticipate.getApplicationTime())){
            qdParticipate.setApplicationTime("否");
        }

        //填写了个人简介，存到简介表
        if (!StringUtils.isEmpty(qdParticipate.getResumeInfo())){
            QdResumeEntity qdResumeEntity = new QdResumeEntity();
            qdResumeEntity.setContent(qdParticipate.getResumeInfo());
            qdResumeEntity.setCreateBy(sysUser.getId());
            qdResumeEntity.setCreateTime(new Date());
            boolean save = qdResumeService.save(qdResumeEntity);
            if (!save){throw new ZsxcBootException("个人简介数据不符合规范！");}
            qdParticipate.setResumeId(qdResumeEntity.getId());
        }

        //新增一条报名信息
        qdParticipate.setApplyType(0);
        qdParticipate.setApplyStatus(0);
        this.save(qdParticipate);
        log.info("===>>>报名信息为qdParticipate：{}",qdParticipate);
        //启动流程实例
        if (oConvertUtils.isEmpty(qdParticipate.getTzb())){
            throw new ZsxcBootException("请选择团支部");
        }
        ProcessInstance processInstance = qdRegistrationReviewService.startApplyProcess(qdParticipate, sysUser.getId());

        qdParticipate.setProcessInstanceId(processInstance.getId());
        boolean ok = this.updateById(qdParticipate);
        if (!ok){ throw new ZsxcBootException("流程实例id关联失败"); }
        result.success("提交成功！");
        return result;
    }

    /**
     * 根据用户id和班级id查看报名信息
     * @param qdParticipate
     * @return
     */
    @Override
    public QdParticipateEntity qryByUserId(QdParticipateEntity qdParticipate) {
        return baseMapper.qryByUserId(qdParticipate);
    }

    @Override
    public Result<?> importUserExcel(InputStream in, String classId) {
        Result<?> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);

        QdClassEntity classEntity = qdClassService.getById(classId);
        if(oConvertUtils.isEmpty(classEntity)){
            return result.error500("班级不存在。");
        }

        Date date=new Date();
        Date applyStarttime = classEntity.getApplyStarttime();
        boolean before = date.before(applyStarttime);
        if (before){
            return result.error500("报名未开始，无法导入报名人员");
        }

        int count = count(
                Wrappers.<QdParticipateEntity>lambdaQuery()
                        .eq(QdParticipateEntity::getApplyStatus, QdStatusEnum.ybm_status.getCode())
                        .eq(QdParticipateEntity::getClassId,classId));
        if (count>0){
            throw new ZsxcBootException("报名已确认，无法导入报名人员");
        }

        QdPublicEntity qdPublicEntity = qdPublicService.getOne(
                Wrappers.<QdPublicEntity>lambdaQuery()
                        .eq(QdPublicEntity::getClassId, classId)
                        .eq(QdPublicEntity::getType,0));

        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("username", "学号");
        fieldMap.put("realname", "姓名");
        fieldMap.put("sex", "性别");
        fieldMap.put("mobile", "联系方式");
        fieldMap.put("QQ", "QQ");
        fieldMap.put("politic", "政治面貌");
        fieldMap.put("applicationTime", "入党申请");
        fieldMap.put("post", "现任职务");
        fieldMap.put("awards", "获奖情况");
        fieldMap.put("twFlash", "团校结业");
        fieldMap.put("classChoice", "班级选择");

        ImportParams params = new ImportParams();
        params.setTitleRows(0); //表格标题行数
        params.setHeadRows(1); // 表头行数
        params.setDataHanlder(new participateImportHandler(fieldMap));
        int successCnt = 0;
        //错误数据记录集合
        List<String> errors = new LinkedList<>();
        List<Map<String, Object>> list = null;
        try {
            list = ExcelImportUtil.importExcel(in, Map.class, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ZsxcBootException("文件的格式不正确,请使用所下载的模板上传数据！！");
        }
        if (CollectionUtils.isEmpty(list)) throw new ZsxcBootException("未匹配到任何数据...");
        String massge = "";
        int nullContent = 0;
        List<DictModel> politicFace = DictUtils.getKeyAndValueByCode("politic_face", "1");
        Map<String, String> politicFaceMap = politicFace.stream().collect(Collectors.toMap(DictModel::getText, DictModel::getValue));

        List<DictModel> classChoiceList = DictUtils.getKeyAndValueByCode("qd_class_choice", "1");
        Map<String, DictModel> classChoiceMap = classChoiceList.stream().collect(Collectors.toMap(DictModel::getText, t -> t, (key1, key2) -> key1));

        List<QdParticipateEntity> participateEntities = new ArrayList<>();
        //同一次导入同一个人是否存在两条数据
        List<String> useridExist=new ArrayList<>();
        //批量设置导入的流程历史为null
        List<String> setProcessInstanceIdIsNull =new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            try {
                Map<String, Object> scoreMap = list.get(i);

                String username = (String) scoreMap.get("username");//学号
                String realname = (String) scoreMap.get("realname");//姓名
                String sex = (String) scoreMap.get("sex");//性别
                String mobile = (String) scoreMap.get("mobile");//联系方式
                String qq = (String) scoreMap.get("QQ");//qq
                String politic = (String) scoreMap.get("politic");//政治面貌
                String applicationTime = (String) scoreMap.get("applicationTime");//入党申请时间
                String post = (String) scoreMap.get("post");//现任职务
                String awards = (String) scoreMap.get("awards");//获奖情况
                String twFlash = (String) scoreMap.get("twFlash");//团校结业
                String classChoice = (String) scoreMap.get("classChoice");//班级选择

                if (useridExist.contains(username)){
                    massge = ("第" + (i + 2) + "行数据，重复数据。");
                    errors.add(massge);
                    continue;
                }

                //整条数据为空，跳过该条数据
                if ((username == null || (username.trim().length() == 0))
                        &&(realname == null || (realname.trim().length() == 0))
                        &&(sex == null || (sex.trim().length() == 0))
                        &&(mobile == null || (mobile.trim().length() == 0))
                        &&(qq == null || (qq.trim().length() == 0))
                        &&(politic == null || (politic.trim().length() == 0))
                        &&(applicationTime == null || (applicationTime.trim().length() == 0))
                        &&(post == null || (post.trim().length() == 0))
                        &&(awards == null || (awards.trim().length() == 0))
                        &&(twFlash == null || (twFlash.trim().length() == 0))
                        &&(classChoice == null || (classChoice.trim().length() == 0))) {
                    nullContent++;
                    continue;
                }

                if ((username == null || (username.trim().length() == 0))
                        ||(realname == null || (realname.trim().length() == 0))
                        ||(sex == null || (sex.trim().length() == 0))
                        ||(mobile == null || (mobile.trim().length() == 0))
                        ||(qq == null || (qq.trim().length() == 0))
                        ||(politic == null || (politic.trim().length() == 0))
                        ||(applicationTime == null || (applicationTime.trim().length() == 0))
                        ||(post == null || (post.trim().length() == 0))
                        ||(awards == null || (awards.trim().length() == 0))
                        ||(twFlash == null || (twFlash.trim().length() == 0))
                        ||(classChoice == null || (classChoice.trim().length() == 0))) {
                    massge = ("第" + (i + 2) + "行数据，请将表格内容补充完整，所有列均需要填写。");
                    errors.add(massge);
                    continue;
                }

                if ((!sex.equals("男"))&&(!sex.equals("女"))){
                    massge = ("第" + (i + 2) + "行数据，性别填写有误。");
                    errors.add(massge);
                    continue;
                }
                if (!isMobile(mobile)){
                    massge = ("第" + (i + 2) + "行数据，联系方式填写有误。");
                    errors.add(massge);
                    continue;
                }
                if (!isQQ(qq)){
                    massge = ("第" + (i + 2) + "行数据，QQ填写有误。");
                    errors.add(massge);
                    continue;
                }
                String isnull = politicFaceMap.get(politic);
                if (isnull==null){
                    massge = ("第" + (i + 2) + "行数据，政治面貌填写有误。");
                    errors.add(massge);
                    continue;
                }
                DictModel model = classChoiceMap.get(classChoice);
                if (model==null){
                    massge = ("第" + (i + 2) + "行数据，班级选择填写有误。");
                    errors.add(massge);
                    continue;
                }
                String checkDate ="";
                if (!applicationTime.equals("否")) {
                    checkDate = checkDate(applicationTime);
                    if (checkDate.equals("false")) {
                        massge = ("第" + (i + 2) + "行数据，请填写正确的入党年月!");
                        errors.add(massge);
                        continue;
                    }
                }
                //出现“否”的情况
                if (oConvertUtils.isEmpty(checkDate)){
                    checkDate=applicationTime;
                }

                SysUser user = sysUserService.getUserByName(username);
                if (null == user || !realname.equals(user.getRealname())) {
                    massge = ("第" + (i + 2) + "行数据，姓名与学号不匹配。");
                    errors.add(massge);
                } else {

                    QdParticipateEntity participateEntity = new QdParticipateEntity();
                    participateEntity.setUserId(username)
                            .setUserName(realname)
                            .setSex(sex)
                            .setClassId(classId)
                            .setMobile(mobile)
                            .setQq(qq)
                            .setPolitic(politic)
                            .setApplicationTime(checkDate)
                            .setPost(post)
                            .setAwards(awards)
                            .setTwFlash(twFlash)
                            .setApplyType(QdParticipateEntity.APPLY_STATUS)
                            .setClassName(classEntity.getClassName())
                            .setClassChoice(classChoice);

                    if (oConvertUtils.isEmpty(qdPublicEntity)){
                        participateEntity.setApplyStatus(QdStatusEnum.ysh_status.getCode());
                    }else {
                        if (QdPublicEntity.GSZ_STATUS.equals(qdPublicEntity.getPublicStatus())){
                            participateEntity.setApplyStatus(QdStatusEnum.gsz_status.getCode());
                        }else if (QdPublicEntity.GSYJS_STATUS.equals(qdPublicEntity.getPublicStatus())){
                            participateEntity.setApplyStatus(QdStatusEnum.gsyjs_status.getCode());
                        }
                    }

                    LambdaQueryWrapper<QdParticipateEntity> queryWrapper = new LambdaQueryWrapper<>();
                    queryWrapper.eq(QdParticipateEntity::getUserId, username).eq(QdParticipateEntity::getClassId, classId);
                    QdParticipateEntity one = this.getOne(queryWrapper);
                    if (one != null) {
                        participateEntity.setId(one.getId());
                        //结束流程
                        String processInstanceId = one.getProcessInstanceId();
                        if(!StringUtils.isEmpty(processInstanceId)){
                            ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
                            if (!(QdStatusEnum.btjbm_status.getCode().equals(one.getApplyStatus())
                                    || QdStatusEnum.xghztj_status.getCode().equals(one.getApplyStatus()))) {
                                processEngine.getRuntimeService().deleteProcessInstance(processInstanceId, "删除当前流程");
                                // 推送消息
                                campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
                            }
                            //删除流程，流程id变为null，防止在已审核查出
                            setProcessInstanceIdIsNull.add(one.getId());
                        }
                    }
                    //添加成功后，把当前学生的学号保存进去，用于去除
                    useridExist.add(username);
                    participateEntities.add(participateEntity);
                    successCnt++;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (participateEntities.size() > 0) {
            boolean b = saveOrUpdateBatch(participateEntities);
            if (!b) throw new ZsxcBootException("导入失败");
        }
        //qdRegistrationReviewService.insert3Table(classEntity.getId(),participateEntities,sysUser.getId());
        //把流程id变为null
        if (setProcessInstanceIdIsNull.size()>0){
            qdParticipateMapper.setProcessInstanceIdIsNull(setProcessInstanceIdIsNull);
        }
        for (QdParticipateEntity participateEntity : participateEntities) {
            String userId = participateEntity.getUserId();
            //给学生发送导入报名成功信息
            sendMassage2ItemRegistration(classEntity.getClassName(),userId);
        }
        result.success("共" + (list.size() - nullContent) + "条,导入成功：" + successCnt + "条,导入失败："
                + errors.size() + "条"
                + (CollectionUtils.isEmpty(errors) ? "" :
                ",错误原因：" + String.join(" ", errors)));
        return result;
    }

    public  boolean isMobile(String mobile) {
        //String regex = "^((13[0-9])|(14[5,7,9])|(15([0-3]|[5-9]))|(16[5,6])|(17[0-8])|(18[0-9])|(19[1、5、8、9]))\\d{8}$";
        String regex = "^1(3[0-9]|4[01456879]|5[0-35-9]|6[2567]|7[0-8]|8[0-9]|9[0-35-9])\\d{8}$";
        Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(mobile);
        return m.matches();
    }
    public  boolean isQQ(String qq) {
        //先验证是否为5—12位数字
        if(qq.length() < 5 || qq.length() > 12) {
            return false;
        }
        //首位不能是0
        if(qq.charAt(0) == '0') {
            return false;
        }
        //验证每一位数字都在1-9内
        for(int x = 0;x < qq.length();x++) {
            char ch = qq.charAt(x);
            if(ch < '0' || ch > '9') {
                return false;
            }
        }
        return true;
    }

    public String checkDate(String date) {
        //boolean convertSuccess = true;
        String dateConvert="";
        // 指定日期格式为四位年/两位月份/两位日期，注意yyyy/MM/dd区分大小写；
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        try {
            // 设置lenient为false.
            // 否则SimpleDateFormat会比较宽松地验证日期，比如2007/02/29会被接受，并转换成2007/03/01
            format.setLenient(false);
//            format.parse(date);
            dateConvert= format.format(format.parse(date));
        } catch (ParseException e) {
            // e.printStackTrace();
            // 如果throw java.text.ParseException或者NullPointerException，就说明格式不对
//            convertSuccess = false;
            dateConvert="false";
        }
        return dateConvert;
    }

    //导入报名消息提醒
    public void sendMassage2ItemRegistration(String itemName,String username) {
        if (null != itemName) {
            String title = "第二课堂状态提示";
            StringBuilder contentBuilder = new StringBuilder();
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(username, appid);
            if (null == wxMesUserInfo) {
                log.error("给课程导入人员发送消息，未找到接收人：{} 的信息", username);
                return;
            }
            String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有新的项目提醒";
            String openId = wxMesUserInfo.getOpenId();
            contentBuilder.append(wxMesUserInfo.getRealname()+"同学，管理员已将您添加至“"+ itemName+"”班级报名人员名单");
            String remark = "青春科大智慧团学综合信息平台";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(username)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }

    @Override
    public List<QdParticipateEntity> getPartOkByClassId(String id) {
        QueryWrapper<QdParticipateEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("class_id", id)
                .eq("del_flag", 0)
                .eq("apply_status", QdStatusEnum.ybm_status.getCode());//通过报名公示
        List<QdParticipateEntity> list = this.list(queryWrapper);
        return list;
    }

    /**
     * 我要报名修改后再提交
     * @param qdParticipate
     * @return
     */
    @Override
    @Transactional
    public Result<QdParticipateEntity> edit(QdParticipateEntity qdParticipate) {
        Result<QdParticipateEntity> result = new Result<QdParticipateEntity>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        qdParticipate.setUpdateBy(sysUser.getId());
        qdParticipate.setUpdateTime(new Date());

        //暂存
        if (QdStatusEnum.zc_status.getCode().equals(qdParticipate.getApplyStatus())){
            //填写了个人简介，存到简介表
            if (!StringUtils.isEmpty(qdParticipate.getResumeId())){
                QdResumeEntity qdResumeEntity = qdResumeService.getById(qdParticipate.getResumeId());
                qdResumeEntity.setContent(qdParticipate.getResumeInfo());
                qdResumeEntity.setUpdateBy(sysUser.getId());
                qdResumeEntity.setUpdateTime(new Date());
                qdResumeService.updateById(qdResumeEntity);
            }else if (!StringUtils.isEmpty(qdParticipate.getResumeInfo()) && StringUtils.isEmpty(qdParticipate.getResumeId())){
                QdResumeEntity qdResumeEntity = new QdResumeEntity();
                qdResumeEntity.setContent(qdParticipate.getResumeInfo());
                qdResumeEntity.setCreateBy(sysUser.getId());
                qdResumeEntity.setCreateTime(new Date());
                boolean save = qdResumeService.save(qdResumeEntity);
                if (!save){throw new ZsxcBootException("个人简介数据不符合规范！");}
                qdParticipate.setResumeId(qdResumeEntity.getId());
            }
            if (StringUtils.isEmpty(qdParticipate.getApplicationTime())){
                qdParticipate.setApplicationTime("否");
            }
            this.updateById(qdParticipate);
            result.success("暂存成功！");
            return result;
        }
        //判断是否在报名时间内
        QdClassEntity qdClassEntity = qdClassService.getById(qdParticipate.getClassId());
        long nowDate = System.currentTimeMillis();
        if (qdClassEntity.getApplyStarttime().getTime() > nowDate) {
            throw new ZsxcBootException("报名还未开始，不允许报名！");

        }else if (qdClassEntity.getApplyEndtime().getTime() < nowDate) {
            throw new ZsxcBootException("报名已经结束，不允许继续报名！");

        }

        if (!StringUtils.isEmpty(qdParticipate.getApplicationTime())){
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM");
            try {
                Date date = simpleDateFormat.parse(qdParticipate.getApplicationTime());
                if (date.getTime() > nowDate) {
                    throw new ZsxcBootException("入党申请时间不能大于当前时间！");
                }
            }catch (Exception e){
                log.error("时间转换错误",e);
            }

        }

        //根据用户id和班级id查询报名记录
        QdParticipateEntity qdParticipateEntity =  baseMapper.qryRegistrationRecord(qdParticipate);
        if (!Objects.isNull(qdParticipateEntity)) {throw new ZsxcBootException("已在该班级报过名，请勿重复报名。");}

        if (StringUtils.isEmpty(qdParticipate.getApplicationTime())){
            qdParticipate.setApplicationTime("否");
        }

        //填写了个人简介，存到简介表
        if (!StringUtils.isEmpty(qdParticipate.getResumeId())){
            QdResumeEntity qdResumeEntity = qdResumeService.getById(qdParticipate.getResumeId());
            qdResumeEntity.setContent(qdParticipate.getResumeInfo());
            qdResumeEntity.setUpdateBy(sysUser.getId());
            qdResumeEntity.setUpdateTime(new Date());
            qdResumeService.updateById(qdResumeEntity);
        }else if (!StringUtils.isEmpty(qdParticipate.getResumeInfo()) && StringUtils.isEmpty(qdParticipate.getResumeId())){
            QdResumeEntity qdResumeEntity = new QdResumeEntity();
            qdResumeEntity.setContent(qdParticipate.getResumeInfo());
            qdResumeEntity.setCreateBy(sysUser.getId());
            qdResumeEntity.setCreateTime(new Date());
            boolean save = qdResumeService.save(qdResumeEntity);
            if (!save){throw new ZsxcBootException("个人简介数据不符合规范！");}
            qdParticipate.setResumeId(qdResumeEntity.getId());
        }
//        //结束流程
//        String processInstanceId = qdParticipate.getProcessInstanceId();
//        if(!StringUtils.isEmpty(processInstanceId)){
//            ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
//            processEngine.getRuntimeService().deleteProcessInstance(processInstanceId,"删除当前流程");
//        }

        //启动新的流程实例
        if (oConvertUtils.isEmpty(qdParticipate.getTzb())){
            throw new ZsxcBootException("请选择团支部");
        }
        ProcessInstance processInstance = qdRegistrationReviewService.startApplyProcess(qdParticipate, sysUser.getId());
        //修改后再提交，审核状态重置
        qdParticipate.setProcessInstanceId(processInstance.getId());
        qdParticipate.setApplyStatus(0);
        boolean ok = this.updateById(qdParticipate);

        return ok ? result.success("修改成功!") : result.error500("修改失败!");
    }

    @Override
    public IPage<QdParticipateEntity> getParticipate(QdParticipateEntity qdParticipate, Integer pageNo, Integer pageSize) {
        //获取登陆人信息
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(sysUser)){
            throw new ZsxcBootException("未能获取到登录人信息，请联系管理员");
        }
         //校验团委身份
        Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getUsername());
        Boolean YtwPersonInCharge = sysDepartMapper.isYTwPersonInCharge(sysUser.getUsername());
        if (!(twPersonInCharge || YtwPersonInCharge)){
            throw new ZsxcBootException("非团委负责人不可查看");
        }

        String classId = qdParticipate.getClassId();
        if (oConvertUtils.isEmpty(classId)) throw new ZsxcBootException("请传入班级id");

        QdClassEntity classEntity = qdClassService.getById(classId);
        if (oConvertUtils.isEmpty(classEntity)) throw new ZsxcBootException("没有找到对应的班级");

        Page<QdParticipateEntity> page = new Page<>(pageNo, pageSize);

        IPage<QdParticipateEntity> participateEntityIPage = qdParticipateMapper.pageList(page,qdParticipate);
        int i = 1;
        for (QdParticipateEntity qdParticipateEntity : participateEntityIPage.getRecords()) {
            Integer applyStatus = qdParticipateEntity.getApplyStatus();
            qdParticipateEntity.setApplyStatusName(QdStatusEnum.codeOf(applyStatus).getDesc());
//            String index = String.valueOf((pageNo-1)*pageSize + i + 1);
            String index = String.valueOf(i++);
            qdParticipateEntity.setSort(index);
        }
        return participateEntityIPage;

    }

    @Override
    public List<QdParticipateEntity> getParticipateAll(QdParticipateEntity qdParticipate) {
        return qdParticipateMapper.pageListAll(qdParticipate);
    }

    /**
     * 报名数据删除
     * @param id
     */
    @Override
    public void deleteById(String id) {
        QdParticipateEntity qdParticipateEntity = this.getById(id);
        if (Objects.isNull(qdParticipateEntity)){throw new ZsxcBootException("该报名数据已被删除，请刷新后重试！"); }
        if (!qdParticipateEntity.getApplyStatus().equals(QdStatusEnum.zc_status.getCode())){
            throw new ZsxcBootException("该报名数据已被审核，不可删除");
        }
        qdParticipateMapper.deleteById(id);
    }

    /**
     * 查询班级状态为公示已结束报名人员
     * @param classId
     * @return
     */
    @Override
    public List<QdParticipateEntity> qryByClassId(String classId) {
        return qdParticipateMapper.qryByClassId(classId);
    }

    /**
     * 查询班级报名状态为已报名和已结业报名人员
     * @param classId
     * @return
     */
    @Override
    public List<QdParticipateEntity> qryYBMByClassId(String classId) {
        return qdParticipateMapper.qryYBMByClassId(classId);
    }

    /**
     * 查询班级报名状态为申请中的数据
     * @param classId
     * @return
     */
    @Override
    public List<QdParticipateEntity> qryApplyingByClassId(String classId) {
        return qdParticipateMapper.qryApplyingByClassId(classId);
    }

    @Override
    public void sendMessage2DeletePerson(String className,String userId) {

        String title = "第二课堂状态提示";
        StringBuilder contentBuilder = new StringBuilder();
        WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(userId, appid);
        if (null == wxMesUserInfo) {
            log.error("给被删除人员发送消息，未找到接收人：{} 的信息", userId);
            return;
        }
        String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有新的提醒";
        String openId = wxMesUserInfo.getOpenId();
        contentBuilder.append(wxMesUserInfo.getRealname()+"同学，您已被管理员从“"+className+"”报名人员名单中删除");
        String remark = "青春科大智慧团学综合信息平台";
        WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                .setUseCommonTemplate(Boolean.TRUE)
                .setTheme(theme)
                .setTitle(title)
                .setUserId(userId)
                .setCreateDate(new Date())
                .setContent(contentBuilder.toString())
                .setOpenId(openId)
                .setRemark(remark);
        wxMessageSender.wxMessageSend(wxCommonMsgInfo);
    }

    /**
     * 一键驳回班级内所有报名审核未结束流程（也可做删除）
     * @param classId 班级id
     */
    @Override
    public Result<?> cancelUnfinishedBmProcess(String classId) {
        Result<?> result = new Result<>();
        //校验登录人权限
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //查询二课领导小组用户
//        List<String> xtwUserList = sysUserService.qryUserByRole(ROLE_XTWSJ).stream().map(SysUser::getId).collect(Collectors.toList());
        Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(loginUser.getUsername());
        if (!twPersonInCharge) throw new ZsxcBootException("非校团委无权限操作");
        //获取班级信息，班级内所有报名未审核完成数据
        List<QdParticipateEntity> unFinishedList = this.list(new LambdaQueryWrapper<QdParticipateEntity>()
                .eq(QdParticipateEntity::getClassId, classId)
                .eq(QdParticipateEntity::getDelFlag, 0)
                .in(QdParticipateEntity::getApplyStatus, "0", "1"));
        QdClassEntity qdClass = qdClassMapper.selectById(classId);

        //批量驳回
        List<String> idList = unFinishedList.stream().map(QdParticipateEntity::getId).collect(Collectors.toList());
        try{
            if (idList.size()>0){
                List<String> taskList = qdParticipateMapper.getUnfinishedReview(idList);
                ItemAuditDTO itemAuditDTO = new ItemAuditDTO();
                //设为驳回可修改，驳回意见统一定义
                itemAuditDTO.setAuditNote("您的“"+oConvertUtils.null2String(qdClass.getClassName())+"”报名申请由于未在规定时间内完成审核，现已被统一驳回。")
                        .setRejectstatus(0)
                        .setStatus(0);
                taskList.forEach(taskId -> {
                    itemAuditDTO.setTaskId(taskId);
                    Map<String, Object> variables = new HashMap<>();
                    variables.put("status", itemAuditDTO.getStatus());
                    String processInstanceId = qdRegistrationReviewService.registrationReview(itemAuditDTO, loginUser.getId(), variables, Boolean.FALSE);
                    // 推送消息
                    campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
                });
                return result.success("统一驳回成功");
            }else {
                return result.success("无待审核报名人员数据");
            }
        }catch (Exception e){
            log.error("一键驳回错误信息()",e);
            return Result.error(e.getMessage());
        }
    }
}
