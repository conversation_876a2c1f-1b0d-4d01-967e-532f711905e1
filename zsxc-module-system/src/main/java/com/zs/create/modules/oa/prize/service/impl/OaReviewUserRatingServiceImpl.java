package com.zs.create.modules.oa.prize.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.oa.prize.entity.*;
import com.zs.create.modules.oa.prize.mapper.OaReviewUserRatingMapper;
import com.zs.create.modules.oa.prize.mapper.OaReviewWaitUserMapper;
import com.zs.create.modules.oa.prize.service.*;
import jodd.util.URLDecoder;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description 评审用户打分Service实现层
 * @email null
 * @date 2021-01-23 15:23:38
 * @Version: V1.0
 */
@Service
public class OaReviewUserRatingServiceImpl extends ServiceImpl<OaReviewUserRatingMapper, OaReviewUserRatingEntity> implements OaReviewUserRatingService {

    @Autowired
    OaPrizeService oaPrizeService;
    @Autowired
    OaPrizeReviewService oaPrizeReviewService;
    @Autowired
    OaDeclareUserService oaDeclareUserService;
    @Autowired
    OaReviewWaitUserService oaReviewWaitUserService;
    @Resource
    private OaReviewWaitUserMapper oaReviewWaitUserMapper;


    /**
     * 评审打分/投票
     *
     * @param oaReviewUserRatingEntity
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void saveUserRating(OaReviewUserRatingEntity oaReviewUserRatingEntity) {
        if (Objects.isNull(oaReviewUserRatingEntity)) {
            throw new ZsxcBootException("您没有提交数据");
        }

        OaPrizeReviewEntity review = oaPrizeReviewService.getById(oaReviewUserRatingEntity.getReviewId());
        if (review == null) {
            throw new ZsxcBootException("当前评审不存在");
        }

        Date date = new Date();
        if (date.compareTo(review.getSt()) < 0 || date.compareTo(review.getEt()) > 0) {
            throw new ZsxcBootException("该奖项评审时间已结束！");
        }

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        oaReviewUserRatingEntity.setJudgesId(sysUser.getId());
        oaReviewUserRatingEntity.setCreateBy(sysUser.getId());
        oaReviewUserRatingEntity.setCreateTime(new Date());

        OaReviewUserRatingEntity oaReviewUser = baseMapper.queryByUserIdAndJudgeId(oaReviewUserRatingEntity);

        if (Objects.nonNull(oaReviewUser) && OaPrizeReviewEntity.ZERO.equals(review.getType()) && oaReviewUser.getPoll() > 0){
            throw new ZsxcBootException("已投票，请勿重复投票！");
        }
        if (OaPrizeReviewEntity.ZERO.equals(review.getType())){
            QueryWrapper<OaReviewUserRatingEntity> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("review_id",oaReviewUserRatingEntity.getReviewId());
            queryWrapper.eq("judges_id",sysUser.getId());
            List<OaReviewUserRatingEntity> oaReviewUserRatingEntities = baseMapper.selectList(queryWrapper);
            if (!CollectionUtils.isEmpty(oaReviewUserRatingEntities) && oaReviewUserRatingEntities.size() >= review.getCheckMax()){
                throw new ZsxcBootException("您已超过最大投票数，不可再投票");
            }
        }

//        if (Objects.nonNull(oaReviewUser) && OaPrizeReviewEntity.ONE.equals(review.getType())){
//            throw new ZsxcBootException("已打分，请勿重复打分！");
//        }
        if ("0".equals(review.getType())){
            oaReviewUserRatingEntity.setScore(new BigDecimal(0));
        }else if ("1".equals(review.getType())){
            oaReviewUserRatingEntity.setPoll(0);
        }

        if (Objects.nonNull(oaReviewUser)){
            oaReviewUserRatingEntity.setId(oaReviewUser.getId());
        }
        this.saveOrUpdate(oaReviewUserRatingEntity);

        OaReviewWaitUserEntity oaReviewWaitUserEntity = oaReviewWaitUserMapper.queryByUserIdAndReviewId(oaReviewUserRatingEntity.getUserId(), oaReviewUserRatingEntity.getReviewId());

//      BigDecimal avgScore = score.divide(new BigDecimal(scoreList.size() + list.size()), 2, BigDecimal.ROUND_HALF_UP);
        if ("0".equals(review.getType())) {
            if (Objects.nonNull(oaReviewWaitUserEntity)) {
                int poll = oaReviewWaitUserEntity.getPoll() + oaReviewUserRatingEntity.getPoll();
                oaReviewWaitUserEntity.setPoll(poll);
            }
            oaReviewWaitUserService.saveOrUpdate(oaReviewWaitUserEntity);
        }
    }

    /**
     * 根据评审id、用户id、评委id查询具体的评审详情
     *
     * @param user
     * @return
     */
    @Override
    public OaReviewUserRatingEntity queryByUserIdAndJudgeId(OaReviewUserRatingEntity user) {
        return baseMapper.queryByUserIdAndJudgeId(user);
    }

    /**
     * 根据评审id查询全部数据
     * @param reviewId
     * @return
     */
    @Override
    public List<OaReviewUserRatingEntity> queryByReviewId(String reviewId) {
        return baseMapper.queryByReviewId(reviewId);
    }
}
