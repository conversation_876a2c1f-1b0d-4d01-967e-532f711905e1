package com.zs.create.modules.bigScreen.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zs.create.modules.bigScreen.entity.SysBigScreenModelEntity;
import com.zs.create.modules.bigScreen.entity.SysBigScreenSevenDayEntity;
import com.zs.create.modules.bigScreen.mapper.SysBigScreenSevenDayMapper;
import com.zs.create.modules.bigScreen.service.SysBigScreenSevenDayService;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.service.ScItemService;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import com.zs.create.modules.system.service.ISysLogService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 大屏Service实现层
 *
 * <AUTHOR> @email 
 * @date 2022-10-10 14:27:36
 * @Version: V1.0
 */
@Service
public class SysBigScreenSevenDayServiceImpl extends ServiceImpl<SysBigScreenSevenDayMapper, SysBigScreenSevenDayEntity> implements SysBigScreenSevenDayService {

    @Autowired
    private ScItemService scItemService;

    @Autowired
    private ISysLogService logService;
    @Autowired
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private SysBigScreenSevenDayMapper sysBigScreenSevenDayMapper;

    @Override
    public void add() {
        isExistDelete();
        SysBigScreenSevenDayEntity sysBigScreenSevenDayEntity=new SysBigScreenSevenDayEntity();

        //待审核
        LambdaQueryWrapper<ScItemEntity> scItemEntityLqw=new LambdaQueryWrapper<>();
        scItemEntityLqw.eq(ScItemEntity::getExamineStatus,"1");
        scItemEntityLqw.eq(ScItemEntity::getDelFlag,"0");
        int count = scItemService.count(scItemEntityLqw);
        sysBigScreenSevenDayEntity.setExamine(count);

        //进行中
        int afood=scItemService.getAfoodItemNum();
        sysBigScreenSevenDayEntity.setAfoot(afood);

        //访问量
//        Calendar calendar = new GregorianCalendar();
//        calendar.set(Calendar.HOUR_OF_DAY, 0);
//        calendar.set(Calendar.MINUTE, 0);
//        calendar.set(Calendar.SECOND, 0);
//        calendar.set(Calendar.MILLISECOND, 0);
//        Date et = calendar.getTime();
//        calendar.add(Calendar.DATE, -1);
//        Date st = calendar.getTime();
//        Long todayIp = logService.findTodayVisitCount(st, et);

        long todaylogin = sysDepartMapper.selectlogin();
        long yeslogin = Long.valueOf(sysBigScreenSevenDayMapper.selectYesVis());

        long todayIp = todaylogin - yeslogin;

        sysBigScreenSevenDayEntity.setVisits((int) todayIp)
                .setVisitsAll((int) todaylogin);
        this.save(sysBigScreenSevenDayEntity);

    }

    public void isExistDelete() {
        Date date=new Date();
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
        String format = format2.format(date);
        LambdaQueryWrapper<SysBigScreenSevenDayEntity> lqw=new LambdaQueryWrapper<>();
        lqw.likeRight(SysBigScreenSevenDayEntity::getCreateTime,format);
        List<SysBigScreenSevenDayEntity> list = this.list(lqw);
        List<String> collect = list.stream().map(SysBigScreenSevenDayEntity::getId).collect(Collectors.toList());
        if (list.size()!=0){
            this.removeByIds(collect);
        }
    }

}
