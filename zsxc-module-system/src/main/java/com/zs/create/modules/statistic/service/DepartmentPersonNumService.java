package com.zs.create.modules.statistic.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.entity.ScItemHoursEntity;
import com.zs.create.modules.item.entity.ScItemRegistrationEntity;
import com.zs.create.modules.statistic.entity.DepartHours;
import com.zs.create.modules.statistic.entity.DepartPersonNum;
import com.zs.create.modules.statistic.entity.DepartStatisticVo;
import com.zs.create.modules.statistic.entity.ScItemVo;
import com.zs.create.modules.system.entity.SysDepart;

import java.util.List;

public interface DepartmentPersonNumService {

    Page<DepartPersonNum> page(DepartStatisticVo vo, Integer pageNo, Integer pageSize);

    Page<ScItemRegistrationEntity> itemNumList(DepartStatisticVo vo, Integer pageNo, Integer pageSize);

    Page<ScItemEntity> itemList(DepartStatisticVo vo, Integer pageNo, Integer pageSize);


    List<DepartPersonNum> listPersonNums(DepartStatisticVo vo);

    /**
     * 参与项目次数钻取下载列表
     * @param vo
     * @return
     */
    List<ScItemRegistrationEntity> itemNumListExportXls(DepartStatisticVo vo);

    /**
     * 查看详情列表下载列表
     * @param vo
     * @return
     */
    List<ScItemVo> itemListExportXls(DepartStatisticVo vo);
}
