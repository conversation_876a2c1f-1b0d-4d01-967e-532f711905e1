<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.statistic.mapper.SuperviseMonthlyReportMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.statistic.entity.SuperviseMonthlyReportEntity"
               id="superviseMonthlyReportMap">
        <result property="id" column="id"/>
        <result property="time" column="time"/>
        <result property="collegeId" column="college_id"/>
        <result property="content" column="content"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <select id="page" resultMap="superviseMonthlyReportMap">
        SELECT smr.id, smr.time, smr.college_id, smr.create_time
        FROM supervise_monthly_report smr
        INNER JOIN (select item_value,sort_order from sys_dict_item
        where dict_id =(select id from sys_dict where dict_code='statistic_college')) d on d.item_value=smr.college_id
        <where>
            <if test="vo.collegeId != null">
                and college_id = #{vo.collegeId}
            </if>
            <if test="vo.collegeId == null">
                <foreach collection="depIds" item="depId" open="and college_id in (" separator="," close=")">
                    #{depId}
                </foreach>
            </if>
            <if test="vo.startTime != null">
                and time >= #{vo.startTime}
            </if>
            <if test="vo.endTime != null">
                and time &lt;= #{vo.endTime}
            </if>
        </where>
        order by smr.time desc, d.sort_order
    </select>
</mapper>
