package com.zs.create.modules.oa.booking.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.oa.booking.entity.OaBookingOrderEntity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.oa.booking.entity.bookingOrderVo;

import java.util.List;
import java.util.Map;

/**
 * @Description 场馆预订模块—订单Service层
 *
 * <AUTHOR> @email 
 * @date 2023-05-05 11:12:18
 * @Version: V1.0
 */
public interface OaBookingOrderService extends IService<OaBookingOrderEntity> {

    Result<?> showData(String venueId, String bookingDate);

    Result<?> showVenueInfo(String venueId);

    void checkRange(String venueId);

    Result<?> userInfo();

    void add(OaBookingOrderEntity oaBookingOrder);

    void delete(String id);

    OaBookingOrderEntity queryById(String id);

    IPage<bookingOrderVo> myOrder(Page<bookingOrderVo> page, String venueId);

    IPage<bookingOrderVo> orderAll(Page<bookingOrderVo> page, bookingOrderVo orderVo);

    List<bookingOrderVo> orderAll(bookingOrderVo orderVo);

    List<Map<String, Object>> getAllOrder(List<String> dates, String venueId);


    List<Map<String, Object>> bookingList(String venueId, String dateSearch);
}

