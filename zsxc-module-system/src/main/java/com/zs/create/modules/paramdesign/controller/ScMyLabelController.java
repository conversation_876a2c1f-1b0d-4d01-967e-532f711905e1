package com.zs.create.modules.paramdesign.controller;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.paramdesign.entity.ScMyLabelEntity;
import com.zs.create.modules.paramdesign.service.ScMyLabelService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * @Description 我的标签Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-24 11:16:11
 * @Version: V1.0
 */
@Slf4j
@Api(tags="我的标签")
@RestController
@RequestMapping("/paramdesign/scMyLabel")
public class ScMyLabelController {
    @Autowired
    private ScMyLabelService scMyLabelService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "我的标签-分页列表查询")
    @ApiOperation(value="我的标签-分页列表查询", notes="我的标签-分页列表查询")
    @GetMapping(value = "/allLabel")
    public Result<List<ScMyLabelEntity>> queryPageList() {
        return scMyLabelService.selectMyLabel();
    }

    /**
     * 添加
     */
    @AutoLog(value = "我的标签-点击修改先删除所有我的标签，然后保存前段传递过来的标签id集合进行关联")
    @ApiOperation(value="我的标签-点击修改", notes="我的标签-点击修改")
    @PostMapping(value = "/addMyLabel")
    public Result<?> addMyLabel(String labelIds) {
        return scMyLabelService.addMyLabel(labelIds);
    }

    /**
     * 添加
     */
    @AutoLog(value = "我的标签-添加")
    @ApiOperation(value="我的标签-添加", notes="我的标签-添加")
    @PostMapping(value = "/add")
    public Result<ScMyLabelEntity> add(@RequestBody ScMyLabelEntity scMyLabel) {
            Result<ScMyLabelEntity> result = new Result<ScMyLabelEntity>();
            scMyLabelService.save(scMyLabel);
            result.success("添加成功！");
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "我的标签-编辑")
    @ApiOperation(value="我的标签-编辑", notes="我的标签-编辑")
    @PutMapping(value = "/edit")
    public Result<ScMyLabelEntity> edit(@RequestBody ScMyLabelEntity scMyLabel) {
        Result<ScMyLabelEntity> result = new Result<ScMyLabelEntity>();
        ScMyLabelEntity scMyLabelEntity = scMyLabelService.getById(scMyLabel.getId());
        if(scMyLabelEntity==null) {
           return result.error500("未找到对应实体");
        }else {
            boolean ok = scMyLabelService.updateById(scMyLabel);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "我的标签-通过id删除")
    @ApiOperation(value="我的标签-通过id删除", notes="我的标签-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
            scMyLabelService.removeById(id);
		    return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "我的标签-批量删除")
    @ApiOperation(value="我的标签-批量删除", notes="我的标签-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<ScMyLabelEntity> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<ScMyLabelEntity> result = new Result<ScMyLabelEntity>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.scMyLabelService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "我的标签-通过id查询")
    @ApiOperation(value="我的标签-通过id查询", notes="我的标签-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScMyLabelEntity> queryById(@RequestParam(name="id",required=true) String id) {
        Result<ScMyLabelEntity> result = new Result<ScMyLabelEntity>();
        ScMyLabelEntity scMyLabel = scMyLabelService.getById(id);
        if(scMyLabel==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(scMyLabel);
            result.setSuccess(true);
        }
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<ScMyLabelEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                ScMyLabelEntity scMyLabel = JSON.parseObject(deString, ScMyLabelEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(scMyLabel, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<ScMyLabelEntity> pageList = scMyLabelService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "我的标签列表");
        mv.addObject(NormalExcelConstants.CLASS, ScMyLabelEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("我的标签列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ScMyLabelEntity> listScMyLabels = ExcelImportUtil.importExcel(file.getInputStream(), ScMyLabelEntity.class, params);
                scMyLabelService.saveBatch(listScMyLabels);
                return Result.ok("文件导入成功！数据行数:" + listScMyLabels.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
