<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.budget.mapper.OaBudgetProjectMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.budget.entity.OaBudgetProjectEntity" id="oaBudgetProjectMap">
        <result property="id" column="id"/>
        <result property="budgetId" column="budget_id"/>
        <result property="itemName" column="item_name"/>
        <result property="module" column="module"/>
        <result property="form" column="form"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>