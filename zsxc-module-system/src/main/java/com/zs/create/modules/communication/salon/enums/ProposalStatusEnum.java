package com.zs.create.modules.communication.salon.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

public enum ProposalStatusEnum {
    REVIEWREJECTED("结项审核驳回", -15),
    TOPICREJECTED("话题驳回" , -10),
    GUESTREJECTED("嘉宾驳回", -5),
    GUESTCONFIRMED("嘉宾待确认", 0),
    TOPICREVIEW("话题审核中" , 5),
    SIGN("报名中(话题审核通过)" , 10),
    SIGNEND("报名结束" , 15) ,
    CANCEL("已取消（报名人数未达到上限30%）" , 20) ,
    LAUNCHITEM("已发起项目" , 25),
    KNOTAUDIT("结项待审核" , 30),
    KNOTPASS("已举办(结项审核通过)" , 35);

    ProposalStatusEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }
    private String name;
    @EnumValue
    private Integer code;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
