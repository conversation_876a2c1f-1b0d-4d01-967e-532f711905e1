package com.zs.create.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.base.util.RedissonLock;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.modules.item.entity.*;
import com.zs.create.modules.item.enums.HoursAuditStatusEnum;
import com.zs.create.modules.item.mapper.ScItemSignMapper;
import com.zs.create.modules.item.service.ScItemHoursService;
import com.zs.create.modules.item.service.ScItemRegistrationService;
import com.zs.create.modules.item.service.ScItemSignService;
import com.zs.create.modules.item.service.ScItemWorksService;
import com.zs.create.modules.system.entity.*;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import com.zs.create.modules.system.mapper.SysScientificqiValueMapper;
import com.zs.create.modules.system.model.CanSignupModel;
import com.zs.create.modules.system.service.ISysUserService;
import com.zs.create.modules.system.service.SysScientificqiValueService;
import com.zs.create.modules.system.service.SysWhiteListService;
import com.zs.create.util.RedisUtil;
import org.apache.commons.lang3.time.DateUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Service
public class SysScientificqiValueServiceImpl extends ServiceImpl<SysScientificqiValueMapper, SysScientificqiValueEntity> implements SysScientificqiValueService {

    private static final String CACHE_KEY = "KQZ:FIRST_LOGIN::";

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private RedissonLock redissonLock;
    @Autowired
    private RedisUtil redisUtil;
    @Autowired
    private ISysUserService sysUserService;
    @Lazy
    @Autowired
    private ScItemRegistrationService scItemRegistrationService;
    @Lazy
    @Autowired
    private ScItemHoursService scItemHoursService;
    @Autowired
    private SysWhiteListService sysWhiteListService;
    @Autowired
    private ScItemSignService scItemSignService;
    @Autowired
    private ScItemWorksService scItemWorksService;
    @Resource
    private SysScientificqiValueMapper scientificqiValueMapper;

    @Resource
    private SysDepartMapper sysDepartMapper;

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void firstLogin() {
        try {
            String cacheKey = getCacheKey();
            if (!Boolean.TRUE.equals(redisTemplate.hasKey(cacheKey))) {
                redisTemplate.opsForHash().put(cacheKey, "init", 1);
                redisTemplate.expire(cacheKey, 1, TimeUnit.DAYS);
            }

            LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
            if (loginUser == null){
                throw new ZsxcBootException("用户信息获取失败");
            }
            String userId = loginUser.getId();

            String lockKey = cacheKey + ":" + userId;
            if (redissonLock.lock(lockKey, 3)) {
                if (Boolean.TRUE.equals(redisTemplate.opsForHash().hasKey(cacheKey, userId))) {
                    return;
                }

                String time = DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH时mm分").format(LocalDateTime.now());
                SysUser user = sysUserService.getUserById(userId);
                changeValue(user, 1, 1, null, time + "签到成功", null);

                redisTemplate.opsForHash().put(cacheKey, userId, 1);
            }
            redissonLock.release(lockKey);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void signup(SysUser user, ScItemEntity scItem) {
        String itemId = scItem.getId();
        String message = scItem.getItemName() + " 项目报名成功";
        changeValue(user, 1, 1, itemId, message, null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void cancelSignup(SysUser user, ScItemEntity scItem) {
        int value = -1;

        Date st = scItem.getSt();
        if (st != null) {
            Date now = new Date();
            long diff = st.getTime() - now.getTime();
            long hours = TimeUnit.MILLISECONDS.toHours(diff);
            if (hours < 12) {
                // 在项目开始前12小时内取消报名
                value = -9;
            } else if (hours < 24) {
                // 在项目开始24小时内12小时前取消报名
                value = -6;
            } else if (hours < 48) {
                // 在项目开始48小时内24小时前取消报名
                value = -3;
            }
        }

        String itemId = scItem.getId();
        String message = scItem.getItemName() + " 项目取消报名";
        changeValue(user, 1, value, itemId, message, null);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void update(ScItemEntity scItem) {
        String itemId = scItem.getId();
        Date today = DateUtils.truncate(new Date(), Calendar.DATE);

        List<ScientificqiValuePunishment> punishmentList = getPunishment(scItem);
        for (ScientificqiValuePunishment punishment : punishmentList) {
            String userId = punishment.getUserId();
            int value = punishment.getValue();
            int days = punishment.getDays();
            SysUser user = sysUserService.getUserById(userId);

            // 项目报名不参加的行为，未有签到时长、未提交作品实行7日（自然日）内不得报名任何项目的限制
            Date endTime = null;
            if (days != 0) {
                endTime = DateUtils.addDays(today, days);
                if (user.getEndTime() != null && today.before(user.getEndTime())) {
                    // 在限制期内再次出现不参与行为，限制期累加7日
                    endTime = DateUtils.addDays(user.getEndTime(), days);
                }
            }

            int type = days == 0 ? 1 : 2;
            String message = punishment.getReason() + "，项目名称：" + scItem.getItemName();
            changeValue(user, type, value, itemId, message, endTime);
        }
    }

    @Override
    public CanSignupModel canSignup(SysUser user) {
        // 2025-05-06 增加老师不校验科气值
        if("T".equals(user.getType())){
            return new CanSignupModel(true, null);
        }
        if (user.getScientificqiValue() < 0) {
            // 校验白名单
            Wrapper<SysWhiteListEntity> wrapper = Wrappers.<SysWhiteListEntity>lambdaQuery()
                    .eq(SysWhiteListEntity::getUserId, user.getId());
            int count = sysWhiteListService.count(wrapper);
            if (count == 0) {
                // 不在白名单中
                return new CanSignupModel(false, "当前科气值<0，无法报名。");
            }
        }

        Date endTime = user.getEndTime();
        if (endTime != null) {
            Date now = new Date();
            if (now.before(endTime)) {
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月dd日");
                String time = sdf.format(endTime);
                return new CanSignupModel(false, "当前账户处于限制状态，将于" + time + "结束。");
            }
        }
        return new CanSignupModel(true, null);
    }

    @Override
    public List<ScientificqiValuePunishment> getPunishment(ScItemEntity scItem) {
        // 项目创建时间在2022-3-19之前不处理
        if (scItem.getCreateTime().getTime() < 1647619200000L) {
            return Collections.emptyList();
        }

        String itemId = scItem.getId();

        Wrapper<ScItemRegistrationEntity> registrationQueryWrapper = Wrappers.<ScItemRegistrationEntity>lambdaQuery()
                .eq(ScItemRegistrationEntity::getItemId, itemId)
                .eq(ScItemRegistrationEntity::getDelFlag, DelFlagEnum.NO_DEL.getCode());
        // 报名用户
        List<ScItemRegistrationEntity> scItemRegistrationEntityList = scItemRegistrationService.list(registrationQueryWrapper);
        Set<String> registrationUserList = scItemRegistrationEntityList.stream()
                .map(ScItemRegistrationEntity::getUserId)
                .collect(Collectors.toSet());
        if (registrationUserList.isEmpty()) {
            return Collections.emptyList();
        }

        Wrapper<ScItemHoursEntity> itemHourWrapper = Wrappers.<ScItemHoursEntity>lambdaQuery()
                .eq(ScItemHoursEntity::getItemId, itemId)
                .eq(ScItemHoursEntity::getStatus, HoursAuditStatusEnum.AUDIT_PASS.getCode())
                .eq(ScItemHoursEntity::getDelFlag, DelFlagEnum.NO_DEL.getCode());
        // 授予学时的用户
        List<ScItemHoursEntity> scItemHoursEntityList = scItemHoursService.list(itemHourWrapper);
        Set<String> hoursUserSet = scItemHoursEntityList.stream()
                .map(ScItemHoursEntity::getUserId)
                .collect(Collectors.toSet());

        // 未授予学时的用户
        List<String> userIdList = registrationUserList.stream()
                .filter(id -> !hoursUserSet.contains(id))
                .collect(Collectors.toList());
        if (userIdList.isEmpty()) {
            return Collections.emptyList();
        }

        // 过滤不存在的学生id
        userIdList = sysUserService.lambdaQuery()
                .in(SysUser::getId, userIdList)
                .eq(SysUser::getDelFlag, DelFlagEnum.NO_DEL.getCode())
                .select(SysUser::getId)
                .list()
                .stream()
                .map(SysUser::getId)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(userIdList)) return Collections.emptyList();

        List<ScientificqiValuePunishment> list = new LinkedList<>();
        if (ScItemEntity.ITEM_FORM.equals(scItem.getForm())) {
            // 1. 现场参与
            // 1.1 只签到
            // 1.2 只签退
            // 1.3 未签到未签退
            // 1.4 已签到已签退
            List<ScItemSignEntity> scItemSignList = ((ScItemSignMapper) scItemSignService.getBaseMapper()).listUserItemSignHours(scItem.getId(), userIdList);
            for (String userId : userIdList) {
                ScientificqiValuePunishment punishment = new ScientificqiValuePunishment()
                        .setUserId(userId);
                long count = scItemSignList.stream()
                        .filter(itemSign -> userId.equals(itemSign.getCreateBy()))
                        .count();
                if (count == 1) {
                    // 只签到 or 只签退
                    punishment.setValue(-9)
                            .setDays(0)
                            .setReason("未按要求参与项目");
                } else {
                    // 其他
                    punishment.setValue(-20)
                            .setDays(7)
                            .setReason("未参与项目");
                }
                list.add(punishment);
            }
        } else {
            // 2. 提交作品
            // 2.1 作品质量不合格
            // 2.2 恶意参与
            // 2.3 未提交作品
            List<ScItemWorksEntity> scItemWorksList = scItemWorksService.listItemWorks(scItem.getId(), userIdList);
            for (String userId : userIdList) {
                List<ScItemWorksEntity> worksList = scItemWorksList.stream()
                        .filter(itemWorks -> userId.equals(itemWorks.getCreateBy()))
                        .collect(Collectors.toList());
                // 判断是否有"作品质量不合格"
                boolean b = worksList.stream()
                        .anyMatch(itemWorks -> "1".equals(itemWorks.getDeleteReasonType()));
                if (!b) {
                    // 未提交 or 恶意提交
                    ScientificqiValuePunishment punishment = new ScientificqiValuePunishment()
                            .setUserId(userId)
                            .setValue(-20)
                            .setDays(7)
                            .setReason(worksList.isEmpty() ? "未提交作品" : "恶意提交作品");
                    list.add(punishment);
                }
            }
        }

        return list;
    }

    /**
     * 修改科气值并记录
     *
     * @param user    用户
     * @param type    科气值记录类型
     * @param value   变更值
     * @param itemId  项目id
     * @param message 变更理由
     * @param endTime 限制报名截止时间
     */
    private void changeValue(SysUser user, int type, int value, String itemId, String message, Date endTime) {
        String updateValue = (value > 0 ? "+" : "") + value;
        SysScientificqiValueEntity sysScientificqiValueEntity = new SysScientificqiValueEntity()
                .setId(SnowIdUtils.uniqueLongHex())
                .setCreateBy(user.getId())
                .setDelFlag(DelFlagEnum.NO_DEL.getCode())
                .setType(type)
                .setItemId(itemId)
                .setContent(message)
                .setEndTime(endTime)
                .setUpdateValue(updateValue);
        save(sysScientificqiValueEntity);

        user.setScientificqiValue(user.getScientificqiValue() + value);
        user.setLoginNum(user.getLoginNum()+1);
        if (endTime != null) {
            user.setEndTime(endTime);
        }
        sysUserService.updateById(user);
    }

    private String getCacheKey() {
        return CACHE_KEY + DateTimeFormatter.ISO_LOCAL_DATE.format(LocalDate.now());
    }

    /**
     * 科气值列表展示
     * @param page
     * @param userName
     * @param type
     * @return
     */
    @Override
    public IPage<SysScientificqiValueVO> queryPage(Page<SysScientificqiValueVO> page, String userName, String type) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (!sysDepartMapper.isSecPersonInCharge(sysUser.getUsername())){
            return new Page<>();
        }
        return scientificqiValueMapper.queryPage(page,userName,type);
    }

    @Override
    public IPage<SysScientificqiValueVO> operationLog(Page<SysScientificqiValueVO> page, String userName) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (!sysDepartMapper.isSecPersonInCharge(sysUser.getUsername())){
            return new Page<>();
        }

        return scientificqiValueMapper.operationLog(page,userName);
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public boolean editScientificqiValue(SysScientificqiValueVO sysScientificqiValueVO) {

        if (StringUtils.isEmpty(sysScientificqiValueVO.getUserName())
                || StringUtils.isEmpty(sysScientificqiValueVO.getScientificqiValue())
                || StringUtils.isEmpty(sysScientificqiValueVO.getOldValue())
                || StringUtils.isEmpty(sysScientificqiValueVO.getContent())){
            throw new ZsxcBootException("参数错误");
        }
        if (sysScientificqiValueVO.getContent().length()>512){
            throw new ZsxcBootException("事由输入过长，最大可输入512个字符");
        }

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //判断操作人权限
        if (!sysDepartMapper.isSecPersonInCharge(sysUser.getUsername()))
            throw new ZsxcBootException("非二领办老师暂无权限操作");

        //算分
        Integer oldValue = Integer.parseInt(sysScientificqiValueVO.getOldValue());
        Integer nowValue = Integer.parseInt(sysScientificqiValueVO.getScientificqiValue());
        int updateValue = nowValue - oldValue;
        String updateValueS = (updateValue > 0 ? "+" : "") + updateValue;

        SysScientificqiValueEntity sysScientificqiValueEntity = new SysScientificqiValueEntity()
                .setId(SnowIdUtils.uniqueLongHex())
                .setCreateBy(sysScientificqiValueVO.getUserName())
                .setDelFlag(DelFlagEnum.NO_DEL.getCode())
                .setType(1)
                .setContent(sysScientificqiValueVO.getContent())
                .setUpdateValue(updateValueS)
                .setOperator(sysUser.getUsername());
        save(sysScientificqiValueEntity);

        SysUser user = sysUserService.getUserByName(sysScientificqiValueVO.getUserName());

        user.setScientificqiValue(nowValue);
        sysUserService.updateById(user);
        return true;
    }

    /**
     * 科气值导出
     * @param userName
     * @param type
     * @return
     */
    @Override
    public List<SysScientificqiValueVO> queryList(String userName, String type) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (!sysDepartMapper.isSecPersonInCharge(sysUser.getUsername())){
            return new ArrayList<>();
        }
        return scientificqiValueMapper.queryPage(userName,type);
    }

    @Override
    public List<SysScientificqiValueVO> operationLogList(String userName) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (!sysDepartMapper.isSecPersonInCharge(sysUser.getUsername())){
            return new ArrayList<>();
        }

        return scientificqiValueMapper.operationLog(userName);
    }
}
