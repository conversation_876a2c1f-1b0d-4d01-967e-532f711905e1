package com.zs.create.modules.personal.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.personal.entity.SysUserRecordEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Map;

/**
 * @Description 用户编辑记录表Service层
 *
 * <AUTHOR> @email 
 * @date 2022-07-28 09:19:46
 * @Version: V1.0
 */
public interface SysUserRecordService extends IService<SysUserRecordEntity> {

    IPage<SysUserRecordEntity> taskPage(SysUserRecordEntity record, Integer pageNo, Integer pageSize);

    void audit(SysUserRecordEntity record);

    void sendMessage(SysUserRecordEntity record);
}

