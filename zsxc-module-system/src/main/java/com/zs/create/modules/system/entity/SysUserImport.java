package com.zs.create.modules.system.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
/**
 * 用户信息维护
 */

/**
 * <AUTHOR>
 * @date 2022/9/9
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@TableName("v_bzks")
public class SysUserImport implements Serializable {

    //id
    private String dqztdm;
    //学号
    private String xh;
    //真实姓名
    private String xm;
    //性别
    private String xbdm;
    //出生年月
    private String csrq;
    //政治面貌
    private String zzmmdm;
    //yxdm
    private String yxdm;
    //yxmc
    private String yxmc;
    //gid
    private String gid;
    //CURUSING
    private String curusing;

}
