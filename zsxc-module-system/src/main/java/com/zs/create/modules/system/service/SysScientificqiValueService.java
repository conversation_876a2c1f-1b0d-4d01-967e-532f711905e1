package com.zs.create.modules.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.system.entity.ScientificqiValuePunishment;
import com.zs.create.modules.system.entity.SysScientificqiValueEntity;
import com.zs.create.modules.system.entity.SysScientificqiValueVO;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.model.CanSignupModel;

import java.util.List;

/**
 * 2022/3/11 重写科气值接口
 * 1. 每天第一次打开小程序增加科气值
 * 2. 报名项目增加科气值
 * 3. 取消报名减少科气值
 * 4. 报名但不参与项目减少科气值
 * 5. 是否可以报名（科气值<0 或在 惩罚期内）
 *
 * <AUTHOR>
 */
public interface SysScientificqiValueService extends IService<SysScientificqiValueEntity> {

    /**
     * 每天第一次打开小程序
     */
    void firstLogin();

    /**
     * 报名项目
     *
     * @param user   用户
     * @param scItem 项目
     */
    void signup(SysUser user, ScItemEntity scItem);

    /**
     * 取消报名项目
     *
     * @param user   用户
     * @param scItem 项目
     */
    void cancelSignup(SysUser user, ScItemEntity scItem);

    /**
     * 更新科气值
     *
     * @param scItem 项目
     */
    void update(ScItemEntity scItem);

    /**
     * 是否可以报名项目
     *
     * @param user 用户
     */
    CanSignupModel canSignup(SysUser user);

    /**
     * 获取处罚信息
     *
     * @param scItem 项目
     */
    List<ScientificqiValuePunishment> getPunishment(ScItemEntity scItem);

    IPage<SysScientificqiValueVO> queryPage(Page<SysScientificqiValueVO> page, String userName, String type);


    IPage<SysScientificqiValueVO> operationLog(Page<SysScientificqiValueVO> page, String userName);

    boolean editScientificqiValue(SysScientificqiValueVO sysScientificqiValueVO);


    List<SysScientificqiValueVO> queryList(String userName, String type);

    List<SysScientificqiValueVO> operationLogList(String userName);
}

