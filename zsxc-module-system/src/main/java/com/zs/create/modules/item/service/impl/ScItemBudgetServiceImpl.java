package com.zs.create.modules.item.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.modules.item.entity.ScItemBudgetEntity;
import com.zs.create.modules.item.mapper.ScItemBudgetMapper;
import com.zs.create.modules.item.service.ScItemBudgetService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;

/**
 * @Description 项目预算Service实现层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-23 14:30:09
 * @Version: V1.0
 */
@Service
public class ScItemBudgetServiceImpl extends ServiceImpl<ScItemBudgetMapper, ScItemBudgetEntity> implements ScItemBudgetService {

    @Autowired
    ScItemBudgetMapper scItemBudgetMapper ;
    /*@Override
    @Transactional
    public Boolean modifyBudgetItemStatus(String itemId , Integer budgetItemStatus) {
        QueryWrapper<ScItemBudgetEntity> wrapper = new QueryWrapper();
        wrapper.eq("item_id" ,itemId);
        List<ScItemBudgetEntity> list = this.list(wrapper);
        if(!CollectionUtils.isEmpty(list)){
            list.forEach(d->d.setBudgetItemStatus(budgetItemStatus));
            return this.updateBatchById(list);
        }else{
            return Boolean.FALSE;
        }
    }*/

    @Override
    public BigDecimal getBudgetChargeAmount(String deptId, String xn, String xq) {
        BigDecimal budgetChargeAmount = scItemBudgetMapper.getBudgetChargeAmount(deptId, xn, xq);
        return budgetChargeAmount == null ? new BigDecimal(0) : budgetChargeAmount;
    }


}
