package com.zs.create.modules.oa.ticketManagement.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2023/4/11
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="订单查看对象", description="订单")
public class actOrderVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 订单id
     */
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 序号
     */
    @ApiModelProperty(value = "序号")
    private String sort;
    /**
     * 人员学工号
     */
    @ApiModelProperty(value = "人员学工号")
    private String userId;
    /**
     * 姓名
     */
    @ApiModelProperty(value = "姓名")
    private String userName;
    /**
     * 活动id
     */
    @ApiModelProperty(value = "activityId")
    private String activityId;
    /**
     * 活动名称
     */
    @ApiModelProperty(value = "活动名称")
    private String activityName;
    /**
     * 活动时长
     */
    @ApiModelProperty(value = "活动时长")
    private String duration;
    /**
     * 活动海报
     */
    @ApiModelProperty(value = "活动名称")
    private String picUrls;
    /**
     * 活动开始时间
     */
    @ApiModelProperty(value = "活动开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date st;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "活动结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date et;
    /**
     * 场馆id
     */
    @ApiModelProperty(value = "场馆id")
    private String venueId;
    /**
     * 场馆名称
     */
    @ApiModelProperty(value = "场馆名称")
    private String venueName;
    /**
     * 座位编号
     */
    @ApiModelProperty(value = "座位编号")
    private String seatNumber;
    /**
     * 是否参加(核验)  0:未参加  1:已参加 默认0
     */
    @ApiModelProperty(value = "是否参加(核验)  0:未参加  1:已参加 默认0")
    private Integer isJoin;
    /**
     * 订单状态：0:锁定未确定；1:已确认
     */
    @ApiModelProperty(value = "订单状态：0:锁定未确定；1:已确认")
    private Integer status;
    /**
     * 条形码Base64编码
     */
    @ApiModelProperty(value = "条形码Base64编码")
    private String barCode;
    /**
     * 活动结束时间
     */
    @ApiModelProperty(value = "下单时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderSureTime;

    /**
     * 订单创建时间
     */
    @ApiModelProperty(value = "订单创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date orderCreateTime;


    /**
     * 移动端判断显示 待确认/待使用/已完成/已过期
     */
    @ApiModelProperty(value = "状态显示用")
    private String statusName;

    /**
     * 封装id/座位编号/条形码map
     */
    @ApiModelProperty(value = "封装id/座位编号/条形码map")
    private List<Map<String , Object>> dataMapList;

    /**
     * 移动端排序字段  0待确认    1待使用  2已完成  3已过期
     */
    @ApiModelProperty(value = "移动端排序字段")
    private Integer orderStatus;
}
