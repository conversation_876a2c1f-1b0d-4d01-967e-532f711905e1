package com.zs.create.modules.oa.prize.entity;

import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName suzheng
 * @Description TODO
 * @date 2022-12-20  15:03
 * @Version 1.0
 */
@Data
public class OaDeclareUserDto implements Serializable {
    private static final long serialVersionUID = -5780080740342397170L;

    /**
     * 奖项等级Id
     */
    private String auditLevel;
    /**
     * 审核意见
     */
    private String auditNote;
    /**
     * 审核结果（0：驳回 1：通过）
     */
    private Integer auditType;
    /**
     * 驳回选项（0：直接驳回 1：修改后提交）
     */
    private Integer rejectstatus;
    /**
     * 待审核奖项ids
     */
    private String ids;
}
