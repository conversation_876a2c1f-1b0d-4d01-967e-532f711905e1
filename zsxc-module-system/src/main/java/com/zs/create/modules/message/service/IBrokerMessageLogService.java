package com.zs.create.modules.message.service;

import com.zs.create.modules.mq.entity.BrokerMessageLog;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

/**
 * @createUser hy
 * @createTime 2020-5-13
 * @description
 */
public interface IBrokerMessageLogService {
    Page<BrokerMessageLog> findAll(Pageable pageable, BrokerMessageLog brokerMessageLog);

    BrokerMessageLog findBrokerLog(String id);

    void del(String id);

    void removeByIds(List<String> asList);
}
