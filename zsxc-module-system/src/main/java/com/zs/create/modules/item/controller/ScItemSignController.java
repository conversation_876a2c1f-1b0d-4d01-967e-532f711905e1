package com.zs.create.modules.item.controller;

import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.modules.item.entity.ItemSignDTO;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.entity.ScItemSignEntity;
import com.zs.create.modules.item.mapper.ScItemMapper;
import com.zs.create.modules.item.service.ScItemSignService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * @Description 项目签到签退Controller层
 * @email <EMAIL>
 * @date 2020-07-05 20:53:13
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "项目签到签退")
@RestController
@RequestMapping("/item/sign")
public class ScItemSignController {

    @Autowired
    private ScItemSignService scItemSignService;
    @Autowired
    private ScItemMapper scItemMapper;
    @Value("${qrUrl}")
    private String qrUrl;

    @AutoLog(value = "项目签到签退-签到/签退信息")
    @ApiOperation(value = "项目签到签退-签到/签退信息", notes = "项目签到签退-签到/签退信息")
    @GetMapping(value = "/basicInfo/{itemId}/{type}")
    public Result<?> getSignBasic(@PathVariable String itemId, @PathVariable Integer type) {
        ItemSignDTO signBasicInfo = scItemSignService.getSignBasicInfo(itemId, type);
        return Result.ok(signBasicInfo);
    }

    /**
     * @param itemId 项目id
     * @param type   0 签到 1 签退
     * @return
     * @throws UnsupportedEncodingException
     */
    @AutoLog(value = "项目签到签退-获取静态二维码")
    @ApiOperation(value = "项目签到签退-获取静态二维码", notes = "项目签到签退-获取静态二维码")
    @GetMapping(value = "/staticQrcode/{itemId}/{type}")
    public Result<?> getStaticQrcode(@PathVariable String itemId, @PathVariable Integer type) throws UnsupportedEncodingException {
        Map<String, Object> res = scItemSignService.getStaticQrCode(itemId, type);
        return Result.ok(res);
    }


    @AutoLog(value = "项目签到签退-获取动态二维码")
    @ApiOperation(value = "项目签到签退-获取动态二维码", notes = "项目签到签退-获取动态二维码")
    @GetMapping(value = "/dynamicQrcode/{itemId}/{type}")
    public Result<?> getDynamicQrcode(@PathVariable String itemId
            , @PathVariable Integer type) throws Exception {
        Object dynamicQrCode = scItemSignService.getDynamicQrCode(itemId, type);
        return Result.ok(dynamicQrCode);
    }


    @AutoLog(value = "项目签到签退-签到/签退")
    @ApiOperation(value = "项目签到签退-扫码签到/签退", notes = "项目签到签退-扫码签到/签退")
    @GetMapping(value = "/wx/{itemId}/{type}")
    public ModelAndView sign(@PathVariable String itemId, @PathVariable Integer type
            , String qrCodeType, String signTag, String code) {
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        if (scItemEntity == null) throw new ZsxcBootException("itemId：参数错误");
        ModelAndView mv = new ModelAndView("sign/sign_callback");
        String message = "";
        String signName = ScItemSignEntity.SIGN_IN_TYPE.equals(type) ? "签到" : "签退";
        Boolean success = Boolean.FALSE;
        try {
            success = scItemSignService.sign(itemId, type, signTag, qrCodeType, code);
        } catch (ZsxcBootException ze) {
            message = ze.getMessage();
        } catch (Exception e) {
            log.error("扫码签到/签退错误：{}", e.getMessage());
            message = "扫码" + signName + "出错了,联系管理员";
        }
        if (success) message = "恭喜," + signName + "成功！";

        Map<String, Object> resultMap = new HashMap<>(2);
        resultMap.put("success", success);
        resultMap.put("message", message);
        mv.addObject("qrUrl", qrUrl);
        mv.addObject("result", resultMap);
        return mv;
    }


    @AutoLog(value = "项目签到签退-关闭/开启二维码")
    @ApiOperation(value = "项目签到签退-关闭/开启二维码", notes = "项目签到签退-关闭/开启二维码")
    @PutMapping(value = "/staticQrCode/status/{itemId}/{type}")
    @NoRepeatSubmit(expireSeconds = 1)
    public Result<?> modifyStaticQrCodeStatus(@PathVariable String itemId, @PathVariable Integer type) {
        Boolean modifyStatus = scItemSignService.modifyQrCodeStatus(itemId, type);
        return Result.ok(modifyStatus);
    }


    //签到签退二维码权力下放
    @AutoLog(value = "签到签退二维码权力下放")
    @ApiOperation(value = "签到签退二维码权力下放", notes = "签到签退二维码权力下放")
    @PostMapping(value = "/staticQrCode/status/{itemId}/{userIds}")
    public Result<?> signDevolution(@PathVariable String itemId, @PathVariable String userIds) {
        Boolean modifyStatus = scItemSignService.signDevolution(itemId, userIds);
        return Result.ok(modifyStatus);
    }
}
