package com.zs.create.modules.item.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 团队成员审核记录表
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-05-13 10:07:40
 */
@Data
@TableName("team_audit_record")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="team_audit_record对象", description="团队成员审核记录表")
public class TeamAuditRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId
	private String id;
	/**
	 * 所属团队id
	 */
	private String teamId;
	/**
	 * 队长id
	 */
	private String leaderId;
	/**
	 * 被审核人id
	 */
	private String userId;
	/**
	 * 审核类型： 1通过 2不通过
	 */
	private String type;
	/**
	 * 删除标记
	 */
	private Integer delFlag;
	/**
	 * 创建人id
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

}
