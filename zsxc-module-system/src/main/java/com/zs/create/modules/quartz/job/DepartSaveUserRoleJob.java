package com.zs.create.modules.quartz.job;

import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.service.ISysDepartService;
import lombok.extern.slf4j.Slf4j;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

/**
 * @createUser hy
 * @createTime 2020-3-30
 * @description
 */
@Slf4j
public class DepartSaveUserRoleJob implements Job {

    @Autowired
    ISysDepartService sysDepartService;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        List<SysDepart> list=sysDepartService.list();
        for (SysDepart depart:list){
            sysDepartService.jbsaveAndDelDepartUser(depart);
        }
        System.out.println("========================处理完成==================");
    }
}
