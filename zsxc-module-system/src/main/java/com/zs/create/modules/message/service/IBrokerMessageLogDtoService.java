package com.zs.create.modules.message.service;

import com.zs.create.modules.mq.entity.BrokerMessageLogDto;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;

public interface IBrokerMessageLogDtoService {
    Page<BrokerMessageLogDto> findAll(Pageable pageable, BrokerMessageLogDto brokerMessageLogDto);

    List<BrokerMessageLogDto> findById(String title);

    BrokerMessageLogDto findByMessageId(String id);
}
