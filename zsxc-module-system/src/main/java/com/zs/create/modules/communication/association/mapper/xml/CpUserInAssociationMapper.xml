<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.communication.association.mapper.CpUserInAssociationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.communication.association.entity.CpUserInAssociationEntity" id="cpUserInAssociationMap">
        <result property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="realname" column="realname"/>
        <result property="associationId" column="association_id"/>
        <result property="status" column="status"/>
        <result property="message" column="message"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="writeTime" column="write_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="isEnd" column="is_end"/>
        <result property="totalScore" column="total_score"/>
    </resultMap>


    <select id="pageList" resultMap="cpUserInAssociationMap">
        SELECT  su.`id`,
        su.`association_id`,
        su.`association_name`,
        su.total_score,
        su.username,
        su.realname,
        su.process_instance_id,
        su.`dept_id`,
        su.`dept_name`,
        DATE_FORMAT(su.`write_time`,'%Y-%m-%d %H:%i:%s') write_time,
        DATE_FORMAT(su.`st`,'%Y-%m-%d %H:%i:%s') st,
        DATE_FORMAT(su.`et`,'%Y-%m-%d %H:%i:%s') et,
        (case
        when now() > su.et
        then 6
        else 7
        end
        ) timeStatus,
        (case
        when now() &lt; su.st
        then 0
        when  su.status = 0
        then 1
        when  su.status = 1 and su.examine_status = -2
        then 2
        when  su.examine_status = 0
        then 3
        when  su.examine_status = 1
        then 4
        when  su.examine_status = -1
        then 5
        end
        ) status FROM cp_user_in_association su

        <where>
            su.association_id=#{entity.associationId}
            <if test="entity.username != null and entity.username != ''">
                and su.username like CONCAT('%',#{entity.username},'%')
            </if>

            <if test="entity.realname != null and entity.realname != ''">
                and su.realname like CONCAT('%',#{entity.realname},'%')
            </if>
            <if test="entity.deptName != null and entity.deptName != ''">
                and su.dept_name like CONCAT('%',#{entity.deptName},'%')
            </if>
            and su.status = '1'
            and  su.examine_status in ('-1','1','0')
        </where>


    </select>

    <select id="queryByUser" resultType="java.lang.String">
        SELECT association_id FROM cp_user_in_association c
        WHERE username=#{userId}

    </select>
    <select id="getStatusById" resultType="com.zs.create.modules.communication.association.entity.CpUserInAssociationEntity">
        SELECT * FROM cp_user_in_association c
        WHERE dept_id =#{deptId}
        and association_id = #{associationId}


    </select>
    <!-- 0 未开始 1 暂存 2 提交/审核中 3 审核通过 4 驳回 5 -->
    <select id="queryDeptByUser" resultType="com.zs.create.modules.communication.association.entity.CpUserInAssociationEntity">
  SELECT   su.`id`,
        su.`association_id`,
        su.`association_name`,
        su.`dept_id`,
        su.`dept_name`,
        DATE_FORMAT(su.`write_time`,'%Y-%m-%d %H:%i:%s') write_time,
        DATE_FORMAT(su.`st`,'%Y-%m-%d %H:%i:%s') st,
        DATE_FORMAT(su.`et`,'%Y-%m-%d %H:%i:%s') et,
        (case
          when now() > su.et
          then 6
          else 7
          end
          ) timeStatus,
        (case
          when now() &lt; su.st
          then 0
          when  su.status = 0
          then 1
          when  su.status = 1 and su.examine_status = -2
          then 2
          when  su.examine_status = 0
          then 3
          when  su.examine_status = 1
          then 4
          when  su.examine_status = -1
          then 5
        end
        ) status,
        su.process_instance_id,
        su.is_end,
        su.total_score
FROM
  sys_depart s,
  cp_user_in_association su
WHERE
su.dept_id = s.`id`
  AND FIND_IN_SET(
    #{userId},
    s.`person_in_charge`
  )
  <if test="id!=null and id !=''">
      and su.id = #{id}
  </if>
order by su.create_time desc
    </select>


    <select id="todoTaskPage" resultType="com.zs.create.modules.communication.association.entity.CpUserInAssociationTaskVo">
        SELECT  a.`id`,
        a.`association_id`,
        a.`association_name`,
        a.username,
        a.realname,
        a.`dept_id`,
        a.`dept_name`,
        DATE_FORMAT(a.`write_time`,'%Y-%m-%d %H:%i:%s') write_time,
        DATE_FORMAT(a.`st`,'%Y-%m-%d %H:%i:%s') st,
        DATE_FORMAT(a.`et`,'%Y-%m-%d %H:%i:%s') et,
        (case
        when now() > a.et
        then 6
        else 7
        end
        ) timeStatus,
        (case
        when now() &lt; a.st
        then 0
        when  a.status = 0
        then 1
        when  a.status = 1 and a.examine_status = -2
        then 2
        when  a.examine_status = 0
        then 3
        when  a.examine_status = 1
        then 4
        when  a.examine_status = -1
        then 5
        end
        ) status,
        a.process_instance_id,
        a.is_end ,
        task.ID_ as task_id from act_ru_task task
        inner join act_ru_identitylink i on i.TASK_ID_ = task.ID_
        INNER JOIN cp_user_in_association a on task.PROC_INST_ID_=a.process_instance_id


        <where>
            and `task`.`SUSPENSION_STATE_` = 1
            <if test="taskQueryDTO.assignee != null and taskQueryDTO.assignee != ''">
                and (
                `task`.`ASSIGNEE_` = #{taskQueryDTO.assignee}
                or (
                `task`.`ASSIGNEE_` is null and `i`.TYPE_ = 'candidate' and (`i`.USER_ID_ = #{taskQueryDTO.assignee})
                )
                )
            </if>
            <if test="taskQueryDTO.deptName != null and taskQueryDTO.deptName != ''">
                and a.dept_name like CONCAT('%',#{taskQueryDTO.deptName},'%')
            </if>
            <if test="taskQueryDTO.username != null and taskQueryDTO.username != ''">
                and a.username like CONCAT('%',#{taskQueryDTO.username},'%')
                or a.realname like CONCAT('%',#{taskQueryDTO.username},'%')
            </if>

        </where>
        order by task.create_time_ desc

    </select>
    <select id="historyTaskPage" resultType="com.zs.create.modules.communication.association.entity.CpUserInAssociationTaskVo">
        SELECT a.`id`,
        a.`association_id`,
        a.`association_name`,
        a.username,
        a.realname,
        a.`dept_id`,
        a.`dept_name`,
        DATE_FORMAT(a.`st`,'%Y-%m-%d %H:%i:%s') st,
        DATE_FORMAT(a.`et`,'%Y-%m-%d %H:%i:%s') et,
        DATE_FORMAT(a.`write_time`,'%Y-%m-%d %H:%i:%s') write_time,
        (case
        when now() > a.et
        then 6
        else 7
        end
        ) timeStatus,
        (case
        when now() &lt; a.st
        then 0
        when  a.status = 0
        then 1
        when  a.status = 1 and a.examine_status = -2
        then 2
        when  a.examine_status = 0
        then 3
        when  a.examine_status = 1
        then 4
        when  a.examine_status = -1
        then 5
        end
        ) status,
        a.process_instance_id,
        a.is_end     FROM cp_user_in_association a
        <where>
            <if test="taskQueryDTO.assignee != null and taskQueryDTO.assignee != ''">
                a.process_instance_id
                in (select  proc_inst_id_ from act_hi_taskinst taskinst  where  taskinst.ASSIGNEE_ = #{taskQueryDTO.assignee})
            </if>
            <if test="taskQueryDTO.deptName != null and taskQueryDTO.deptName != ''">
                and a.dept_name like CONCAT('%',#{taskQueryDTO.deptName},'%')
            </if>
            <if test="taskQueryDTO.username != null and taskQueryDTO.username != ''">
                and a.username like CONCAT('%',#{taskQueryDTO.username},'%')
                or a.realname like CONCAT('%',#{taskQueryDTO.username},'%')
            </if>
        </where>
        order by a.update_time desc
    </select>
</mapper>