<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.system.mapper.SysScientificqiValueMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.system.entity.SysScientificqiValueEntity" id="sysScientificqiValueMap">
        <result property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="updateValue" column="update_value"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="operator" column="operator"/>
    </resultMap>
    <select id="queryPage" resultType="com.zs.create.modules.system.entity.SysScientificqiValueVO">
        SELECT u.username as userName,
               u.realname as realName,
               u.scientificqi_value as scientificqiValue,
               u.type as type
        from sys_user u
    <where>
        u.del_flag = 0
        and u.type != 'T'
        <if test="userName != null and userName !=''">
            and (u.username LIKE CONCAT('%', #{userName}, '%') or u.realname LIKE CONCAT('%', #{userName}, '%'))
        </if>
        <if test="type != null and type !=''">
            and u.type = #{type}
        </if>
    </where>
    </select>
    <select id="operationLog" resultType="com.zs.create.modules.system.entity.SysScientificqiValueVO">
        select sv.id as id,
               sv.operator as operator,
               u.realname as realName,
               sv.create_time as createTime,
               sv.update_value as updateValue,
               sv.content as content,
               sv.create_by as userName
        from sys_user u
        LEFT JOIN sys_scientificqi_value sv on u.username = sv.operator
        where sv.create_by = #{userName}
        and sv.operator is not null
        and sv.operator != ''
        order by sv.create_time desc
    </select>


</mapper>