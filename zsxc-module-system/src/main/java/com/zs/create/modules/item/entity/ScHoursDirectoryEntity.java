package com.zs.create.modules.item.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 项目-学时目录
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-23 16:53:33
 */
@Data
@TableName("sc_hours_directory")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_hours_directory对象", description="项目-学时目录")
public class ScHoursDirectoryEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public static final String IS_PUBLIC_YES = "1";

	public static final String IS_PUBLIC_NO = "0";

	public static final Integer PROCESS_REJECT = 0;
	public static final Integer PROCESS_PASS = 1;

	public static final String processDefinitionKey = "score_aduit";

	/**
	 * id
	 */
	@TableId
	private String id;
	/**
	 * 项目id
	 */
	private String itemId;
	/**
	 * 流程实例id
	 */
	private String processInstanceId;
	/**
	 * 是否公示 0 否  1 是
	 */
	private String isPublic;
	/**
	 * 创建人id
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 审核状态 
	 */
	private Integer status;

	/**
	 * 公示开始时间
	 */
	private Date publicStartTime;

	@TableField(exist = false)
	@ApiModelProperty(value = "任务处理人")
	private String assignee;

}
