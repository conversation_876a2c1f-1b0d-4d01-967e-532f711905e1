package com.zs.create.modules.system.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.system. entity.UserDepartLogEntity;
import com.zs.create.modules.system. service.UserDepartLogService;
import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import com.zs.create.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
/**
 * @Description 人员/部门修改记录Controller层
 *
 * <AUTHOR> @email 
 * @date 2023-03-13 09:19:00
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "人员/部门修改记录")
@RestController
@RequestMapping("/userDepartLog")
public class UserDepartLogController {
    @Autowired
    private UserDepartLogService userDepartLogService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "人员/部门修改记录-分页列表查询")
    @ApiOperation(value = "人员/部门修改记录-分页列表查询", notes = "人员/部门修改记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<UserDepartLogEntity>> queryPageList(UserDepartLogEntity userDepartLog,
                                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                       HttpServletRequest req) {
        Result<IPage<UserDepartLogEntity>> result = new Result<IPage<UserDepartLogEntity>>();
        LambdaQueryWrapper<UserDepartLogEntity> queryWrapper = new LambdaQueryWrapper<>();
        //自行构造查询参数
        Page<UserDepartLogEntity> page = new Page<UserDepartLogEntity>(pageNo, pageSize);
        IPage<UserDepartLogEntity> pageList = userDepartLogService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "人员/部门修改记录-添加")
    @ApiOperation(value = "人员/部门修改记录-添加", notes = "人员/部门修改记录-添加")
    @PostMapping(value = "/add")
    public Result<UserDepartLogEntity> add(@RequestBody UserDepartLogEntity userDepartLog) {
        Result<UserDepartLogEntity> result = new Result<UserDepartLogEntity>();
            userDepartLogService.save(userDepartLog);
        result.success("添加成功！");
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "人员/部门修改记录-编辑")
    @ApiOperation(value = "人员/部门修改记录-编辑", notes = "人员/部门修改记录-编辑")
    @PutMapping(value = "/edit")
    public Result<UserDepartLogEntity> edit(@RequestBody UserDepartLogEntity userDepartLog) {
        Result<UserDepartLogEntity> result = new Result<UserDepartLogEntity>();
            UserDepartLogEntity userDepartLogEntity = userDepartLogService.getById(userDepartLog.getId());
        if (userDepartLogEntity == null) {
            return result.error500("未找到对应实体");
        } else {
            boolean ok = userDepartLogService.updateById(userDepartLog);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "人员/部门修改记录-通过id删除")
    @ApiOperation(value = "人员/部门修改记录-通过id删除", notes = "人员/部门修改记录-通过id删除")
    @PostMapping(value = "/delete/{id}")
    public Result<?> delete(@PathVariable(name = "id") String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.error("请选择要删除的数据!");
        }
            userDepartLogService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "人员/部门修改记录-批量删除")
    @ApiOperation(value = "人员/部门修改记录-批量删除", notes = "人员/部门修改记录-批量删除")
    @PostMapping(value = "/deleteBatch/{ids}")
    public Result<UserDepartLogEntity> deleteBatch(@PathVariable(name = "ids") String ids) {
        Result<UserDepartLogEntity> result = new Result<UserDepartLogEntity>();
        if (ids == null || StringUtils.isEmpty(ids)) {
            result.error500("参数不识别！");
        } else {
            this.userDepartLogService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "人员/部门修改记录-通过id查询")
    @ApiOperation(value = "人员/部门修改记录-通过id查询", notes = "人员/部门修改记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<UserDepartLogEntity> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<UserDepartLogEntity> result = new Result<UserDepartLogEntity>();
            UserDepartLogEntity userDepartLog = userDepartLogService.getById(id);
        if (userDepartLog==null){
            result.error500("未找到对应实体");
        }else{
            result.setResult(userDepartLog);
            result.setSuccess(true);
        }
        return result;
    }
}
