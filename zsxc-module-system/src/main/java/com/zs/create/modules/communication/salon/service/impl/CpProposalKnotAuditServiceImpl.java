package com.zs.create.modules.communication.salon.service.impl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.communication.salon.entity.CpSalonProposalEntity;
import com.zs.create.modules.communication.salon.mapper.CpProposalEvaluationMapper;
import com.zs.create.modules.communication.salon.mapper.CpProposalKnotMapper;
import com.zs.create.modules.communication.salon.service.CpProposalKnotAuditService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


@Service
@Slf4j
public class CpProposalKnotAuditServiceImpl implements CpProposalKnotAuditService {
    @Autowired
    private CpProposalKnotMapper cpProposalKnotMapper;
    @Override
    public Page<CpSalonProposalEntity> taskPage(CpSalonProposalEntity cpSalonProposalEntity, Integer pageNo, Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        cpSalonProposalEntity.setAssignee(sysUser.getId());
        Page<CpSalonProposalEntity> taskVoPage = null;
        if(CpSalonProposalEntity.QUERY_HISTORY.equals(cpSalonProposalEntity.getQueryType())){
            taskVoPage = this.historyTaskPage(cpSalonProposalEntity, pageNo, pageSize);
        }
        if(CpSalonProposalEntity.QUERY_TODO.equals(cpSalonProposalEntity.getQueryType())){
            taskVoPage = this.todoTaskPage(cpSalonProposalEntity , pageNo ,pageSize);
        }
        return taskVoPage;
    }

    /**
     * 代办任务分页
     * @param pageNo
     * @param pageSize
     */
    public Page<CpSalonProposalEntity> todoTaskPage(CpSalonProposalEntity cpSalonProposalEntity,Integer pageNo,Integer pageSize){
        Page<CpSalonProposalEntity> page = new Page<>(pageNo, pageSize);
        page.setRecords(cpProposalKnotMapper.todoTaskPage(page,cpSalonProposalEntity));
        return page;

    }


    /**
     * 历史任务分页
     * @param pageNo
     * @param pageSize
     */
    public Page<CpSalonProposalEntity> historyTaskPage(CpSalonProposalEntity cpSalonProposalEntity,Integer pageNo,Integer pageSize){
        Page<CpSalonProposalEntity> page = new Page<>(pageNo, pageSize);
        page.setRecords(cpProposalKnotMapper.historyTaskPage(page,cpSalonProposalEntity));
        return page;
    }
}
