package com.zs.create.modules.score.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zs.create.modules.score.entity.ScScoreReportEntity;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class UserScoreDto extends ScScoreReportEntity {
    private String username;

    private String realname;

    private String email;

    private String orgCode;

    private String phone;

    private String type;

    private Integer sex;

    private String major;

    private String classes;

    private String avatar;

    private Date birthday;

    private Integer educationalSystem;//学制 0-全日制 1-非全日制

    private Date dateOfAdmission;//入学日期

    private String charge;//负责人

    private String column;

    private String order;

    private String depId;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date st;

    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone ="GMT+8")
    private Date et;
}
