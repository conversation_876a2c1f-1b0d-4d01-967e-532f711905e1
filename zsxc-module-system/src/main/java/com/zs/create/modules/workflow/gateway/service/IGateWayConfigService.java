package com.zs.create.modules.workflow.gateway.service;

import com.zs.create.modules.workflow.gateway.entity.GateWayConfig;

import java.util.List;

/**
 * <AUTHOR> guodl
 * @Date: 2019/8/9 16:42
 * @Description:网关条件接口
 */
public interface IGateWayConfigService {
    /**
     * 根据流程部署ID和网关ID查询当前网关设置的条件
     *
     * @param deploymentId 部署ID
     * @param gateWayId    网关ID
     * @param gateWayType  网关类型
     * @return
     */
    List<GateWayConfig> list(String deploymentId, String gateWayId, String gateWayType);

    /**
     * 根据流程定义ID和网关ID查询当前网关设置的条件
     *
     * @param processDefinitionId 部署ID
     * @param gateWayId           网关ID
     * @param gateWayType         网关类型
     * @return
     */
    List<GateWayConfig> listByProcessDefinitionId(String processDefinitionId, String gateWayId, String gateWayType);
}
