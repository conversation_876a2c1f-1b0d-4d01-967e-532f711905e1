package com.zs.create.modules.item.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

/**
 * <AUTHOR>
 * @Date 2023/9/14
 */
public enum ItemSubmitFlowPdKeyEnum {
    SUBMIT_XST_SQ("校社团申请", "submit_item_xst_sq" , "5")
    ,SUBMIT_YST_SQ("院社团/协会申请", "submit_item_ystxh_sq", "6")
    ,SUBMIT_XJXH_SQ("校级协会申请", "submit_item_xjxh_sq", "7")
    ,SUBMIT_XXYH_SQ("校学研会申请", "submit_item_xxyh_sq", "8")
    ,SUBMIT_YXYH_SQ("院学研会申请", "submit_item_yxyh_sq", "9")
    ,SUBMIT_XMFZR_SQ("项目负责人申请", "submit_item_xmfzr_sq", "")
    ,SUBMIT_BJ_SQ("班级申请", "submit_item_bj_sq", "4")
    ,SUBMIT_TZB_SQ("团支部申请" , "submit_item_tzb_sq" , "10")
    ,SUBMIT_ZZZTZB_SQ("自组织团支部申请" , "submit_item_tzb_sq" , "17")
    ,SUBMIT_ZSTZB_SQ("直属团支部申请", "submit_item_zstzb_sq", "11")
    ,SUBMIT_XQNZX_SQ("校青年中心申请", "submit_item_xqnzx_sq", "12")
    ,SUBMIT_XSDZB_SQ("学生党支部申请", "submit_item_dzb_sq", "13")
    ,SUBMIT_LS_SQ("老师角色申请", "submit_item_ls_sq", "14");

    ItemSubmitFlowPdKeyEnum(String name, String pdKey , String orgType) {
        this.name = name;
        this.pdKey = pdKey;
        this.orgType = orgType;
    }
    private String name;

    private String pdKey;
    @EnumValue
    private String orgType;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPdKey() {
        return pdKey;
    }

    public void setPdKey(String pdKey) {
        this.pdKey = pdKey;
    }

    public String getOrgType() { return orgType; }

    public void setOrgType() { this.orgType = orgType; }

}
