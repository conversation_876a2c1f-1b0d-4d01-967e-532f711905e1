package com.zs.create.config.wx;


import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.WxMpConfigStorage;
import me.chanjar.weixin.mp.config.impl.WxMpRedisConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import redis.clients.jedis.JedisPool;


/**
 * wechat mp configuration
 *
 * <AUTHOR> Wang(https://github.com/binarywang)
 */
@Configuration
@ConditionalOnClass(WxMpService.class)
public class WxMpConfiguration {

    @Autowired
    private JedisPool jedisPool;

    @Value("${gzhAppid}")
    private String appid;
    @Value("${gzhSecret}")
    private String secret;
//    @Value("gzhToken")
//    private String token;
//    @Value("gzhAesKey")
//    private String aesKey;

    @Bean
    @ConditionalOnMissingBean
    public WxMpConfigStorage configStorage() {
        WxMpRedisConfigImpl configStorage = new WxMpRedisConfigImpl(jedisPool);

        configStorage.setAppId(appid);
        configStorage.setSecret(secret);
//        configStorage.setToken(token);
//        configStorage.setAesKey(aesKey);

        return configStorage;
    }

    @Bean
    @ConditionalOnMissingBean
    public WxMpService wxMpService(WxMpConfigStorage configStorage) {
        WxMpService wxMpService = new WxMpServiceImpl();
        wxMpService.setWxMpConfigStorage(configStorage);
        return wxMpService;
    }


}
