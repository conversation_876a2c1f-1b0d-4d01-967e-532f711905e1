package com.zs.create.modules.mobile.sc;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.MogoResult;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.constant.CommonConstant;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.FastJsonConvert;
import com.zs.create.modules.item.utils.PseudoPagingUtil;
import com.zs.create.modules.mq.entity.BrokerMessageLogDto;
import com.zs.create.modules.mq.entity.MessageReciveUserDto;
import com.zs.create.modules.mq.entity.SysMessageDto;
import com.zs.create.modules.system.service.ISysAnnouncementService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;

/**
 * @createUser hy
 * @createTime 2020-7-27
 * @description 通知公告
 */
@Slf4j
@Api(tags="手机端-首页-通知公告")
@RestController
@RequestMapping("/mobile/announcement")
public class AnnouncementApiController {
    @Autowired
    private MongoTemplate mongoTemplate;
    @Autowired
    private ISysAnnouncementService sysAnnouncementService;

    /**
     * @return
     * @功能：手机端-首页-通知公告
     */
    @ApiOperation(value="手机端-首页-通知公告", notes="手机端-首页-通知公告")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Result<IPage<SysMessageDto>> getNotice(@RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                  @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                  @RequestParam(name ="suggestionMsg",required = false) String suggestionMsg){
        Result<IPage<SysMessageDto>> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //通知公告
        Query reciveUserQuery = new Query();
        reciveUserQuery.addCriteria(Criteria.where("reciveUser").is(sysUser.getId()))
                .addCriteria(Criteria.where("msgCategory").is(CommonConstant.DEL_FLAG_1.toString()));
        if ("0".equals(suggestionMsg)) {
            reciveUserQuery.addCriteria(Criteria.where("suggestionMsg").ne("1"));
        } else if ("1".equals(suggestionMsg)) {
            reciveUserQuery.addCriteria(Criteria.where("suggestionMsg").is("1"));
        }
        reciveUserQuery.with(new Sort(Sort.Direction.DESC, "createTime"));
        List<MessageReciveUserDto> messageReciveUserDtos =
                mongoTemplate.find(reciveUserQuery, MessageReciveUserDto.class);
        List<BrokerMessageLogDto> brokerMessageLogDtoList=new ArrayList<>();
        for (MessageReciveUserDto messageReciveUserDto:messageReciveUserDtos) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").is(messageReciveUserDto.getMessageId()))
                    .addCriteria(Criteria.where("title").ne("学生骨干申报提醒"));  //消息业务类型 根据这个判断json对应实体类
            //query.with(new Sort(Sort.Direction.DESC, "publicTime"));
            BrokerMessageLogDto brokerMessageLogDto = mongoTemplate.findOne(query, BrokerMessageLogDto.class);
            if(null != brokerMessageLogDto)
             brokerMessageLogDtoList.add(brokerMessageLogDto);
        }
        IPage<SysMessageDto> page=new Page<>();
        if(CollectionUtil.isNotEmpty(brokerMessageLogDtoList)){
            List<SysMessageDto> sysMessageDtos=new ArrayList<>();
            brokerMessageLogDtoList.stream().forEach(brokerMessageLogDto -> {
                SysMessageDto sysMessageDto = FastJsonConvert.convertJSONToObject(brokerMessageLogDto.getMessageObject(),SysMessageDto.class);
                //塞入主表id这样方便后面根据id查询通知公告详情
                sysMessageDto.setId(brokerMessageLogDto.getId());
                sysMessageDtos.add(sysMessageDto);
            });
            List<SysMessageDto> sysMessageDtoList = (List<SysMessageDto>) PseudoPagingUtil.pseudoPaging(page, sysMessageDtos, pageNo, pageSize);
            Collections.sort(sysMessageDtoList, new Comparator<SysMessageDto>() {
                @Override
                public int compare(SysMessageDto o1, SysMessageDto o2) {
                    return o2.getStick() - o1.getStick();
                }
            });
            page.setRecords(sysMessageDtoList);
        }
        result.setSuccess(true);
        result.setResult(page);
        return result;
    }


    /**
     * @return
     * @功能：手机端-首页-小铃铛
     */
    @ApiOperation(value="手机端-首页-小铃铛", notes="手机端-首页-小铃铛")
    @RequestMapping(value = "/littleBell", method = RequestMethod.GET)
    public Result<List<SysMessageDto>> littleBell(){
        Result<List<SysMessageDto>> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        //通知公告
        Query reciveUserQuery = new Query();
        reciveUserQuery.addCriteria(Criteria.where("reciveUser").is(sysUser.getId()))
                .addCriteria(Criteria.where("msgCategory").is(CommonConstant.DEL_FLAG_1.toString()))
                .addCriteria(Criteria.where("suggestionMsg").is("0"));
        List<MessageReciveUserDto> messageReciveUserDtos =
                mongoTemplate.find(reciveUserQuery, MessageReciveUserDto.class);
        List<BrokerMessageLogDto> brokerMessageLogDtoList=new ArrayList<>();
        for (MessageReciveUserDto messageReciveUserDto:messageReciveUserDtos) {
            Query query = new Query();
            query.addCriteria(Criteria.where("_id").is(messageReciveUserDto.getMessageId()))
                    .addCriteria(Criteria.where("title").ne("学生骨干申报提醒"));  //消息业务类型 根据这个判断json对应实体类
            query.with(new Sort(Sort.Direction.DESC, "publicTime"));
            BrokerMessageLogDto brokerMessageLogDto = mongoTemplate.findOne(query, BrokerMessageLogDto.class);
            if(brokerMessageLogDto != null)
            brokerMessageLogDtoList.add(brokerMessageLogDto);
        }
        List<SysMessageDto> sysMessageDtos=new ArrayList<>();
        if(CollectionUtil.isNotEmpty(brokerMessageLogDtoList)){
            brokerMessageLogDtoList.stream().forEach(brokerMessageLogDto -> {
                SysMessageDto sysMessageDto = FastJsonConvert.convertJSONToObject(brokerMessageLogDto.getMessageObject(),SysMessageDto.class);
                //塞入主表id这样方便后面根据id查询通知公告详情
                sysMessageDto.setId(brokerMessageLogDto.getId());
                if(sysMessageDto.getStick()!=null){
                    if(sysMessageDto.getStick()==1){
                        sysMessageDtos.add(sysMessageDto);
                    }
                }

            });
            //过滤list中非置顶的通知公告
            //sysMessageDtos.stream().filter(sysMessageDto ->sysMessageDto.getStick()==0).collect(Collectors.toList());
        }
        result.setSuccess(true);
        result.setResult(sysMessageDtos);
        return result;
    }


    /**
     * @return
     * @功能：手机端-首页-通知公告-点击查看详情
     */
    @ApiOperation(value="点击查看详情", notes="点击查看详情")
    @RequestMapping(value = "/getMessageById", method = RequestMethod.GET)
    public Result<SysMessageDto> getMessageById(@RequestParam(name = "id", required = true) String id){
        Result<SysMessageDto> result=new Result<>();
        Query BrokerMessageLogDtoQuery = new Query(Criteria
                .where("_id").is(id));
        BrokerMessageLogDto brokerMessageLogDto =
                mongoTemplate.findOne(BrokerMessageLogDtoQuery, BrokerMessageLogDto.class);
        if(brokerMessageLogDto == null) throw new ZsxcBootException("已删除");
        SysMessageDto sysMessageDto = FastJsonConvert.convertJSONToObject(brokerMessageLogDto.getMessageObject(),SysMessageDto.class);
        result.setResult(sysMessageDto);
        result.setSuccess(true);
        return result;
    }


    /**
     * @return
     * @功能：手机端-我的消息
     */
    @AutoLog(value = "我的消息")
    @ApiOperation(value="我的消息", notes="我的消息")
    @RequestMapping(value = "/myMessageList", method = RequestMethod.GET)
    public MogoResult<IPage<BrokerMessageLogDto>> myMessageList(@ApiParam(name = "title" , value = "标题" , required = false) String title,
                                                                @ApiParam(name = "type" , value = "消息类型" , required = false) String type,
                                                                @RequestParam(name = "pageNo" , defaultValue = "0") Integer pageNo,
                                                                @RequestParam(name = "pageSize" , defaultValue = "10") Integer pageSize){
        MogoResult<IPage<BrokerMessageLogDto>> result = new MogoResult<>();
        IPage<BrokerMessageLogDto> pageList = sysAnnouncementService.queryMyMessagePageList(title,type,pageNo, pageSize);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }
}
