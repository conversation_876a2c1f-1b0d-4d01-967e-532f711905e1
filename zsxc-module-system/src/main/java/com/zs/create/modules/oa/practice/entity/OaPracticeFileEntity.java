package com.zs.create.modules.oa.practice.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 团队上传附件信息
 *
 * <AUTHOR> @email 
 * @date 2023-01-11 14:59:00
 */
@Data
@TableName("oa_practice_file")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oa_practice_file对象", description="团队上传附件信息")
public class OaPracticeFileEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public static final String LEADER_ROLE = "0";//领队
	public static final String HEAD_ROLE = "1";//团长
	public static final String member_ROLE = "2";//团员

	/**
	 * id
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "id")
	    private String id;
	/**
	 * 团队id
	 */
	    @ApiModelProperty(value = "团队id")
	    private String teamId;
	/**
	 * 文件名称
	 */
	    @ApiModelProperty(value = "文件名称")
	    private String name;
	/**
	 * 文件地址
	 */
	    @ApiModelProperty(value = "文件地址")
	    private String url;
	/**
	 * 是否为结项材料 0：新增时文件，1：结项文件
	 */
		@ApiModelProperty(value = "是否为结项文件")
		private String endFile;
	/**
	 * 创建时间
	 */
	    @ApiModelProperty(value = "创建时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date createTime;

}
