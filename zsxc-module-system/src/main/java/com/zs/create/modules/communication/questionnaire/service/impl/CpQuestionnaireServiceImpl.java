package com.zs.create.modules.communication.questionnaire.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.config.RabbitmqConfig;
import com.zs.create.modules.communication.questionnaire.dto.QuestionDto;
import com.zs.create.modules.communication.questionnaire.dto.QuestionnaireDto;
import com.zs.create.modules.communication.questionnaire.entity.CpQuestionnaireImportUserEntity;
import com.zs.create.modules.communication.questionnaire.entity.CpUserInQuestionnaireEntity;
import com.zs.create.modules.communication.questionnaire.service.CpQuestionnaireImportUserService;
import com.zs.create.modules.communication.questionnaire.service.CpQuestionnaireService;
import com.zs.create.modules.communication.questionnaire.service.CpUserInQuestionnaireService;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.paramdesign.entity.ScWeeksEntity;
import com.zs.create.modules.paramdesign.mapper.ScWeeksMapper;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.mapper.SysUserDepartMapper;
import com.zs.create.modules.system.mapper.SysUserMapper;
import com.zs.create.modules.system.service.ISysDictService;
import com.zs.create.modules.system.service.ISysUserService;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.mapper.SysWeixinUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.aggregation.Aggregation;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Field;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static org.springframework.data.mongodb.core.query.Criteria.where;

@Slf4j
@Service
public class CpQuestionnaireServiceImpl implements CpQuestionnaireService {

    @Autowired
    MongoTemplate mongoTemplate;
    @Autowired
    CpUserInQuestionnaireService cpUserInQuestionnaireService;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    ScWeeksMapper scWeeksMapper;
    @Autowired
    SysUserDepartMapper sysUserDepartMapper;
    @Autowired
    SysWeixinUserMapper sysWeixinUserMapper;
    @Autowired
    @Lazy
    WxMessageSender wxMessageSender;
    @Autowired
    SysUserMapper sysUserMapper;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    ISysDictService sysDictService;
    @Autowired
    private CpQuestionnaireImportUserService cpQuestionnaireImportUserService;

    @Value("${gzhAppid}")
    String appid;

    public static final String CP_QUESTIONNAIRE_NAME = "cp_questionnaire";

    public static final String CP_USER_QUESTIONNAIRE_NAME = "cp_user_questionnaire";

    private static final String TEACHER = "6";
    private static final String GRADUATE = "5";

    @Override
    public void add(Map<String, Object> map) {
        log.info("start...");
        if(map.get("applyRange").equals("2")){
            String uuidKey = (String) map.get("uuIdKey");
            if (oConvertUtils.isEmpty(uuidKey)) throw new ZsxcBootException("问卷发布对象不能为空，请修改后重新提交");
        }
        //获取当前学期学年
        ScWeeksEntity scWeeksEntity = scWeeksMapper.selectCurrentWeek();
        if (scWeeksEntity==null){
            throw new ZsxcBootException("当前学年学期配置不正确");
        }

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        map.put("delFlag", 0);
        map.put("status", 0);//0暂存，1发布，2未开始，3进行中，4已完成
        map.put("st", format.format(new Date()));
        map.put("create_time", format.format(new Date()));
        map.put("create_by", sysUser.getId());

        int num=Integer.parseInt(sysDictService.getDictByCode("wjdc_num").get(0).getValue());
        int quesNum=(int) map.get("number");
        if (!map.get("applyRange").toString().equals("2")) {
            if (quesNum==0){
                List<String> list=new ArrayList<>();
                if (StringUtils.isNotBlank(map.get("rangeDeptIds").toString())){
                    list=Arrays.asList(map.get("rangeDeptIds").toString().split(","));
                }
                int count=sysUserMapper.userDepAndNjCount(scWeeksEntity.getXq(),map.get("nj").toString(),list);
                if (count>num){
                    throw new ZsxcBootException("问卷填写人数不能大于"+num);
                }
            }else {
                if (quesNum>num){
                    throw new ZsxcBootException("问卷填写人数不能大于"+num);
                }
            }
        }

        mongoTemplate.save(map, CP_QUESTIONNAIRE_NAME);
        log.info("end...");
    }


    @Override
    public Page<Map> queryList(String formName, String status, Integer pageNo, Integer pageSize) throws ParseException {
        Page<Map> page = new Page<>(pageNo, pageSize);
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Criteria cd = where("delFlag").is(0);
        Pattern pattern = Pattern.compile("");
        if (StringUtils.isNotEmpty(formName)) {
            pattern = Pattern.compile(".*?" + formName + ".*");
            cd.and("formName").regex(pattern);
        }
        if (StringUtils.isNotEmpty(status)) {
            if(status.equals("0")){
                cd.and("status").is(Integer.parseInt(status));
            }else {
                if (status.equals("2")){
                 cd.and("st").gt(format.format(new Date()));
                }else if (status.equals("3")){
                  cd.andOperator(
                            Criteria.where("st").lt(format.format(new Date())),
                            Criteria.where("et").gte(format.format(new Date()))
                  );
                }else {
                  cd.and("et").lt(format.format(new Date()));
                }
            }

        }
        Query query = new Query(cd);
        Long count=mongoTemplate.count(query,Map.class,CP_QUESTIONNAIRE_NAME);
        query.with(new Sort(Sort.Direction.DESC, "create_time"));
        query.skip((pageNo - 1) * pageSize).limit(pageSize);
        List<Map> results = mongoTemplate.find(query, Map.class, CP_QUESTIONNAIRE_NAME);
        if (results != null && !CollectionUtils.isEmpty(results)) {
            Date date = new Date();
            for (Map<String, Object> map : results) {
                map.put("id", String.valueOf(map.get("_id")));
                if (map.get("status").toString().equals("1")) {
                    Date st = format.parse(map.get("st").toString());
                    Date et = format.parse(map.get("et").toString());
                    if (date.compareTo(st) == -1) {
                        map.put("status", 2);
                    } else if (date.compareTo(st) == 1 && date.compareTo(et) == -1) {
                        map.put("status", 3);
                    } else {
                        map.put("status", 4);
                    }
                }
                if (map.get("applyRange").toString().equals("2")) {
                    List<String> importRange=new ArrayList<>();
                    LambdaQueryWrapper<CpQuestionnaireImportUserEntity> queryWrapper=new LambdaQueryWrapper<>();
                    queryWrapper.eq(CpQuestionnaireImportUserEntity::getUuidKey,map.get("uuIdKey"));
                    List<CpQuestionnaireImportUserEntity> list = cpQuestionnaireImportUserService.list(queryWrapper);
                    for (CpQuestionnaireImportUserEntity userEntity : list) {
                        importRange.add(userEntity.getUserId()+"/"+userEntity.getUserName());
                    }
                    map.put("importRange",importRange);
                }
            }
        }
        page.setTotal(count);
        page.setRecords(results);
        return page;
    }


    @Override
    public Map<String, Object> getByIdAndUser(String id) throws ParseException {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, Object> map = mongoTemplate.findById(id, Map.class, CP_QUESTIONNAIRE_NAME);
        if (!CollectionUtils.isEmpty(map)) {
            map.put("id", map.get("_id").toString());
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (map.get("status").toString().equals("1")) {
                Date st = format.parse(map.get("st").toString());
                Date et = format.parse(map.get("et").toString());
                if (date.compareTo(st) == -1) {
                    map.put("status", 2);
                } else if (date.compareTo(st) == 1 && date.compareTo(et) == -1) {
                    map.put("status", 3);
                } else {
                    map.put("status", 4);
                }
            }
            if (map.get("applyRange").toString().equals("2")) {
                List<String> importRange=new ArrayList<>();
                LambdaQueryWrapper<CpQuestionnaireImportUserEntity> queryWrapper=new LambdaQueryWrapper<>();
                queryWrapper.eq(CpQuestionnaireImportUserEntity::getUuidKey,map.get("uuIdKey"));
                List<CpQuestionnaireImportUserEntity> list = cpQuestionnaireImportUserService.list(queryWrapper);
                for (CpQuestionnaireImportUserEntity userEntity : list) {
                    importRange.add(userEntity.getUserId()+"/"+userEntity.getUserName());
                }
                map.put("importRange",importRange);
            }
            Query query = new Query();
            query.addCriteria(Criteria.where("questionnaireId").is(id).and("create_by").is(sysUser.getId()));
            List<Map> list = mongoTemplate.find(query, Map.class, CP_USER_QUESTIONNAIRE_NAME);
            if (list.size()>0) map.put("flag",Boolean.TRUE);
            map.put("delete","0");
            return map;
        } else {
            Map<String,Object> result = new HashMap<>();
            //问卷不存在或已删除
            result.put("delete","1");
            return result;
        }
    }

    @Override
    public Map<String, Object> getById(String id) throws ParseException {
        Map<String, Object> map = mongoTemplate.findById(id, Map.class, CP_QUESTIONNAIRE_NAME);
        if (!CollectionUtils.isEmpty(map)) {
            map.put("id", map.get("_id").toString());
            Date date = new Date();
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (map.get("status").toString().equals("1")) {
                Date st = format.parse(map.get("st").toString());
                Date et = format.parse(map.get("et").toString());
                if (date.compareTo(st) == -1) {
                    map.put("status", 2);
                } else if (date.compareTo(st) == 1 && date.compareTo(et) == -1) {
                    map.put("status", 3);
                } else {
                    map.put("status", 4);
                }
            }
            return map;
        } else {
            Map<String,Object> result = new HashMap<>();
            return result;
        }
    }



    @Override
    public void remove(String id) {
        Query query = new Query(where("_id").is(id));
        mongoTemplate.remove(query, CP_QUESTIONNAIRE_NAME);
    }

    @Override
    public void updateById(Map<String, Object> map, String type) {
        if (type.equals("update")){
            if(map.get("applyRange").equals("2")){
                String uuidKey = (String) map.get("uuIdKey");
                if (oConvertUtils.isEmpty(uuidKey)) throw new ZsxcBootException("问卷发布对象不能为空，请修改后重新提交");
            }
        }
        map.remove("_id");
        //条件构造器，拼接条件
        Query query = new Query(Criteria.where("_id").is(map.get("id").toString()));
        Update update = new Update();
        if (map != null && !CollectionUtils.isEmpty(map)) {
            for (Map.Entry<String, Object> entry : map.entrySet()) {
                update.set(entry.getKey(), entry.getValue());
            }
        }
        mongoTemplate.updateFirst(query, update, Map.class, CP_QUESTIONNAIRE_NAME);
    }


    @Override
    public void release(Map<String, Object> map) throws ParseException {
        Map<String, Object> ques = new HashMap<>();
        ques.put("id", map.get("id").toString());
        ques.put("status", 1);
        ques.put("et", map.get("et").toString());
        updateById(ques,"release");

        messageSend(map.get("id").toString());
    }


    /**
     * 发送消息提醒
     *
     */
    public void messageSend(String id){ MessageProperties messageProperties = new MessageProperties();
        messageProperties.getHeaders().put("id", id);
        Message message = new Message("问卷调查消息发送".getBytes(), messageProperties);
        rabbitTemplate.convertAndSend(RabbitmqConfig.EXCHANGE_ROURTING_QUESTIONAIRE,
                    RabbitmqConfig.ROUTINGKEY_QUESTIONAIRE,message);
    }

    //监听消息
    @RabbitListener(queues = {RabbitmqConfig.QUEUE_QUESTIONAIRE})
    public void listenerMessageSend(Message message)throws ParseException{
        String id=message.getMessageProperties().getHeaders().get("id").toString();
        messageUser(id);
    }

    public void messageUser(String id)throws ParseException{
        Map<String, Object> map = getById(id);
        if (CollectionUtil.isNotEmpty(map)) {
            List<SysUser> list = new ArrayList<>();
            List<String> ids = new ArrayList<>();
            if (map.get("applyRange").toString().equals("0")) {
                QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("status", 1);
                queryWrapper.eq("del_flag", 0);
                list = sysUserService.list(queryWrapper);
            } else if (map.get("applyRange").toString().equals("1")) {//0全体人员，1自定义范围
                if (StringUtils.isNotBlank(map.get("rangeDeptIds").toString())
                        && StringUtils.isBlank(map.get("nj").toString())) {
                    list = sysUserMapper.findUserDepart(Arrays.asList(map.get("rangeDeptIds").toString().split(",")));
                } else if (StringUtils.isNotBlank(map.get("rangeDeptIds").toString())
                        && StringUtils.isNotBlank(map.get("nj").toString())) {
                    list = sysUserMapper.findUserDepart(Arrays.asList(map.get("rangeDeptIds").toString().split(",")));
                    removeItem(list, map.get("nj").toString());
                } else if (StringUtils.isBlank(map.get("rangeDeptIds").toString())
                        && StringUtils.isNotBlank(map.get("nj").toString())) {
                    QueryWrapper<SysUser> queryWrapper = new QueryWrapper<>();
                    queryWrapper.eq("status", 1);
                    queryWrapper.eq("del_flag", 0);
                    list = sysUserService.list(queryWrapper);
                    removeItem(list, map.get("nj").toString());
                }
            }else if (map.get("applyRange").toString().equals("2")) {//2.导入的范围
                String uuidKey = (String) map.get("uuIdKey");
                LambdaQueryWrapper<CpQuestionnaireImportUserEntity> queryWrapper=new LambdaQueryWrapper<>();
                queryWrapper.eq(CpQuestionnaireImportUserEntity::getUuidKey,uuidKey);
                List<CpQuestionnaireImportUserEntity> importUserEntities = cpQuestionnaireImportUserService.list(queryWrapper);
                ids= importUserEntities.stream().map(CpQuestionnaireImportUserEntity::getUserId).collect(Collectors.toList());
            }
            if (!map.get("applyRange").toString().equals("2")) {
                Set<String> userIds = list.stream().map(SysUser::getId).collect(Collectors.toSet());
                ids=new ArrayList<>(userIds);
            }

            int quesNum=(int) map.get("number");

            if (quesNum!= 0&&ids.size()>quesNum) {//随机人数

                Collections.shuffle(ids);
                int randomSeriesLength = quesNum;

                ids = ids.subList(0, randomSeriesLength);
            }
            ids.stream().forEach(userId -> {
                CpUserInQuestionnaireEntity cp = new CpUserInQuestionnaireEntity();
                WxUserDTO wxUserDTO = sysWeixinUserMapper.findUserOpenId(userId, appid);
                if (wxUserDTO != null) {
                    String title = "问卷调查参与提醒";
                    String theme = wxUserDTO.getRealname() + "/" + wxUserDTO.getUsername() + ",您有一条新的工作提醒";
                    StringBuilder contentBuilder = new StringBuilder()
                            .append("您有一份问卷调查需要参与，标题名称：").append(map.get("formName").toString())
                            .append("。请准时参与!");
                    String remark = "点击可查看详情";
                    String url="pagesC/questionnaire/questionnaireDetail?id="+id;
                    WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                            .setUseCommonTemplate(Boolean.TRUE)
                            .setTheme(theme)
                            .setTitle(title)
                            .setCreateDate(new Date())
                            .setContent(contentBuilder.toString())
                            .setOpenId(wxUserDTO.getOpenId())
                            .setRemark(remark)
                            .setMiniAppUrl(url);
                    wxMessageSender.wxMessageSend(wxCommonMsgInfo);
                    cp.setMessage("0");//0成功，1失败
                } else {
                    cp.setMessage("1");
                }
                SysUser user = sysUserService.getById(userId);
                cp.setUsername(userId);
                cp.setRealname(user == null? userId:user.getRealname());
                cp.setQuestionnaireId(map.get("_id").toString());
                cp.setStatus("0");
                cpUserInQuestionnaireService.save(cp);
            });
        }
    }


    public void removeItem(List<SysUser> list, String nj) {
        //获取当前学期学年
        ScWeeksEntity scWeeksEntity = scWeeksMapper.selectCurrentWeek();
        Iterator<SysUser> iterator = list.iterator();
        while (iterator.hasNext()) {
            SysUser sysUser = iterator.next();
            //CommonConstant.DEL_FLAG_1.toString()
            String type = sysUser.getType();
            if (StringUtils.isNotEmpty(nj)) {
                //当前时间没有学年学期，例如寒假暑假则无法区分大几  过滤掉
                if (scWeeksEntity == null) {
                    iterator.remove();
                    continue;
                }
                String grade = sysUser.getGrade();   //当前用户所在年级  2012级学生就是2012年进校  大一
                SimpleDateFormat format = new SimpleDateFormat("yyyy");
                Integer year = Integer.valueOf(format.format(new Date()));

                int month = Calendar.getInstance().get(Calendar.MONTH) + 1;
                SimpleDateFormat sft = new SimpleDateFormat("MM");
                Integer ksy = Integer.valueOf(sft.format(scWeeksEntity.getKsrq()));

                int currentNj;   //  5研究生   6老师
                if ("T".equals(type)) {   //该用户为老师  T老师没有grade
                    if (!nj.contains(TEACHER)) {
                        iterator.remove();
                        continue;
                    }
                } else if ("G".equals(type)) {  //该用户为研究生    G研究生有grade
                    if (!nj.contains(GRADUATE)) {
                        iterator.remove();
                        continue;
                    }
                } else if ("S".equals(type)) {   //该用户为学生
                    if (StringUtils.isNotEmpty(grade)) {
                        //2018年9月份入学--2019年9月份为大一    2019年9月份-2020年9月份为大二
                        //如果当前时间为2020年第一学期为大二-------2020-2018    如果当前时间为2020年第二学期为大三-----2020-2018+1
                        if (scWeeksEntity.getXq().equals("1")) {
                            //新的大一还未产生 2020 2019 2018 2017 2016
                            //大一班级年级为2016
                            if (month < ksy) {
                                currentNj = year - Integer.valueOf(grade);
                            } else {
                                currentNj = year - Integer.valueOf(grade) + 1;
                            }
                        } else {
                            //新的大一班级年级为当前年
                            currentNj = year - Integer.valueOf(grade);
                        }
                        if (currentNj > 4) {
                            iterator.remove();
                            continue;
                        } else {
                            if (!nj.contains(String.valueOf(currentNj))) {
                                iterator.remove();
                                continue;
                            }
                        }
                    } else {   //该学生没有年级字段  数据有误 则不让他报名
                        iterator.remove();
                        continue;
                    }
                } else {   //其他身份
                    iterator.remove();
                    continue;
                }
            }
        }
    }


    @Override
    public List<Map<String, Object>> surveyResults(String id) throws ParseException {
        List<Map<String, Object>> surveys = new ArrayList<>();
        Map<String, Object> ques = getById(id);
        if (ques.isEmpty()){
            return new ArrayList<>();
        }
        List<Map<String, Object>> map=(List<Map<String, Object>>)ques.get("components");
        for (Map m : map) {
            //先遍历所有formkey，在遍历选项
            Map<String, Object> component = (Map<String, Object>) m.get("component");
            List<Map<String, Object>> options = (List<Map<String, Object>>) component.get("options");
            Map<String, Object> sur = new HashMap<>();

            sur.put("title",component.get("title").toString());
            sur.put("formItemKey",m.get("formItemKey").toString());
            sur.put("id",id);

            if (m.get("type").toString().equals("oneRadio")
                    || m.get("type").toString().equals("oneSelectInput")) {//单选
               sur=survey(m.get("formItemKey").toString(),component.get("title").toString(),options,0);
            } else if (m.get("type").toString().equals("mutiCheckbox")) {//多选
               sur=survey(m.get("formItemKey").toString(),component.get("title").toString(),options,1);

            }
            surveys.add(sur);
        }
        return surveys;
    }

    public Map<String, Object> survey(String formItemKey,String title,List<Map<String, Object>> options,int type){
        int sum = 0;
        Map<String, Object> sur = new HashMap<>();
        List<QuestionnaireDto> dtos = new ArrayList<>();
        int count=0;
        for (Map opt : options) {
            if (type==0){//单选
                count = (int) mongoTemplate.count(new Query(Criteria.where(formItemKey).is(opt.get("value").toString())), Map.class, CP_USER_QUESTIONNAIRE_NAME);
            }else {
                count = (int) mongoTemplate.count(new Query(Criteria.where(formItemKey).in(opt.get("value").toString())), Map.class, CP_USER_QUESTIONNAIRE_NAME);
            }
            QuestionnaireDto dto = new QuestionnaireDto();
            dto.setCount(count);
            dto.setLabel(opt.get("label").toString());
            dtos.add(dto);
            sum += count;
        }
        for (QuestionnaireDto dto : dtos) {
            // 创建一个数值格式化对象
            NumberFormat numberFormat = NumberFormat.getInstance();
            // 设置精确到小数点后2位
            numberFormat.setMaximumFractionDigits(2);
            if (sum==0){
                dto.setProportion("0");
            }else {
                String pro = numberFormat.format((float) dto.getCount() / (float) sum * 100);//所占百分比
                dto.setProportion(pro);
            }
        }
        sur.put("itemKey", formItemKey);
        sur.put("title", title);
        sur.put("sum", sum);
        sur.put("ques", dtos);
        return sur;
    }

    @Override
    public Page<Map> pageList(Integer pageNo, Integer pageSize, String formItemKey, QuestionDto dto) throws ParseException {
        Page<Map> page = new Page(pageNo, pageSize);
        List <Map> all = new ArrayList<>();
        //问卷信息
        Map<String, Object> ques = getById(dto.getId());
        if (ques.isEmpty()){
            Page<Map> pageList = new Page<>(pageNo, pageSize);
            pageList.setTotal(0);
            return  pageList ;
        }
        //获取问卷的问题
        List<Map<String, Object>> map=(List<Map<String, Object>>)ques.get("components");

        map.stream().forEach(e -> {
            //当前问题
            if (formItemKey.equals(e.get("formItemKey").toString())) {
                Map<String, Object> component = (Map<String, Object>) e.get("component");
                List<Map<String, Object>> options = (List<Map<String, Object>>) component.get("options");
                List<Map> totalList = new ArrayList<>();
                List<Map> cpUsers = new ArrayList<>();
                int count = 0;
                for (Map opt : options) {
                    if (e.get("type").toString().equals("oneRadio")
                            || e.get("type").toString().equals("oneSelectInput")) {//单选
                        cpUsers =  mongoTemplate.find(new Query(Criteria.where(formItemKey).is(opt.get("value").toString())), Map.class, CP_USER_QUESTIONNAIRE_NAME);
                    } else if (e.get("type").toString().equals("mutiCheckbox")) {//多选
                        cpUsers =  mongoTemplate.find(new Query(Criteria.where(formItemKey).in(opt.get("value").toString())), Map.class, CP_USER_QUESTIONNAIRE_NAME);
                    }
                    for (Map user: cpUsers) {
                        user.put("label",opt.get("label").toString());
                    }
                    totalList.addAll(cpUsers);
                }
                List<String> oldIds = new ArrayList<>();
                totalList.stream().forEach(item -> oldIds.add(item.get("create_by").toString()));
                if (oldIds.size() > 0) {
                    List<String> ids = oldIds.stream().distinct().collect(Collectors.toList());
                    if (dto.getRealname()!= null  && !"".equals(dto.getRealname())) {

                        List<String> usernames = sysUserMapper.selectUserByrealOrId(dto.getRealname());
                        List<String> res = new ArrayList<>();
                        for (String username : usernames) {
                            if (ids.contains(username)) {
                                res.add(username);
                            } else {
                                continue;
                            }
                        }
                        ids = res;

                    }
                    for (String id : ids) {
                        Map<String, Object> map1 = new HashMap<>();
                        SysUser sysUser = sysUserMapper.selectById(id);
//                        if (dto.getRealname()!= null && !"".equals(dto.getRealname()) && !dto.getRealname().equals(sysUser.getRealname())){
//                            continue;
//                        }
                        map1.put("username", id);
                        map1.put("realname", sysUser == null?"":sysUser.getRealname());
                        StringBuffer label = new StringBuffer();
                        totalList.stream().forEach(cpUser -> {
                            if (id.equals(cpUser.get("create_by").toString())) {
                                if (label.indexOf(cpUser.get("label").toString()) == -1) {
                                    label.append(cpUser.get("label").toString() +" ");
                                }
                            }
                        });
                        map1.put("label", label.toString());
                        all.add(map1);
                    }
                }
                if (all.size() < (pageNo-1)*pageSize) {
                    List<Map> result = new ArrayList<>();
                    page.setRecords(result);
                } else if ((pageNo-1)*pageSize < all.size() && all.size() < pageNo*pageSize){
                    List<Map> result = all.subList((pageNo-1)*pageSize,all.size());
                    page.setRecords(result);
                } else if (all.size() > pageNo*pageSize) {
                    List<Map> result = all.subList((pageNo-1)*pageSize,pageNo*pageSize);
                    page.setRecords(result);
                }
                page.setTotal(all.size());
            }
        });
        return page;
    }

    @Override
    public List<QuestionDto> surveyExportXls(String formItemKey, QuestionDto dto) throws ParseException {
        List <QuestionDto> all = new ArrayList<>();
        //问卷信息
        Map<String, Object> ques = getById(dto.getId());
        //获取问卷的问题
        List<Map<String, Object>> map=(List<Map<String, Object>>)ques.get("components");
        map.stream().forEach(e -> {
            //当前问题
            if (formItemKey.equals(e.get("formItemKey").toString())) {
                Map<String, Object> component = (Map<String, Object>) e.get("component");
                List<Map<String, Object>> options = (List<Map<String, Object>>) component.get("options");
                List<Map> totalList = new ArrayList<>();
                List<Map> cpUsers = new ArrayList<>();
                int count = 0;
                for (Map opt : options) {
                    if (e.get("type").toString().equals("oneRadio")
                            || e.get("type").toString().equals("oneSelectInput")) {//单选
                        cpUsers =  mongoTemplate.find(new Query(Criteria.where(formItemKey).is(opt.get("value").toString())), Map.class, CP_USER_QUESTIONNAIRE_NAME);
                    } else if (e.get("type").toString().equals("mutiCheckbox")) {//多选
                        cpUsers =  mongoTemplate.find(new Query(Criteria.where(formItemKey).in(opt.get("value").toString())), Map.class, CP_USER_QUESTIONNAIRE_NAME);
                    }
                    for (Map user: cpUsers) {
                        user.put("label",opt.get("label").toString());
                    }
                    totalList.addAll(cpUsers);
                }
                List<String> oldIds = new ArrayList<>();
                totalList.stream().forEach(item -> oldIds.add(item.get("create_by").toString()));
                if (oldIds.size() > 0) {
                    List<String> ids = oldIds.stream().distinct().collect(Collectors.toList());
                    if (dto.getUsername()!= null  && !"".equals(dto.getUsername())) {
                        if (ids.contains(dto.getUsername())) {
                            ids = new ArrayList<>();
                            ids.add(dto.getUsername());
                        } else {
                            ids = new ArrayList<>();
                        }
                    }
                    for (String id : ids) {
                        QuestionDto questionDto = new QuestionDto();
                        SysUser sysUser = sysUserMapper.selectById(id);
                        if (dto.getRealname()!= null && !"".equals(dto.getRealname()) && !dto.getRealname().equals(sysUser.getRealname())){
                            continue;
                        }
                        questionDto.setId(id);
                        questionDto.setUsername(id);
                        questionDto.setRealname(sysUser == null?"":sysUser.getRealname());

                        StringBuffer label = new StringBuffer();
                        totalList.stream().forEach(cpUser -> {
                            if (id.equals(cpUser.get("create_by").toString())) {
                                if (label.indexOf(cpUser.get("label").toString()) == -1) {
                                    label.append(cpUser.get("label").toString() +" ");
                                }
                            }
                        });
                        questionDto.setLabel(label.toString());
                        all.add(questionDto);
                    }
                }
            }
        });
        return all;
    }

    @Override
    public Map<String,Object> surveyTextList(String questionnaireId,String formItemKey, Integer pageNo, Integer pageSize) throws ParseException {
        Map<String,Object> hashMap=new HashMap<>();
        Page<Map> page = new Page<>(pageNo, pageSize);
        Criteria cd = where("delFlag").is(0);
        cd.and("questionnaire_id").is(questionnaireId);
        if (StringUtils.isNotEmpty(formItemKey)) {
            cd.and("textFields").in(formItemKey);
        }
        Query query = new Query(cd);
        Long count=mongoTemplate.count(query,Map.class,CP_USER_QUESTIONNAIRE_NAME);
        Field findFields = query.fields();
        findFields.include(formItemKey);
        query.with(new Sort(Sort.Direction.DESC, "create_time"));
        query.skip((pageNo - 1) * pageSize).limit(pageSize);
        List<Map> results = mongoTemplate.find(query, Map.class, CP_USER_QUESTIONNAIRE_NAME);
        page.setTotal(count);
        page.setRecords(results);
        hashMap.put("page",page);
        Map<String, Object> ques = getById(questionnaireId);
        if (ques.isEmpty()){
            return new HashMap<>();
        }
        List<Map<String, Object>> map=(List<Map<String, Object>>)ques.get("components");
        for (Map m : map) {
            if (m.get("formItemKey").toString().equals(formItemKey)){
                Map<String, Object> component = (Map<String, Object>) m.get("component");
                hashMap.put("component",component);
            }
        }
        return hashMap;
    }




    @Override
    public Page<Map> queryUserList(String type,LoginUser sysUser,Integer pageNo, Integer pageSize) throws ParseException {
        //type 0：未参与 1：已参与
        Page<Map> page = new Page<>(pageNo, pageSize);
        Criteria cd = where("delFlag").is(0);
        if (type.equals("0")){
            List<String> quesId=noUserQuesId(sysUser.getId());
            cd.and("_id").in(quesId);
        }else {
            List<String> quesId=queryByUserQuesId(sysUser.getId());
            cd.and("_id").in(quesId);
        }
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Query query = new Query(cd);
        Long count=mongoTemplate.count(query,Map.class,CP_QUESTIONNAIRE_NAME);
        query.with(new Sort(Sort.Direction.DESC, "create_time"));
        query.skip((pageNo - 1) * pageSize).limit(pageSize);
        List<Map> results = mongoTemplate.find(query, Map.class, CP_QUESTIONNAIRE_NAME);
        if (results != null && !CollectionUtils.isEmpty(results)) {
            Date date = new Date();
            for (Map<String, Object> map : results) {
                map.put("id", String.valueOf(map.get("_id")));
                if (map.get("status").toString().equals("1")) {
                    Date st = format.parse(map.get("st").toString());
                    Date et = format.parse(map.get("et").toString());
                    if (date.compareTo(st) == -1) {
                        map.put("status", 2);
                    } else if (date.compareTo(st) == 1 && date.compareTo(et) == -1) {
                        map.put("status", 3);
                    } else {
                        map.put("status", 4);
                    }
                }
            }
        }
        page.setTotal(count);
        page.setRecords(results);
        return page;
    }


    public List<String> queryByUserQuesId(String userId){
        Criteria cd = where("delFlag").is(0);
        cd.and("create_by").is(userId);
        Aggregation aggregation1 = Aggregation.newAggregation(Aggregation.match(cd),
                Aggregation.group("questionnaire_id"));
        List<Map> outputTypeCount = mongoTemplate.aggregate(aggregation1, CP_USER_QUESTIONNAIRE_NAME,
                Map.class).getMappedResults();
        if (outputTypeCount.isEmpty()){
            return new ArrayList<>();
        }
        List<String>list=new ArrayList<>();
        for (Map link : outputTypeCount) {
           list.add(link.get("_id").toString());
        }
        return list;
    }



    public List<String> noUserQuesId(String userId){
        List<String> quesId=cpUserInQuestionnaireService.queryByUser(userId);
        if (quesId.isEmpty()) return new ArrayList<>();
        List<String> apply=queryByUserQuesId(userId);
        if (apply.isEmpty()) return quesId;
        quesId.removeAll(apply);
        return quesId;
    }

}



