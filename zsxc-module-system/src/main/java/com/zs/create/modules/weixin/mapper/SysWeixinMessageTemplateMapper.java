package com.zs.create.modules.weixin.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Param;
import com.zs.create.modules.weixin.entity.SysWeixinMessageTemplateEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

/**
 * @Description 微信管理Mapper层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-04-22 11:45:34
 * @Version: V1.0
 */
public interface SysWeixinMessageTemplateMapper extends BaseMapper<SysWeixinMessageTemplateEntity> {

    SysWeixinMessageTemplateEntity queryWeixinMessageTemplateByTemplateId(@Param("templateId") String templateId, @Param("status") String status);
}
