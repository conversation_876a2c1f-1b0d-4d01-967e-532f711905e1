package com.zs.create.modules.communication.association.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.communication.association.entity.CpUserInAssociationEntity;

import java.util.List;
import java.util.Map;

/**
 * @Description 社团评审人员Service层
 *
 * <AUTHOR>
 * @email null
 * @date 2020-11-26 11:15:09
 * @Version: V1.0
 */
public interface CpUserInAssociationService extends IService<CpUserInAssociationEntity> {


    Page<CpUserInAssociationEntity> pageList(CpUserInAssociationEntity entity, Integer pageNo, Integer pageSize);

    List<String> queryByUser(String userId);

    List<CpUserInAssociationEntity> queryDeptByUser(Page<CpUserInAssociationEntity> page,String userId,String id);

    CpUserInAssociationEntity  getStatusById(String deptId,String associationId );
}

