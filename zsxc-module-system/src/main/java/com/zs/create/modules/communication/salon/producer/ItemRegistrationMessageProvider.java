package com.zs.create.modules.communication.salon.producer;

import com.zs.create.config.RabbitmqConfig;
import com.zs.create.modules.communication.salon.utils.ItemRegistrationQueue;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/4/22
 */
@Component
@Slf4j
@AllArgsConstructor
public class ItemRegistrationMessageProvider {

    private final ItemRegistrationQueue itemRegistrationQueue;
    private final RabbitTemplate rabbitTemplate;

    /**
     * 发送消息
     *
     * @param message
     */
    public void sendItemRegistrationMessage(String message) {
        if (message != null) {
            rabbitTemplate.convertAndSend(RabbitmqConfig.EXCHANGE_FANOUT_ITEM_REGISTRATION, RabbitmqConfig.ROUTINGKEY_ITEM_REGISTRATION, message);
        }

//        try {
//            if (message != null) {
//                String seqId = SnowIdUtils.uniqueLongHex();
//                DelayQueueMessage delayQueueMessage = new DelayQueueMessage();
//                //时间戳默认为毫秒 延迟5s即为 5*1000
//                long time = System.currentTimeMillis();
//                LocalDateTime dateTime = Instant.ofEpochMilli(time).atZone(ZoneOffset.ofHours(8)).toLocalDateTime();
//                delayQueueMessage.setId(seqId);
//                delayQueueMessage.setDelayTime(time);
//                delayQueueMessage.setCreateTime(dateTime);
//                delayQueueMessage.setBody(message);
//                itemRegistrationQueue.push(delayQueueMessage);
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
    }
}
