package com.zs.create.modules.paramdesign.service.impl;

import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.paramdesign.entity.ScLabelEntity;
import com.zs.create.modules.paramdesign.mapper.ScLabelMapper;
import com.zs.create.modules.paramdesign.service.ScLabelService;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * @Description 兴趣标签Service实现层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-06-24 10:33:56
 * @Version: V1.0
 */
@Service
public class ScLabelServiceImpl extends ServiceImpl<ScLabelMapper, ScLabelEntity> implements ScLabelService {
}
