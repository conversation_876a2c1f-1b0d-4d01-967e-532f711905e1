package com.zs.create.modules.item.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.entity.ScItemEvaluationEntity;
import com.zs.create.modules.mobile.sc.dto.ItemQueryDTO;

/**
 * @Description 项目-评价Service层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-09 10:06:50
 * @Version: V1.0
 */
public interface ScItemEvaluationService extends IService<ScItemEvaluationEntity> {
    Result<?> isEvaluation(String itemId , Integer type);

    double selectAvgEvaluation(String libraryId);

    Boolean saveEvaluation(ScItemEvaluationEntity scItemEvaluation);

    void canEaluationCheck(ScItemEvaluationEntity scItemEvaluation) ;


    IPage<ScItemEntity> pageEvaluationItems(Page<ScItemEntity> page, ItemQueryDTO itemQueryDTO);
}

