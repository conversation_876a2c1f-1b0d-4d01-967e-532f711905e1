package com.zs.create.modules.bigScreen.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.bigScreen.entity.SysBigScreenBasicsEntity;
import com.zs.create.modules.bigScreen.service.SysBigScreenBasicsService;
import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import com.zs.create.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
/**
 * @Description 基础Controller层
 *
 * <AUTHOR> @email 
 * @date 2022-10-10 14:20:32
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "基础数据")
@RestController
@RequestMapping("/bigScreen/sysBigScreenBasics")
public class SysBigScreenBasicsController {
    @Autowired
    private SysBigScreenBasicsService sysBigScreenBasicsService;

    @RequestMapping(value = "/add", method = RequestMethod.GET)
    @ApiOperation("添加基础")
    public void add(){
        sysBigScreenBasicsService.add();
    }

    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @ApiOperation("查询")
    public SysBigScreenBasicsEntity list(){
        LambdaQueryWrapper<SysBigScreenBasicsEntity> sysBigScreenBasicsEntityLqw=new LambdaQueryWrapper<>();
        sysBigScreenBasicsEntityLqw.orderByDesc(SysBigScreenBasicsEntity::getCreateTime).last("limit 1");
        return sysBigScreenBasicsService.getOne(sysBigScreenBasicsEntityLqw);
    }


}
