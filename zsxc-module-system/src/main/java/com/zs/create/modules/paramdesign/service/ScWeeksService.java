package com.zs.create.modules.paramdesign.service;

import com.zs.create.modules.paramdesign.entity.ScWeeksEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * @Description 学期学年配置Service层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-04-07 16:49:25
 * @Version: V1.0
 */
public interface ScWeeksService extends IService<ScWeeksEntity> {
    ScWeeksEntity selectCurrentWeek();

    List<ScWeeksEntity> getAllWeek();

    ScWeeksEntity queryByDate();

    ScWeeksEntity getCurrentWeek();
    ScWeeksEntity getWeekMessage( Date date);

    ScWeeksEntity getNowWeek();

    List<ScWeeksEntity> getScWeeksByNow();

    List<String> getXnList();

    String getXnNow();
}

