package com.zs.create.modules.lib.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zs.create.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 培养方案模块表
 * 
 * <AUTHOR>
 * @email null
 * @date 2020-07-13 10:44:47
 */
@Data
@TableName("sc_programme_module")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_programme_module对象", description="培养方案模块表")
public class ScProgrammeModuleEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId
	private String id;
	/**
	 * 归属模块
	 */
	@Dict(dicCode = "item_module")
	private String module;
	/**
	 * 内容
	 */
	private String content;
	/**
	 * 培养方案id
	 */
	private String programmeId;
	/**
	 * 项目库id
	 */
	private String libraryId;
	/**
	 * 创建人id
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 删除标记
	 */
	private Integer delFlag;

	@TableField(exist = false)
	private String librayName="";

	/**
	 * 项目被收藏数量
	 */
	@TableField(exist = false)
	private String moduleName;

	/**
	 * 模块对应精品项目库
	 */
	@TableField(exist = false)
	private List<ScItemLibraryEntity> scItemLibraryList;
}
