<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.communication.suggest.mapper.CpSuggestHistoryMapper">


    <insert id="insertHistory">
        insert into cp_suggest_history (id,suggest_id,user_id,handler,link_name) values (#{reply.id},#{reply.suggestId},#{reply.userId},#{reply.handler},#{reply.linkName})
    </insert>
    <select id="pageList"
            resultType="com.zs.create.modules.communication.suggest.entity.CpSuggestHistoryEntity">
        select id,suggest_id,user_id,handler,result,create_time,content,link_name
        from cp_suggest_history
        WHERE suggest_id =#{itemId}
        order by create_time is null,create_time
     </select>
</mapper>