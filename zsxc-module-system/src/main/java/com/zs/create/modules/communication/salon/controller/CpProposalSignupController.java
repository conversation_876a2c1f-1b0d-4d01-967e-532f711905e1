package com.zs.create.modules.communication.salon.controller;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import javax.validation.constraints.NotBlank;

import com.alibaba.fastjson.JSON;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.communication.salon.entity.CpProposalSignupEntity;
import com.zs.create.modules.communication.salon.entity.CpSalonProposalEntity;
import com.zs.create.modules.communication.salon.service.CpProposalSignupService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.communication.salon.service.CpSalonProposalService;
import com.zs.create.modules.system.entity.SysDict;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.hibernate.validator.constraints.Length;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.servlet.ModelAndView;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.List;


/**
 * @Description 话题报名Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-07 14:55:49
 * @Version: V1.0
 */
@Slf4j
@Api(tags="话题报名")
@RestController
@RequestMapping("/salon/cpProposalSignup")
public class CpProposalSignupController {
    @Autowired
    private CpProposalSignupService cpProposalSignupService;
    @Autowired
    private CpSalonProposalService cpSalonProposalService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "话题报名-分页列表查询")
    @ApiOperation(value="话题报名-分页列表查询", notes="话题报名-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<CpProposalSignupEntity>> queryPageList(CpProposalSignupEntity cpProposalSignup,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        Result<IPage<CpProposalSignupEntity>> result = new Result<>();
        QueryWrapper<CpProposalSignupEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.orderByDesc("create_time");
        if (null!=cpProposalSignup.getUserXuehao()) queryWrapper.like("user_xuehao",cpProposalSignup.getUserXuehao());
        if (null!=cpProposalSignup.getUserName()) queryWrapper.like("user_name",cpProposalSignup.getUserName());
        if (null!=cpProposalSignup.getProposalId()) queryWrapper.eq("proposal_id",cpProposalSignup.getProposalId());
        Page<CpProposalSignupEntity> page = new Page<>(pageNo, pageSize);
        IPage<CpProposalSignupEntity> pageList = cpProposalSignupService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 话题报名
     */
    @AutoLog(value = "话题报名")
    @ApiOperation(value="话题报名", notes="话题报名")
    @PostMapping(value = "/enter")
    public Result<?> enter(@RequestBody @Valid CpProposalSignupEntity cpProposalSignup) {
        return cpProposalSignupService.enter(cpProposalSignup);
    }

    /**
     * 话题取消报名
     */
    @AutoLog(value = "话题取消报名")
    @ApiOperation(value="话题取消报名", notes="话题取消报名")
    @PostMapping(value = "/cancelEnter/{proposalId}")
    public Result<?> cancelEnter(@PathVariable String proposalId) {
        return cpProposalSignupService.cancelEnter(proposalId);
    }

    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(CpProposalSignupEntity cpProposalSignupEntity,HttpServletRequest request) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // Step.1 组装查询条件
        QueryWrapper<CpProposalSignupEntity> queryWrapper = QueryGenerator.initQueryWrapper(cpProposalSignupEntity, request.getParameterMap());
        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<CpProposalSignupEntity> pageList = cpProposalSignupService.list(queryWrapper);
        //导出文件名称
        String proposalId = cpProposalSignupEntity.getProposalId();
        CpSalonProposalEntity cpSalonProposalEntity = cpSalonProposalService.getById(proposalId);
        String title="";
        if (cpSalonProposalEntity!=null){
            title = cpSalonProposalEntity.getTitle();
        }
        mv.addObject(NormalExcelConstants.FILE_NAME, title+"话题报名列表");
        mv.addObject(NormalExcelConstants.CLASS, CpProposalSignupEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams(title+"话题报名列表数据", "导出人:"+sysUser.getRealname(), "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }
}
