package com.zs.create.modules.system.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.backbone.utils.UnsensitiveUtils;
import com.zs.create.modules.system.entity.ScProjectLeaderEntity;
import com.zs.create.modules.system.service.ScProjectLeaderService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static cn.hutool.core.util.StrUtil.replace;


/**
 * @Description 项目负责人Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-02 14:30:27
 * @Version: V1.0
 */
@Slf4j
@Api(tags="项目负责人")
@RestController
@RequestMapping("/item/scProjectLeader")
public class ScProjectLeaderController {
    @Autowired
    private ScProjectLeaderService scProjectLeaderService;
    @Autowired
    private UnsensitiveUtils unsensitiveUtils;

    @AutoLog(value = "项目负责人-判断当前用户是否是项目负责人")
    @ApiOperation(value="项目负责人-判断当前用户是否是项目负责人", notes="项目负责人-判断当前用户是否是项目负责人")
    @GetMapping(value = "/judgeCurrentUserIsProjectLeader")
    public Result<?> judgeCurrentUserIsProjectLeader(){
        Boolean isProjectLeader = scProjectLeaderService.judgeCurrentUserIsProjectLeader();
        return Result.ok(isProjectLeader);
    }
    /**
     * 分页列表查询
     */
    @AutoLog(value = "项目负责人-分页列表查询")
    @ApiOperation(value="项目负责人-分页列表查询", notes="项目负责人-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ScProjectLeaderEntity>> queryPageList(ScProjectLeaderEntity scProjectLeader,
                                                              @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                              @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                              HttpServletRequest req) {
        Result<IPage<ScProjectLeaderEntity>> result = new Result<>();
//        QueryWrapper<ScProjectLeaderEntity> queryWrapper = QueryGenerator.initQueryWrapper(scProjectLeader, req.getParameterMap());
        QueryWrapper<ScProjectLeaderEntity> queryWrapper = new QueryWrapper<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        queryWrapper.eq("create_by",sysUser.getId());
        if (scProjectLeader.getName()!=null && !scProjectLeader.getName().equals("")){
            queryWrapper.and(i->i.like("name", scProjectLeader.getName()).or().like("user_code", scProjectLeader.getName()));
        }
        if (scProjectLeader.getUserCode()!=null && !scProjectLeader.getUserCode().equals("")){
            queryWrapper.like("user_code", scProjectLeader.getUserCode());
        }
        Page<ScProjectLeaderEntity> page = new Page<ScProjectLeaderEntity>(pageNo, pageSize);
        IPage<ScProjectLeaderEntity> pageList = scProjectLeaderService.page(page, queryWrapper);
        boolean contains = unsensitiveUtils.isUnsensitive(sysUser.getUsername());
        if(pageList.getTotal()>0) {
            if (!contains) {
                for (ScProjectLeaderEntity scProjectLeaderEntity : pageList.getRecords()) {
                    String phone = scProjectLeaderEntity.getTel();
                    String email = scProjectLeaderEntity.getEmail();
                    if (oConvertUtils.isNotEmpty(phone)) {
                        scProjectLeaderEntity.setTel(replace(phone, 3, 7, '*'));
                    }
                    if (oConvertUtils.isNotEmpty(email)) {
                        scProjectLeaderEntity.setEmail(replace(email, 1, email.length() - 3, '*'));
                    }
                }
            }
        }
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "项目负责人-添加")
    @ApiOperation(value="项目负责人-添加", notes="项目负责人-添加")
    @PostMapping(value = "/add/{userId}")
    public Result<ScProjectLeaderEntity> add(@PathVariable String userId) {
            Result<ScProjectLeaderEntity> result = new Result<ScProjectLeaderEntity>();
            scProjectLeaderService.saveProjectLeader(userId);
            result.success("添加成功！");
        return result;
    }


    /**
     * 通过id删除
     */
    @AutoLog(value = "项目负责人-通过id删除")
    @ApiOperation(value="项目负责人-通过id删除", notes="项目负责人-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
            scProjectLeaderService.removeById(id);
		    return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "项目负责人-批量删除")
    @ApiOperation(value="项目负责人-批量删除", notes="项目负责人-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<ScProjectLeaderEntity> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<ScProjectLeaderEntity> result = new Result<ScProjectLeaderEntity>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            List<String> list = Arrays.asList(ids.split(","));
            this.scProjectLeaderService.delByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "项目负责人-通过id查询")
    @ApiOperation(value="项目负责人-通过id查询", notes="项目负责人-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScProjectLeaderEntity> queryById(@RequestParam(name="id",required=true) String id) {
        Result<ScProjectLeaderEntity> result = new Result<ScProjectLeaderEntity>();
        ScProjectLeaderEntity scProjectLeader = scProjectLeaderService.getById(id);
        if(scProjectLeader==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(scProjectLeader);
            result.setSuccess(true);
        }
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<ScProjectLeaderEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                ScProjectLeaderEntity scProjectLeader = JSON.parseObject(deString, ScProjectLeaderEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(scProjectLeader, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<ScProjectLeaderEntity> pageList = scProjectLeaderService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "项目负责人列表");
        mv.addObject(NormalExcelConstants.CLASS, ScProjectLeaderEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("项目负责人列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ScProjectLeaderEntity> listScProjectLeaders = ExcelImportUtil.importExcel(file.getInputStream(), ScProjectLeaderEntity.class, params);
                scProjectLeaderService.saveBatch(listScProjectLeaders);
                return Result.ok("文件导入成功！数据行数:" + listScProjectLeaders.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
