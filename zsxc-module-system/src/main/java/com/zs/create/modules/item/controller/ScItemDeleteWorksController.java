package com.zs.create.modules.item. controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.entity.ScItemDeleteWorksEntity;
import com.zs.create.modules.item.service.ScItemDeleteWorksService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
/**
 * @Description 项目作品删除记录Controller层
 *
 * <AUTHOR> @email 
 * @date 2024-08-07 10:58:55
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "项目作品删除记录")
@RestController
@RequestMapping("/item/scItemDeleteWorks")
public class ScItemDeleteWorksController {
    @Autowired
    private ScItemDeleteWorksService scItemDeleteWorksService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "项目作品删除记录-分页列表查询")
    @ApiOperation(value = "项目作品删除记录-分页列表查询", notes = "项目作品删除记录-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ScItemDeleteWorksEntity>> queryPageList(ScItemDeleteWorksEntity scItemDeleteWorks,
                                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                       HttpServletRequest req) {
        Result<IPage<ScItemDeleteWorksEntity>> result = new Result<IPage<ScItemDeleteWorksEntity>>();

        //自行构造查询参数
        Page<ScItemDeleteWorksEntity> page = new Page<ScItemDeleteWorksEntity>(pageNo, pageSize);
        IPage<ScItemDeleteWorksEntity> pageList = scItemDeleteWorksService.pageList(page, scItemDeleteWorks);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
      * 编辑
      */
    @AutoLog(value = "项目作品删除记录-编辑")
    @ApiOperation(value = "项目作品删除记录-编辑", notes = "项目作品删除记录-编辑")
    @PostMapping(value = "/edit")
    public Result<ScItemDeleteWorksEntity> edit(@RequestBody ScItemDeleteWorksEntity scItemDeleteWorks) {
        Result<ScItemDeleteWorksEntity> result = new Result<ScItemDeleteWorksEntity>();
            ScItemDeleteWorksEntity scItemDeleteWorksEntity = scItemDeleteWorksService.getById(scItemDeleteWorks.getId());
        if (scItemDeleteWorksEntity == null) {
            return result.error500("未找到对应实体");
        } else {
            boolean ok = scItemDeleteWorksService.updateById(scItemDeleteWorks);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "项目作品删除记录-通过id删除")
    @ApiOperation(value = "项目作品删除记录-通过id删除", notes = "项目作品删除记录-通过id删除")
    @PostMapping(value = "/delete/{id}")
    public Result<?> delete(@PathVariable(name = "id") String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.error("请选择要删除的数据!");
        }
            scItemDeleteWorksService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "项目作品删除记录-批量删除")
    @ApiOperation(value = "项目作品删除记录-批量删除", notes = "项目作品删除记录-批量删除")
    @PostMapping(value = "/deleteBatch/{ids}")
    public Result<ScItemDeleteWorksEntity> deleteBatch(@PathVariable(name = "ids") String ids) {
        Result<ScItemDeleteWorksEntity> result = new Result<ScItemDeleteWorksEntity>();
        if (ids == null || StringUtils.isEmpty(ids)) {
            result.error500("参数不识别！");
        } else {
            this.scItemDeleteWorksService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "项目作品删除记录-通过id查询")
    @ApiOperation(value = "项目作品删除记录-通过id查询", notes = "项目作品删除记录-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScItemDeleteWorksEntity> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<ScItemDeleteWorksEntity> result = new Result<ScItemDeleteWorksEntity>();
            ScItemDeleteWorksEntity scItemDeleteWorks = scItemDeleteWorksService.getById(id);
        if (scItemDeleteWorks==null){
            result.error500("未找到对应实体");
        }else{
            result.setResult(scItemDeleteWorks);
            result.setSuccess(true);
        }
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // Step.1 组装查询条件
        QueryWrapper<ScItemDeleteWorksEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                    ScItemDeleteWorksEntity scItemDeleteWorks = JSON.parseObject(deString, ScItemDeleteWorksEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(scItemDeleteWorks, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<ScItemDeleteWorksEntity> pageList = scItemDeleteWorksService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "项目作品删除记录列表");
        mv.addObject(NormalExcelConstants.CLASS, ScItemDeleteWorksEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("项目作品删除记录列表数据", "导出人:"+ sysUser.getRealname(), "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ScItemDeleteWorksEntity> listScItemDeleteWorkss = ExcelImportUtil.importExcel(file.getInputStream(), ScItemDeleteWorksEntity.class, params);
                    scItemDeleteWorksService.saveBatch(listScItemDeleteWorkss);
                return Result.ok("文件导入成功！数据行数:" + listScItemDeleteWorkss.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
