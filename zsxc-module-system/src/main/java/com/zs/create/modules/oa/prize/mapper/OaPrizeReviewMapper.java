package com.zs.create.modules.oa.prize.mapper;


import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.oa.prize.entity.OaDeclareUserEntity;
import com.zs.create.modules.oa.prize.entity.OaPrizeReviewEntity;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * @Description 奖项评审Mapper层
 *
 * <AUTHOR>
 * @email null
 * @date 2021-01-19 11:01:56
 * @Version: V1.0
 */
public interface OaPrizeReviewMapper extends BaseMapper<OaPrizeReviewEntity> {

    List<Map<String,Object>> getNowTaskName(@Param("taskKey") String taskKey);

    IPage<OaPrizeReviewEntity> pageList(@Param("page")Page<OaPrizeReviewEntity> page, @Param("oaPrizeReview") OaPrizeReviewEntity oaPrizeReview);

    List<OaPrizeReviewEntity> queryByEt();

    List<OaDeclareUserEntity> queryByStatusAndSt();

    List<OaDeclareUserEntity> queryByStatusAndEt();
}
