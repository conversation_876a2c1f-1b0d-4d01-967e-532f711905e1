package com.zs.create.modules.item.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.base.enums.ScEnum;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.DateUtils;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.entity.*;
import com.zs.create.modules.item.mapper.ScItemDevolutionMapper;
import com.zs.create.modules.item.mapper.ScItemMapper;
import com.zs.create.modules.item.mapper.ScItemRegistrationMapper;
import com.zs.create.modules.item.mapper.ScItemSignMapper;
import com.zs.create.modules.item.service.ScItemSignService;
import com.zs.create.modules.item.utils.QRCodeTool;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.mapper.SysUserMapper;
import com.zs.create.modules.system.service.ISysDictService;
import com.zs.create.modules.system.util.HttpClientUtil;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.mapper.SysWeixinUserMapper;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


@Service
@Slf4j
public class ScItemSignServiceImpl extends ServiceImpl<ScItemSignMapper, ScItemSignEntity> implements ScItemSignService {
    private static final Integer width = 400;
    private static final Integer height = 400;
    @Autowired
    ScItemMapper scItemMapper;
    @Autowired
    ScItemSignMapper scItemSignMapper;
    @Autowired
    ScItemRegistrationMapper scItemRegistrationMapper;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    SysWeixinUserMapper sysWeixinUserMapper;
    @Autowired
    WxMessageSender wxMessageSender;
    @Autowired
    SysWeixinUserService sysWeixinUserService;
    @Autowired
    SysUserMapper sysUserMapper;
    @Value("${gzhAppid}")
    private String appid;
    @Value("${gzhSecret}")
    private String secret;
    @Value("${qrUrl}")
    private String qrUrl;
    @Autowired
    private ISysDictService dictService;

    @Autowired
    private ScItemDevolutionMapper scItemDevolutionMapper;

    /**
     * 获取签到/签退基本信息
     *
     * @param itemId
     * @param type
     * @return
     */
    public ItemSignDTO getSignBasicInfo(String itemId, Integer type) {
        LoginUser user = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        if (null == scItemEntity) throw new ZsxcBootException("参数错误：签到签退未查询到项目信息");
        ItemSignDTO dto = this.buildSignInfo(scItemEntity, type);
        //0：删除权限，1：正常显示，2：未到时间

        //创建人判断
        QueryWrapper<ScItemEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("id",itemId).eq("create_by", user.getId());
        List<ScItemEntity> createUser = scItemMapper.selectList(wrapper);
        if (createUser.size()>0){
            if (this.isShow(scItemEntity, type)){
                dto.setFlag("1");
            }else {
                dto.setFlag("2");
            }
        }else {
            //被托管判断
            LambdaQueryWrapper<ScItemDevolutionEntity> lqw=new LambdaQueryWrapper<>();
            lqw.eq(ScItemDevolutionEntity::getItemId,itemId).eq(ScItemDevolutionEntity::getUserId,user.getId());
            List<ScItemDevolutionEntity> list = scItemDevolutionMapper.selectList(lqw);
            if (list.size()==0){
                dto.setFlag("0");
            }else {
                if (this.isShow(scItemEntity, type)){
                    dto.setFlag("1");
                }else {
                    dto.setFlag("2");
                }

            }
        }
        return dto;
    }

    public Boolean isShow(ScItemEntity itemEntity, Integer type) {
        Date now=new Date();
        long stTime = 45*60*1000;//45分钟
        long etTime = 15*60*1000;//15分钟
        if (DelFlagEnum.NO_DEL.getCode()== type) {
            //签到
            Date stbef=new Date(itemEntity.getSt().getTime() -stTime);//45分钟前的时间
            Date staft=new Date(itemEntity.getSt().getTime() +etTime);//15分钟后的时间
            if (now.compareTo(stbef)==1 && now.compareTo(staft)==-1){
                return Boolean.TRUE;
            }
        } else if (DelFlagEnum.DEL.getCode() == type) {
            //签退
            Date etbef=new Date(itemEntity.getEt().getTime() -etTime);//15分钟前的时间
            Date etaft=new Date(itemEntity.getEt().getTime() +stTime);//45分钟后的时间
            if (now.compareTo(etbef)==1 && now.compareTo(etaft)==-1){
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 获取静态 签到/签退二维码
     *
     * @param itemId
     * @param type
     * @return
     */
    public Map<String, Object> getStaticQrCode(String itemId, Integer type) throws UnsupportedEncodingException {
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        if (null == scItemEntity) throw new ZsxcBootException("参数错误：签到签退未查询到项目信息");

        String qrContent = this.getQrUrl(itemId, type, null, ScItemSignEntity.QRCODE_TYPE_STATIC);
        String imageForBase64 = null;
        try {
            imageForBase64 = QRCodeTool.createImageForBase64(qrContent, width, height);
        } catch (Exception e) {
            log.error("签到/签退二维码生成失败");
        }
        Map<String, Object> resMap = new HashMap<>();
        resMap.put("closed", getQrCodeStatus(scItemEntity.getId(), type));
        resMap.put("qrCode", imageForBase64);

        return resMap;
    }

    /**
     * 获取动态二维码
     *
     * @param itemId
     * @param type
     * @return
     * @throws UnsupportedEncodingException
     */
    @Override
    public String getDynamicQrCode(String itemId, Integer type) throws UnsupportedEncodingException {
        String imageForBase64 = null;
        String signTag = SnowIdUtils.uniqueLongHex();
        String qrContent = this.getQrUrl(itemId, type, signTag, ScItemSignEntity.QRCODE_TYPE_DYNAMIC);
        try {
            imageForBase64 = QRCodeTool.createImageForBase64(qrContent, width, height);
            String dynamicQrCodeKey = ScItemSignEntity.QR_DYNAMIC_SUBFIX + signTag;
            String s = dictService.queryDictTextByKey(ScEnum.SIGN_IN_OUT_TIME.getCode(), "signTime");
            Integer signTime = Integer.valueOf(s);
            redisTemplate.opsForValue().set(dynamicQrCodeKey, itemId, signTime + 2, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("签到/签退二维码生成失败");
        }
        return imageForBase64;
    }

    /**
     * 获取微信unionId
     *
     * @param code
     * @return
     */
    @Override
    public String getUnionId(String code) {
        String url = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=APPID&secret=SECRET&code=CODE&grant_type=authorization_code";
        String path = url.replace("APPID", appid).replace("SECRET", secret).replace("CODE", code);
        String s = HttpClientUtil.doPost(path);
        Map<String, String> hash = (Map<String, String>) JSON.parse(s);
        String access_token = hash.get("access_token");
        String openid = hash.get("openid");

        String userInfoApi = "https://api.weixin.qq.com/sns/userinfo?access_token=" + access_token + "&openid=" + openid + "&lang=zh_CN";
        String user = HttpClientUtil.doGet(userInfoApi);
        Map<String, String> userMap = (Map<String, String>) JSON.parse(user);
        return userMap.get("unionid");
    }

    /**
     * 检查签到有效时间 签到在项目举办开始前后 半个小时  签退在项目举办结束 前后半个小时
     *
     * @param scItemEntity
     * @param type
     */
    private void checkSignEffectiveTime(ScItemEntity scItemEntity, Integer type) {
        if (ScItemSignEntity.SIGN_IN_TYPE.equals(type)) {
            LocalDateTime itemSt = DateUtils.dateToLocalDateTime(scItemEntity.getSt());
            LocalDateTime signInSt = itemSt.minusMinutes(30);
            LocalDateTime signInEt = itemSt.plusMinutes(30);
            if (!DateUtils.judgeBetweenTimeSection(signInSt, signInEt, LocalDateTime.now())) {
                throw new ZsxcBootException("签到时间未到,不得签到");
            }
        }
        if (ScItemSignEntity.SIGN_OFF_TYPE.equals(type)) {
            LocalDateTime itemEt = DateUtils.dateToLocalDateTime(scItemEntity.getEt());
            LocalDateTime signOffSt = itemEt.minusMinutes(30);
            LocalDateTime signOffEt = itemEt.plusMinutes(30);
            if (!DateUtils.judgeBetweenTimeSection(signOffSt, signOffEt, LocalDateTime.now())) {
                throw new ZsxcBootException("签退时间未到,不得签退");
            }
        }
    }

    /**
     * 签到/签退
     *
     * @param itemId
     * @param type
     * @param signTag
     * @param qrCodeType
     * @param code
     * @return
     */
    @Override
    @Transactional
    public Boolean sign(String itemId, Integer type, String signTag, String qrCodeType, String code) {
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        if (null == scItemEntity) throw new ZsxcBootException("参数错误");
        //静态二维码
        if (ScItemSignEntity.QRCODE_TYPE_STATIC.equals(qrCodeType)) {
            String qrCodeStatus = this.getQrCodeStatus(scItemEntity.getId(), type);
            if (ScItemSignEntity.QR_CLOSE.equals(qrCodeStatus))
                throw new ZsxcBootException("静态二维码已经关闭,请联系负责老师");
        }
        //动态二维码
        if (ScItemSignEntity.QRCODE_TYPE_DYNAMIC.equals(qrCodeType)) {
            //判断动态二维码是否失效
            String dynamicQrCodeKey = ScItemSignEntity.QR_DYNAMIC_SUBFIX + signTag;
            if (null == redisTemplate.opsForValue().get(dynamicQrCodeKey))
                throw new ZsxcBootException("动态二维码已经失效,请重新扫码");
        }
        String unionId = this.getUnionId(code);
        if (StringUtils.isBlank(unionId)) throw new ZsxcBootException("系统内部错误,请联系管理员：未获取微信unionId");
        SysUser user;
        try {
            user = sysUserMapper.getUserByUnionId(unionId);
        } catch (Exception e) {
            throw new ZsxcBootException("系统内部错误,请联系管理员：根据unionId查询用户失败");
        }
        if (null == user) throw new ZsxcBootException("扫码失败，请先登录微信小程序或重新关注公众号再试");
        QueryWrapper<ScItemRegistrationEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", user.getId())
                .eq("item_id", itemId)
                .eq("del_flag", DelFlagEnum.NO_DEL.getCode());
        Integer cnt = scItemRegistrationMapper.selectCount(queryWrapper);
        if (cnt <= 0) throw new ZsxcBootException("未报名，不得扫码");

        QueryWrapper<ScItemSignEntity> scItemSignQuery = new QueryWrapper<>();
        scItemSignQuery.eq("item_id",itemId);
        scItemSignQuery.eq("user_code",user.getUsername());
        scItemSignQuery.eq("type",type);
        ScItemSignEntity one = this.getOne(scItemSignQuery);
        if (Objects.nonNull(one)){throw new ZsxcBootException("已签到/签退，请勿重复扫码");}

        ScItemSignEntity scItemSignEntity = new ScItemSignEntity()
                .setItemId(itemId).setId(SnowIdUtils.uniqueLongHex())
                .setCreateBy(user.getId()).setCreateTime(new Date())
                .setUpdateBy(user.getId()).setType(type).setUnionId(unionId)
                .setUserCode(user.getUsername()).setUserName(user.getRealname());
        boolean signSave = this.save(scItemSignEntity);
        //开始处理item表的参与人次回显
        List<ScItemSignEntity> tuiList = this.list(new QueryWrapper<ScItemSignEntity>().eq("item_id", scItemEntity.getId()).eq("user_code", user.getUsername()).eq("type", 1));
        List<ScItemSignEntity> daoList = this.list(new QueryWrapper<ScItemSignEntity>().eq("item_id", scItemEntity.getId()).eq("user_code", user.getUsername()).eq("type", 0));
        if (tuiList.size() == 1 && daoList.size() == 1) {
            ScItemEntity entityForPersons = new ScItemEntity();
            entityForPersons.setId(scItemEntity.getId());
            entityForPersons.setSumPersons(scItemEntity.getSumPersons() + 1);
            scItemMapper.updateById(entityForPersons);
        }
        if (signSave) {
            try {
                //签退发送消息提醒
                if (type == 1) {
                    if (oConvertUtils.isEmpty(scItemEntity.getQdClassId())) {
                        this.sendMessage(user, scItemEntity);
                    }
                }
            } catch (Exception e) {
                throw new ZsxcBootException("签到/签退消息发送失败");
            }
        }
        return signSave;
    }

    public void sendMessage(SysUser user, ScItemEntity scItemEntity) {
        String title = "";
        StringBuilder contentBuilder = new StringBuilder();
        String remark = "";
        String userNo = "";
        //提醒人
        title = "项目签退评分提醒";
        contentBuilder = new StringBuilder()
                .append("有一个项目已完成签退，等待您去评分，请及时处理");
        remark = "点击可查看详情";
        userNo = user.getUsername();
        WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(userNo, appid);
        if (null == wxMesUserInfo) {
            return;
        }
        String openId = wxMesUserInfo.getOpenId();
        String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的工作提醒";
        WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                .setUseCommonTemplate(Boolean.TRUE)
                .setTheme(theme)
                .setTitle(title)
                .setUserId(wxMesUserInfo.getUsername())
                .setCreateDate(new Date())
                //   .setMiniAppUrl("pagesB/approvalHonor/approvalHonor")
                .setContent(contentBuilder.toString())
                .setOpenId(openId)
                .setRemark(remark);
        wxCommonMsgInfo.setMiniAppUrl("my/myProject/myprojectDt?id=" + scItemEntity.getId() + "&workstaus=" + scItemEntity.getWorkStatus() + "&type=1");

        wxMessageSender.wxMessageSend(wxCommonMsgInfo);

    }


    /**
     * 获取签到/签退二维码地址
     *
     * @param itemId     项目id
     * @param signType   签到类型
     * @param signTag    签到标识
     * @param qrCodeType 二维码类型
     * @return String
     * @throws UnsupportedEncodingException
     */
    private String getQrUrl(String itemId, Integer signType, String signTag, String qrCodeType) throws UnsupportedEncodingException {
        String wxRedirectUri = qrUrl + "item/sign/wx/" + itemId + "/" + signType + "?signTag=" + signTag + "&qrCodeType=" + qrCodeType;
        String url = "https://open.weixin.qq.com/connect/oauth2/authorize?appid="
                + appid + "&redirect_uri=" + URLEncoder.encode(wxRedirectUri, "utf-8")
                + "&response_type=code&scope=snsapi_userinfo&state=123#wechat_redirect";
        return url;
    }

    private String getQrCodeStatus(String itemId, Integer type) {
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        if (null == scItemEntity) throw new ZsxcBootException("参数错误：签到签退未查询到项目信息");
        String qrCodeRedisKey = ScItemSignEntity.QR_CLOSED_SUBFIX + scItemEntity.getId() + "::" + type;
        String closed = (String) redisTemplate.opsForValue().get(qrCodeRedisKey);
        String status = type.equals(ScItemSignEntity.SIGN_IN_TYPE) ?
                scItemEntity.getQrSigninClosed() : scItemEntity.getQrSignoffClosed();
        if (null == closed) {
            redisTemplate.opsForValue().set(qrCodeRedisKey, status, 2, TimeUnit.HOURS);
        }
        return status;
    }

    /**
     * 修改二维码关闭/开启状态
     *
     * @param itemId
     * @param type
     * @return
     */
    public Boolean modifyQrCodeStatus(String itemId, Integer type) {
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        if (null == scItemEntity) throw new ZsxcBootException("参数错误：签到签退未查询到项目信息");
        if (type.equals(ScItemSignEntity.SIGN_IN_TYPE)) {//签到
            String qrSigninClosed = scItemEntity.getQrSigninClosed();
            String status = qrSigninClosed.equals(ScItemSignEntity.QR_CLOSE)
                    ? ScItemSignEntity.QR_OPEN : ScItemSignEntity.QR_CLOSE;
            scItemEntity.setQrSigninClosed(status);
        } else {//签退
            String qrSignoffClosed = scItemEntity.getQrSignoffClosed();
            String status = qrSignoffClosed.equals(ScItemSignEntity.QR_CLOSE)
                    ? ScItemSignEntity.QR_OPEN : ScItemSignEntity.QR_CLOSE;
            scItemEntity.setQrSignoffClosed(status);
        }
        String qrCodeRedisKey = ScItemSignEntity.QR_CLOSED_SUBFIX + itemId + "::" + type;
        //redis 延迟双删
        redisTemplate.delete(qrCodeRedisKey);
        scItemMapper.updateById(scItemEntity);
        try {
            Thread.sleep(200);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        redisTemplate.delete(qrCodeRedisKey);

        return Boolean.TRUE;
    }

    /**
     * 获取申请人列表
     *
     * @param itemId
     * @return
     */
    private Set<String> getApplyList(String itemId) {
        //报名列表
        QueryWrapper<ScItemRegistrationEntity> regWrapper = new QueryWrapper<>();
        regWrapper.eq("item_id", itemId);
        regWrapper.eq("del_flag", DelFlagEnum.NO_DEL.getCode());
        List<ScItemRegistrationEntity> scItemRegistrationEntities
                = scItemRegistrationMapper.selectList(regWrapper);

        //报名人员信息列表
        return scItemRegistrationEntities.stream()
                .map(reg -> reg.getRealname() + "(" + reg.getUsername() + ")")
                .collect(Collectors.toSet());
    }

    /**
     * 获取签到人列表
     *
     * @param itemId
     * @param type
     * @return
     */
    private Set<String> getSignList(String itemId, Integer type) {
        //查询签到/签退人数
        QueryWrapper<ScItemSignEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("item_id", itemId);
        queryWrapper.eq("type", type);
        List<ScItemSignEntity> itemSignList = this.list(queryWrapper);
        //签到/签退列表
        return itemSignList.stream()
                .map(sign -> sign.getUserName() + "(" + sign.getUserCode() + ")")
                .collect(Collectors.toSet());
    }

    /**
     * 获取签到/签退基础信息
     *
     * @param scItemEntity
     * @param type
     * @return
     */
    private ItemSignDTO buildSignInfo(ScItemEntity scItemEntity, Integer type) {
        //签到/签退列表
        Set<String> signSet = getSignList(scItemEntity.getId(), type);
        Set<String> applySet = getApplyList(scItemEntity.getId());
        //报名人数
        Integer applyNum = applySet.size();
        //签到/签退人数
        Integer signNum = signSet.size();
        //未签到/签退列表
        List<String> unSignList = applySet.stream()
                .filter(apply -> !signSet.contains(apply))
                .collect(Collectors.toList());
        //未签到/签退人数
        Integer unSignNum = (applyNum - signNum) >= 0 ? (applyNum - signNum) : 0;
        return new ItemSignDTO().setItemId(scItemEntity.getId())
                .setItemName(scItemEntity.getItemName()).setSignNum(signNum)
                .setUnSignNum(unSignNum).setApplyNum(applyNum).setSignType(type)
                .setSignList(new LinkedList<>(signSet)).setUnSignList(unSignList)
                .setApplyList(new LinkedList<>(applySet));
    }


    @Override
    public List<ItemSignHourDTO> listUserItemSignHours(String itemId, List<String> userIds) {
        List<ItemSignHourDTO> signHourList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(userIds)) {
            List<ScItemSignEntity> signEntites = scItemSignMapper.listUserItemSignHours(itemId, userIds);
            Map<String, List<ScItemSignEntity>> signGroupByUserId =
                    signEntites.stream().collect(Collectors.groupingBy(signEntity -> signEntity.getCreateBy()));
            for (String userId : signGroupByUserId.keySet()) {
                ItemSignHourDTO itemSignHourDTO = new ItemSignHourDTO().setItemId(itemId).setUserId(userId)
                        .setSignHours(this.getSignHours(signGroupByUserId.get(userId)));
                signHourList.add(itemSignHourDTO);
            }
        }
        return signHourList;
    }

    @Override
    public BigDecimal getSignHours(String itemId, String userId) {
        Map<String, Object> params = new HashMap<>(2);
        params.put("item_id", itemId);
        params.put("create_by", userId);
        List<ScItemSignEntity> signEntites = scItemSignMapper.selectByMap(params);
        return this.getSignHours(signEntites);
    }


    /**
     * 根据签到记录计算签到时间
     *
     * @param userItemSignRecords
     * @return
     */
    @Override
    public BigDecimal getSignHours(List<ScItemSignEntity> userItemSignRecords) {
        if (CollectionUtils.isEmpty(userItemSignRecords)) return new BigDecimal(0);
        Optional<Date> minSignInOpt = userItemSignRecords.stream()
                .filter(record -> record.getType()
                        .equals(ScItemSignEntity.SIGN_IN_TYPE)).map(ScItemSignEntity::getCreateTime)
                .min(Date::compareTo);

        Optional<Date> maxSignOffOpt = userItemSignRecords.stream()
                .filter(record -> record.getType()
                        .equals(ScItemSignEntity.SIGN_OFF_TYPE)).map(ScItemSignEntity::getCreateTime)
                .max(Date::compareTo);

        if (!minSignInOpt.isPresent() || !maxSignOffOpt.isPresent()) return new BigDecimal(0);

        LocalDateTime minSignTime = DateUtils.dateToLocalDateTime(minSignInOpt.get());
        LocalDateTime maxSignTime = DateUtils.dateToLocalDateTime(maxSignOffOpt.get());

        Duration duration = Duration.between(minSignTime, maxSignTime);

        //秒转化小时
        BigDecimal signHours = BigDecimal.valueOf(duration.getSeconds())
                .divide(BigDecimal.valueOf(60 * 60), 2, BigDecimal.ROUND_HALF_UP);

        //如果是负数 返回 0
        return signHours.compareTo(new BigDecimal(0)) > -1 ? signHours : new BigDecimal(0);
    }


    public Boolean signDevolution(String itemId, String userIds) {
        ScItemEntity scItemEntity = scItemMapper.selectById(itemId);
        if (null == scItemEntity) throw new ZsxcBootException("参数错误：签到签退未查询到项目信息");
        List<WxUserDTO> userOpenIds = sysWeixinUserMapper.findUserOpenIds(Arrays.asList(userIds.split(",")), appid);
        userOpenIds.stream().forEach(wxUserDTO -> {
            String title = "签到二维码管理提醒";
            String theme = wxUserDTO.getRealname() + "/" + wxUserDTO.getUsername() + ",您有一条新的工作提醒";
            StringBuilder contentBuilder = new StringBuilder()
                    .append("有关项目：“").append(scItemEntity.getItemName())
                    .append("”的创建人已经指派您为二维码管理人员");
            String remark = "点击可打开签到二维码";
            String url = "/pagesA/sign/sign?type=" + "0" + "&itemId=" + itemId;
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setUserId(userIds)
                    .setTheme(theme)
                    .setTitle(title)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(wxUserDTO.getOpenId())
                    .setRemark(remark)
                    .setMiniAppUrl(url);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
            String title1 = "签退二维码管理提醒";
            String theme1 = wxUserDTO.getRealname() + "/" + wxUserDTO.getUsername() + ",您有一条新的工作提醒";
            StringBuilder contentBuilder1 = new StringBuilder()
                    .append("有关项目：“").append(scItemEntity.getItemName())
                    .append("”的创建人已经指派您为二维码管理人员");
            String remark1 = "点击可打开签退二维码";
            String url1 = "/pagesA/sign/sign?type=" + "1" + "&itemId=" + itemId;
            WxCommonMsgInfo wxCommonMsgInfo1 = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme1)
                    .setTitle(title1)
                    .setCreateDate(new Date())
                    .setContent(contentBuilder1.toString())
                    .setOpenId(wxUserDTO.getOpenId())
                    .setRemark(remark1)
                    .setMiniAppUrl(url1);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo1);
        });
        return Boolean.TRUE;
    }

    @Override
    public List<String> partUser(String itemId, List<String> ids) {
        return baseMapper.partUsers(itemId, ids);
    }
}
