<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.personal.mapper.SysUserRecordMapper">

    <select id="todoTaskPage" resultType="com.zs.create.modules.personal.entity.SysUserRecordEntity">
        SELECT a.id,a.user_id,a.username,a.`status`,a.check_users,a.audit_mind,a.avatar,
        a.birthday,a.email,a.phone,a.politic,a.sex,b.grade,b.type,a.create_time,a.update_time
        FROM sys_user_record a
        LEFT JOIN sys_user b on a.user_id = b.id
        WHERE a.status = 0 and FIND_IN_SET(#{record.checkUsers},a.check_users)
        <if test="record.userId!=null and record.userId!=''">
            and a.user_id like concat('%',#{record.userId},'%')
        </if>
        <if test="record.username!=null and record.username!=''">
            and ( a.username like concat('%',#{record.username},"%") or a.user_id like concat('%',#{record.username},'%') )
        </if>
        ORDER BY a.update_time DESC
    </select>
    <select id="historyTaskPage" resultType="com.zs.create.modules.personal.entity.SysUserRecordEntity">
        SELECT a.id,a.user_id,a.username,a.`status`,a.check_users,a.audit_mind,a.avatar,
        a.birthday,a.email,a.phone,a.politic,a.sex,b.grade,b.type,a.create_time,a.update_time
        FROM sys_user_record a
        LEFT JOIN sys_user b on a.user_id = b.id
        WHERE a.status  in (1,2) and a.update_by = #{record.checkUsers}
        <if test="record.userId!=null and record.userId!=''">
            and a.user_id like concat('%',#{record.userId},'%')
        </if>
        <if test="record.username!=null and record.username!=''">
            and ( a.username like concat('%',#{record.username},'%') or a.user_id like concat('%',#{record.username},'%') )
        </if>
        ORDER BY a.update_time DESC
    </select>
</mapper>