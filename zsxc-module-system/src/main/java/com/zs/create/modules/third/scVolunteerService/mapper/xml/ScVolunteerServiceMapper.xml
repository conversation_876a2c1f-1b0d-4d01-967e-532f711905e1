<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.third.scVolunteerService.mapper.ScVolunteerServiceMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.third.scVolunteerService.entity.ScVolunteerServiceEntity" id="scVolunteerServiceMap">
        <result property="id" column="id"/>
        <result property="itemId" column="item_id"/>
        <result property="itemName" column="item_name"/>
        <result property="itemContent" column="item_content"/>
        <result property="personInChargeName" column="person_in_charge_name"/>
        <result property="personInChargeTel" column="person_in_charge_tel"/>
        <result property="itemStartTime" column="item_start_time"/>
        <result property="itemEndTime" column="item_end_time"/>
        <result property="userCode" column="user_code"/>
        <result property="userName" column="user_name"/>
        <result property="userNo" column="user_no"/>
        <result property="signInTime" column="sign_in_time"/>
        <result property="signOutTime" column="sign_out_time"/>
        <result property="creditHours" column="credit_hours"/>
        <result property="itemTime" column="item_time"/>
        <result property="hours" column="hours"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="dataType" column="data_type"/>
        <result property="uniqueId" column="unique_id"/>
    </resultMap>

<select id="getSumVolunteerList"  resultType="java.util.Map">
SELECT
  item_id,
  SUM(IFNULL(credit_hours, 0)) credit_hours,
  SUM(IFNULL(hours, 0)) hours ,
  SUM(IFNULL(credit_hours, 0))+ SUM(IFNULL(hours, 0)) total_hours
FROM
  sc_volunteer_service
WHERE item_id = #{itemId}
  AND user_code = #{userNo}
  and DATE_FORMAT( create_time, '%Y-%m' ) = DATE_FORMAT( #{date}, '%Y-%m' )
  GROUP BY  item_id,user_code
</select>

    <update id="updateScVolunteerServiceType" >
        update sc_volunteer_service set data_type = '1' where item_id = #{itemId} and user_code= #{userNo} and DATE_FORMAT( create_time, '%Y-%m' ) = DATE_FORMAT( #{date}, '%Y-%m' )

    </update>
    <update id="updateCreateTime">
        update sc_volunteer_service set create_time = #{date} where id =#{id}
    </update>

    <update id="updateUserName">
        UPDATE `sc_volunteer_service` s
            LEFT JOIN sys_user u ON s.user_code = u.username
            SET s.user_name = u.realname
        WHERE
            s.user_name IS NULL
    </update>

    <select id="getUserCodeScVolunteerServiceHours" resultType="java.lang.String" parameterType="String">
        select SUM(IFNULL(credit_hours, 0))+ SUM(IFNULL(hours, 0)) total_hours
        FROM sc_volunteer_service where user_code = #{userCode}
    </select>
    <select id="getHours" resultType="java.math.BigDecimal">
        SELECT ifnull(sum(hours),0.00)
        FROM sc_item_hours
        WHERE status = 10
        and given=1 and
        del_flag=0
        and item_id in
        (SELECT DISTINCT item_id FROM sc_volunteer_service
        WHERE user_code = #{userId}
        <if test="st != null and et != null">
            and #{st} &lt; create_time  and #{et} >  create_time
        </if>)
        and user_id = #{userId}

    </select>
    <select id="getItemIds" resultType="java.lang.String">
        SELECT
        DISTINCT item_id
        FROM sc_volunteer_service
        <where>
            <if test="st != null and et != null">
                and #{st} &lt; create_time  and #{et} >  create_time
            </if>
        </where>
    </select>

    <select id="getZyhHours" resultType="com.zs.create.modules.item.dto.ScItemHourDto">
        SELECT
        user_id as userId,
        item_name as item_name,
        module,
        hours
        FROM sc_item_hours
        WHERE status = 10
        and given=1 and
        del_flag=0
        and item_id in
        (SELECT DISTINCT item_id FROM sc_volunteer_service
        WHERE user_code = #{userId}
        <if test="st != null and et != null">
            and #{st} &lt; create_time  and #{et} >  create_time
        </if>)
        and user_id = #{userId}
    </select>

    <select id="queryPageList" resultType="com.zs.create.modules.third.scVolunteerService.entity.ScVolunteerServiceVo">
        SELECT
        a.id as id ,a.user_name as userName,a.item_id as itemId,IFNULL(a.user_code,"---") as userCode,a.user_no as userNo,a.college_id as collegeId,
        a.classes_id as classesId,a.sign_in_time as signInTime,a.sign_out_time as signOutTime,a.credit_hours as creditHours,
        a.data_type as dataType,a.hours as hours,a.item_end_time as itemEndTime,
        a.item_name as itemName,
        DATE_FORMAT(a.create_time, '%Y年%m月' )as timeForExport,
        a.create_time as createTime,a.classes as classes,a.college as college,a.sync_id as syncId,a.honor_time as honorTime
        FROM sc_volunteer_service as a
        <where>
        <if  test="dateTime != null">
            and DATE_FORMAT(a.create_time,'%Y-%m') = DATE_FORMAT(#{dateTime} ,'%Y-%m')
        </if>
        <if  test="userId != null and userId != ''">
            and a.user_code = #{userId}
        </if>
        <if test="depId != null  and depId != ''">
            and (FIND_IN_SET (a.college_id,#{depId} )  or  FIND_IN_SET(a.classes_id,#{depId}) )
        </if>
        <if test="userName != null and userName != ''">
            and (a.user_name like concat('%', #{userName}, '%')  or a.user_code  like concat('%', #{userName}, '%'))
        </if>
        <if test="itemName != null and itemName != ''">
            and a.item_name like concat('%', #{itemName}, '%')
        </if>
        <if test="classesId != null and classesId != ''  ">
            and a.classes_id REGEXP  #{classesId}
        </if>
        </where>
        order by CONVERT( trim(IFNULL(a.college,"暂无"))  USING gbk ) asc,CONVERT( trim(IFNULL(a.classes,"暂无"))  USING gbk ) asc,CONVERT( trim(a.user_name)  USING gbk ) asc,
          a.user_no desc,a.item_id desc
    </select>
    <select id="queryHoursList" resultType="com.zs.create.modules.item.entity.ScItemHoursEntity">
        SELECT
        b.user_id as userId,b.hours as hours,b.volunteer_id
        FROM sc_volunteer_service as a
        inner join sc_item_hours b on b.user_id=a.user_code and b.volunteer_id=a.id
        <where>
            <if  test="dateTime != null">
                and DATE_FORMAT(a.create_time,'%Y-%m') = DATE_FORMAT(#{dateTime} ,'%Y-%m')
            </if>
            <if  test="userId != null and userId != ''">
                and a.user_code  like concat('%', #{userId}, '%')
            </if>
            <if test="depId != null  and depId != ''">
                and (  FIND_IN_SET (#{depId},a.college_id )  or  FIND_IN_SET(#{depId},a.classes_id )
                )
            </if>
            <if test="userName != null and userName != ''">
                and (a.user_name like concat('%', #{userName}, '%') or a.user_code  like concat('%', #{userName}, '%') )
            </if>
            <if test="itemName != null and itemName != ''">
                and a.item_name like concat('%', #{itemName}, '%')
            </if>
            <if test="classesId != null and classesId != ''  ">
                and a.classes_id REGEXP  #{classesId}
            </if>
        </where>
    </select>
    <select id="queryVolunteerList" resultType="com.zs.create.modules.third.scVolunteerService.entity.ScVolunteerServiceVo">
        SELECT
        a.id as id ,a.user_name as userName,a.item_id as itemId,IFNULL(a.user_code,"---") as userCode,a.user_no as userNo,a.college_id as collegeId,
        a.classes_id as classesId,a.sign_in_time as signInTime,a.sign_out_time as signOutTime,a.credit_hours as creditHours,
        DATE_FORMAT(a.create_time, '%Y年%m月' )as timeForExport,a.hours as hours,a.item_end_time as itemEndTime,a.honor_time as honorTime,
        (SELECT  b.update_time FROM  sc_item_hours  b  WHERE   b.user_id=a.user_code  AND b.item_id=a.item_id
        and a.id=b.volunteer_id ) as hoursCt,
        IFNULL(
        (SELECT  hours FROM  sc_item_hours  b  WHERE   b.user_id=a.user_code  AND b.item_id=a.item_id and a.id=b.volunteer_id ),"未转化" ) as period,
        a.data_type as dataType,a.item_name as itemName,a.create_time as createTime,a.classes as classes,a.college as college
        FROM sc_volunteer_service a
        <where>
        <if  test="dateTime != null">
            and DATE_FORMAT(a.create_time,'%Y-%m') = DATE_FORMAT(#{dateTime} ,'%Y-%m')
        </if>
        <if  test="userId != null and userId != ''">
            and a.user_code = #{userId}
        </if>
        <if test="depId != null  and depId != ''">
            and (  FIND_IN_SET (a.college_id,#{depId} )  or  FIND_IN_SET(a.classes_id,#{depId}) )
        </if>
        <if test="userName != null and userName != ''">
            and ( a.user_name like concat('%', #{userName}, '%') or a.user_code  like concat('%', #{userName}, '%') )
        </if>
        <if test="itemName != null and itemName != ''">
            and a.item_name like concat('%', #{itemName}, '%')
        </if>
        <if test="classesId != null and classesId != ''  ">
            and a.classes_id REGEXP  #{classesId}
        </if>
    </where>
        order by CONVERT( trim(IFNULL(a.college,"暂无"))  USING gbk ) asc,CONVERT( trim(IFNULL(a.classes,"暂无"))  USING gbk ) asc,CONVERT( trim(a.user_name)  USING gbk ) asc,
                 a.user_no desc,a.item_id desc,period desc,timeForExport desc
    </select>
    <select id="getTotalHoursByItemIdAndUserId" resultType="java.math.BigDecimal">
	SELECT
	TRUNCATE(SUM(IFNULL(credit_hours, 0))+ SUM(IFNULL(hours, 0)),1) total_hours
    FROM
      sc_volunteer_service
    WHERE item_id = #{itemId}
      AND user_code = #{userId}
      and data_type = '1'
    GROUP BY  item_id,user_code
    </select>
    <select id="getItemUserMonthHours"
            resultType="com.zs.create.modules.third.scVolunteerService.entity.ScVolunteerServiceEntity">
    SELECT
    id,
    item_id,
    item_name,
    user_code,
    user_name,
    TRUNCATE(SUM(IFNULL(credit_hours, 0))+ SUM(IFNULL(hours, 0)),1) hours,
    create_time

    from sc_volunteer_service
    WHERE
    data_type =1
    GROUP BY item_id,user_code,DATE_FORMAT(create_time,'%Y-%m')
    </select>
    <select id="dealList"
            resultType="com.zs.create.modules.third.scVolunteerService.entity.ScVolunteerServiceEntity">
        SELECT
        id,
        item_id,
        item_name,
        item_content,
        person_in_charge_name,
        person_in_charge_tel,
        item_start_time,
        item_end_time,
        user_code,
        user_name,
        user_no,
        sign_in_time,
        sign_out_time,
        credit_hours,
        item_time,
        hours,
        create_time,
        create_by,
        update_by,
        update_time,
        data_type,
        unique_id,
        college_id,
        classes_id,
        classes,
        college,
        sync_id,
        honor_time
        FROM
        sc_volunteer_service
        WHERE
        data_type = 0
        AND user_code IS NOT NULL
        AND user_code != ''
    </select>


</mapper>