package com.zs.create.modules.mq.entity;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.util.Date;
@Document("message_recive_user")
@Data
@Accessors(chain = true)
public class MessageReciveUserDto implements Serializable {
    /**
     * 消息数据集合名称
     */
    public static final String MESSAGE_RECIVE_USER_COLLECTION_NAME = "message_recive_user";
    /**
     * 主键id
     */
    @Id
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 对应消息主表BrokerMessageLogDto的id
     */
    private String messageId;
    /**
     * 阅读状态（0未读，1已读）
     */
    private String readFlag;
    /**
     * 阅读时间
     */
    private Date readTime;
    /**
     * 接受者(一人一条记录)
     */
    private String reciveUser;

    /**
     * 消息类型1:通知公告2:系统消息3.项目审核消息4.团队加入或解散消息（冗余字段 方便小铃铛查询）
     */
    private String msgCategory;

    /**
     * 创建时间
     */
    private Date createTime ;
    /**
     * 是否用于沟通平台,0-否,1-是
     */
    private String suggestionMsg;
}
