package com.zs.create.modules.backbone.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.backbone.dto.BackboneLaunchApplyDto;
import com.zs.create.modules.backbone.entity.BackboneLaunchApplyEntity;

import java.util.List;
import java.util.Map;


/**
 * @Description 学生评议申请Service层
 *
 * <AUTHOR>
 * @email null
 * @date 2021-05-22 12:47:22
 * @Version: V1.0
 */
public interface BackboneLaunchApplyDealService extends IService<BackboneLaunchApplyEntity> {

       void dealWithData(List<BackboneLaunchApplyEntity> list);
}

