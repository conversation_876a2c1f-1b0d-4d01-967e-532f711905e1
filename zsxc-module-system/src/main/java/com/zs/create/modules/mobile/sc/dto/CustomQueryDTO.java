package com.zs.create.modules.mobile.sc.dto;

import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @createUser hy
 * @createTime 2020-7-27
 * @description
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="自定义查询对象", description="自定义查询对象")
public class CustomQueryDTO implements Serializable {

    private String columnName;

    private String rule;

    private String val;

    // class java.lang.Integer
    private String valType;
}
