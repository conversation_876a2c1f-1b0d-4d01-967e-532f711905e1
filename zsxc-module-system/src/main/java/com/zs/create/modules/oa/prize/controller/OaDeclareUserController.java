package com.zs.create.modules.oa.prize.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.base.util.JwtUtil;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.DateUtils;
import com.zs.create.common.util.StringKit;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.oa.prize.entity.*;
import com.zs.create.modules.oa.prize.service.OaAuditPrizeServiece;
import com.zs.create.modules.oa.prize.service.OaDeclareUserService;
import com.zs.create.modules.oa.prize.service.OaPrizeLevelService;
import com.zs.create.modules.oa.prize.service.OaPrizeService;
import com.zs.create.modules.oa.prize.vo.*;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysDictItem;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import com.zs.create.modules.system.mapper.SysDictItemMapper;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.workflow.service.IWorkflowService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.activiti.bpmn.model.BpmnModel;
import org.activiti.bpmn.model.FlowElement;
import org.activiti.bpmn.model.UserTask;
import org.activiti.engine.HistoryService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.RuntimeService;
import org.activiti.engine.TaskService;
import org.activiti.engine.history.HistoricTaskInstance;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.MapExcelConstants;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecgframework.poi.excel.view.JeecgMapExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;


/**
 * <AUTHOR>
 * @Description 申报人员Controller层
 * @email null
 * @date 2021-01-16 16:06:53
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "奖项申报人员")
@RestController
@RequestMapping("/prize/oaDeclareUser")
public class OaDeclareUserController {
    @Autowired
    @Lazy
    private OaDeclareUserService oaDeclareUserService;
    @Autowired
    RepositoryService repositoryService;
    @Autowired
    HistoryService historyService;
    @Autowired
    TaskService taskService;
    @Autowired
    OaAuditPrizeServiece oaAuditPrizeServiece;
    @Autowired
    ISysDepartService sysDepartService;
    @Autowired
    RuntimeService runtimeService;
    @Autowired
    IWorkflowService workflowService;
    @Autowired
    OaPrizeService oaPrizeService;
    @Autowired
    private OaPrizeLevelService oaPrizeLevelService;
    @Resource
    private SysDictItemMapper sysDictItemMapper;
    @Resource
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private CampusAppService campusAppService;
    // 获奖等级已确认，即已转化过学时
    public static final Integer HJDJQR_TYPE = 1;
    // 未公示/未转化学时
    public static final Integer WGSWZH_TYPE = 0;
    // 已公示/已转化学时
    public static final Integer YGSWZH_TYPE = 1;


    /**
     * 分页列表查询
     */
    @AutoLog(value = "申报人员-分页列表查询")
    @ApiOperation(value = "申报人员-分页列表查询", notes = "申报人员-分页列表查询")
    @GetMapping(value = "/list")
    public Result<?> queryPageList(OaDeclareUserEntity oaDeclareUser,
                                   @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                   @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<OadeclareBmDto> page = oaDeclareUserService.pageDeclareList(oaDeclareUser, pageNo, pageSize);
        return Result.ok(page);
    }


    @AutoLog(value = "奖项管理获奖人员分页列表查询")
    @ApiOperation(value = "奖项管理获奖人员分页列表查询", notes = "奖项管理获奖人员分页列表查询")
    @GetMapping(value = "/getPrizeUserList")
    public Result<IPage<OaDeclareUserEntity>> getPrizeUserList(OaDeclareUserEntity user,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Result<IPage<OaDeclareUserEntity>> result = new Result<>();
        OaPrizeEntity prize = oaPrizeService.getById(user.getPrizeId());
        QueryWrapper<OaDeclareUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prize_id", user.getPrizeId());
        queryWrapper.ne("status", OaDeclareUserEntity.ZC);
        queryWrapper.ne("status", OaDeclareUserEntity.APPLY);
        queryWrapper.ne("status", OaDeclareUserEntity.AUDIT_REJECT);
        queryWrapper.ne("status", OaDeclareUserEntity.AUDIT_EDIT);
        queryWrapper.ne("status", OaDeclareUserEntity.REVOKE);
        // queryWrapper.eq("is_show",0);//查询未公示人员
        if (StringUtils.isNotBlank(user.getRealname()))
            queryWrapper.and(i -> i.like("realname", user.getRealname()).or().like("username", user.getRealname()));
        if (StringUtils.isNotBlank(user.getAwardLevel()))
            queryWrapper.like("award_level", user.getAwardLevel());
        queryWrapper.orderByAsc("create_time");
        Page<OaDeclareUserEntity> page = new Page<>(pageNo, pageSize);
        IPage<OaDeclareUserEntity> pageList = oaDeclareUserService.page(page, queryWrapper);


        List<OaDeclareUserEntity> records = pageList.getRecords();
        if (records.size() > 0) {
            for (int i = 0; i < records.size(); i++) {
                // 设置序号
                int index = (pageNo - 1) * pageSize + i + 1;
                records.get(i).setSort(String.valueOf(index));
                // 设置获奖等级结果默认值=建议值
                if (oConvertUtils.isNotEmpty(records.get(i).getAwardLevel()) && oConvertUtils.isEmpty(records.get(i).getAwardLevelResult())) {
                    records.get(i).setAwardLevelResult(records.get(i).getAwardLevel());
                }
                records.get(i).setPrizeIdDictText(prize.getTitle());
            }
            oaDeclareUserService.updateBatchById(records);
        }
        pageList.setRecords(records);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    @AutoLog(value = "获奖等级-编辑")
    @ApiOperation(value = "获奖等级-编辑", notes = "获奖等级-编辑")
    @PostMapping(value = "/editAwardLevel")
    @Transactional
    public Result<OaDeclareUserEntity> editAwardLevel(@RequestBody OaDeclareUserEntity oaDeclareUser) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 校验团委身份
        Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getUsername());
        if (oConvertUtils.isEmpty(sysUser)) {
            throw new ZsxcBootException("未获取到登录人信息！");
        }
        if (!twPersonInCharge) {
            throw new ZsxcBootException("非团委不可修改！");
        }
        Result<OaDeclareUserEntity> result = new Result<>();
        OaDeclareUserEntity old = oaDeclareUserService.getById(oaDeclareUser.getId());
        if (oConvertUtils.isEmpty(old)) {
            throw new ZsxcBootException("未找到对应数据");
        }
//        if (old.getIsConfirm().equals(HJDJQR_TYPE)){
        if (old.getIsAdd().equals(HJDJQR_TYPE)) {
            throw new ZsxcBootException("等级已确认，不可修改");
        }
        if (oConvertUtils.isNotEmpty(oaDeclareUser.getAwardLevelResult())) {
            OaPrizeLevelEntity level = oaPrizeLevelService.getById(oaDeclareUser.getAwardLevelResult());
            if (oConvertUtils.isEmpty(level)) throw new ZsxcBootException("对应等级有误");
            old.setAwardLevel(level.getId())
                    .setAwardLevelResult(level.getId())
                    .setHours(level.getHours());
        }
        boolean b = oaDeclareUserService.updateById(old);
        if (b) {
            result.success("修改成功");
        } else {
            result.error500("修改失败");
        }
        return result;
    }


    @AutoLog(value = "奖项管理-按钮判断")
    @ApiOperation(value = "奖项管理-按钮判断", notes = "奖项管理-按钮判断")
    @GetMapping(value = "/buttonShow")
    public Result<OaDeclareUSerButtonShowDto> buttonShow(@RequestParam(name = "prizeId") String prizeId) {
        // 对应奖项，对应奖项申报人员信息
        OaPrizeEntity prizeEntity = oaPrizeService.getById(prizeId);
        if (oConvertUtils.isEmpty(prizeEntity)) throw new ZsxcBootException("未找到对应奖项");
        Result<OaDeclareUSerButtonShowDto> result = new Result<>();
        OaDeclareUSerButtonShowDto buttonShowDto = new OaDeclareUSerButtonShowDto();

        // 公示按钮出现条件：奖项申报时间已结束，此奖项未公示过，即所有此奖项数据is_show为0
        LambdaQueryWrapper<OaDeclareUserEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaDeclareUserEntity::getPrizeId, prizeId);
//        wrapper.eq(OaDeclareUserEntity::getStatus, OaDeclareUserEntity.AUDIT_PASS);
        List<OaDeclareUserEntity> list = oaDeclareUserService.list(wrapper);
        List<Integer> showStatus = list.stream().map(e -> e.getIsShow()).collect(Collectors.toList());
        if (showStatus.size() > 0) {
            Boolean publicShow = true;
            if (showStatus.contains(1)) {
                publicShow = false;
            }
            if (prizeEntity.getEt().before(new Date()) && publicShow) {
                buttonShowDto.setPublicButton(OaDeclareUSerButtonShowDto.BUTTON_SHOW);
            } else {
                buttonShowDto.setPublicButton(OaDeclareUSerButtonShowDto.BUTTON_NOT_SHOW);
            }
        } else {
            buttonShowDto.setPublicButton(OaDeclareUSerButtonShowDto.BUTTON_NOT_SHOW);
        }

        // 奖项确认按钮出现条件：所有审核过数据都已公示过,且没有转化学时过
        LambdaQueryWrapper<OaDeclareUserEntity> confirmWrappers = new LambdaQueryWrapper<>();
        confirmWrappers.eq(OaDeclareUserEntity::getPrizeId, prizeId);
        confirmWrappers.eq(OaDeclareUserEntity::getStatus, OaDeclareUserEntity.END_PUBLIC);
        List<OaDeclareUserEntity> conList = oaDeclareUserService.list(confirmWrappers);

        Boolean confirm = true;
        if (conList.size() > 0) {
            for (OaDeclareUserEntity entity : conList) {
                if (entity.getIsShow().equals(WGSWZH_TYPE) || entity.getIsAdd().equals(YGSWZH_TYPE) || entity.getPublicityEt().after(new Date())) {
                    confirm = false;
                }
            }
            if (confirm && prizeEntity.getIsAffirm() == 0) {
                buttonShowDto.setConfirmButton(OaDeclareUSerButtonShowDto.BUTTON_SHOW);
            } else {
                buttonShowDto.setConfirmButton(OaDeclareUSerButtonShowDto.BUTTON_NOT_SHOW);
            }
        } else {
            buttonShowDto.setConfirmButton(OaDeclareUSerButtonShowDto.BUTTON_NOT_SHOW);
        }
        result.setResult(buttonShowDto);
        result.setSuccess(true);
        return result;
    }

    @AutoLog(value = "奖项管理-奖项确认")
    @ApiOperation(value = "奖项管理-奖项确认", notes = "奖项管理-奖项确认")
    @GetMapping(value = "/confirmPrize")
    public Result<?> confirmPrize(@RequestParam(name = "prizeId") String prizeId) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 校验团委身份
        Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getUsername());
        if (oConvertUtils.isEmpty(sysUser)) {
            throw new ZsxcBootException("未获取到登录人信息！");
        }
        if (!twPersonInCharge) {
            throw new ZsxcBootException("非团委不可进行确认操作！");
        }
        OaPrizeEntity prizeEntity = oaPrizeService.getById(prizeId);
        if (oConvertUtils.isEmpty(prizeEntity)) throw new ZsxcBootException("未找到对应奖项");
        Boolean result = oaDeclareUserService.confirmPrize(prizeId);
        if (result) {
            return Result.ok("奖项确认成功");
        } else {
            return Result.error("奖项确认失败");
        }

    }

    @AutoLog(value = "奖项管理-导出表格")
    @ApiOperation(value = "奖项管理-导出表格", notes = "奖项管理-导出表格")
    @GetMapping(value = "/export/managementExcel")
    public ModelAndView managementExportExcl(OaDeclareUserEntity user) {
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        // 获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 校验团委身份
        Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getUsername());

        List<ExcelExportEntity> entityList = new ArrayList<>();
        ExcelExportEntity entity = new ExcelExportEntity("姓名", "realname");
        entity.setWidth(25);
        entityList.add(entity);
        entity = new ExcelExportEntity("学号", "username");
        entity.setWidth(25);
        entityList.add(entity);
        entity = new ExcelExportEntity("奖项标题", "prizeId_dictText");
        entity.setWidth(25);
        entityList.add(entity);
        entity = new ExcelExportEntity("获得学时", "hours");
        entity.setWidth(25);
        entityList.add(entity);
        entity = new ExcelExportEntity("获奖等级建议", "awardLevel_dictText");
        entity.setWidth(25);
        entityList.add(entity);
        if (twPersonInCharge) {
            entity = new ExcelExportEntity("获奖等级确认", "awardLevelResult_dictText");
            entity.setWidth(25);
            entityList.add(entity);
        }
        QueryWrapper<OaDeclareUserEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("prize_id", user.getPrizeId());
        queryWrapper.ne("status", OaDeclareUserEntity.ZC);
        queryWrapper.ne("status", OaDeclareUserEntity.APPLY);
        queryWrapper.ne("status", OaDeclareUserEntity.AUDIT_REJECT);
        queryWrapper.ne("status", OaDeclareUserEntity.AUDIT_EDIT);
        queryWrapper.ne("status", OaDeclareUserEntity.REVOKE);
        // queryWrapper.eq("is_show",0);//查询未公示人员
        if (StringUtils.isNotBlank(user.getRealname()))
            queryWrapper.and(i -> i.like("realname", user.getRealname()).or().like("username", user.getRealname()));
        if (StringUtils.isNotBlank(user.getAwardLevel()))
            queryWrapper.like("award_level", user.getAwardLevel());
        queryWrapper.orderByAsc("create_time");
        List<OaDeclareUserEntity> list = oaDeclareUserService.list(queryWrapper);
        List<String> prizeIds = new ArrayList<>();
        List<String> awardLevels = new ArrayList<>();
        list.forEach(oaDeclareUserEntity -> {
            if (!prizeIds.contains(oaDeclareUserEntity.getPrizeId())) {
                prizeIds.add(oaDeclareUserEntity.getPrizeId());
            }
            if (!awardLevels.contains(oaDeclareUserEntity.getAwardLevel())) {
                awardLevels.add(oaDeclareUserEntity.getAwardLevel());
            }
        });

        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, String> prizedTitleMap = new HashMap<>();
        if (prizeIds.size() > 0) {
            LambdaQueryWrapper<OaPrizeEntity> oaPrizeEntityLqw = new LambdaQueryWrapper<>();
            oaPrizeEntityLqw.select(OaPrizeEntity::getId, OaPrizeEntity::getTitle).in(OaPrizeEntity::getId, prizeIds);
            List<OaPrizeEntity> list1 = oaPrizeService.list(oaPrizeEntityLqw);

            for (OaPrizeEntity oaPrizeEntity : list1) {
                if (!prizedTitleMap.containsKey(oaPrizeEntity.getId())) {
                    prizedTitleMap.put(oaPrizeEntity.getId(), oaPrizeEntity.getTitle());
                }
            }
        }
        Map<String, String> awardLevelsTextMap = new HashMap<>();
        if (awardLevels.size() > 0) {
            LambdaQueryWrapper<OaPrizeLevelEntity> oaPrizeLevelEntityLqw = new LambdaQueryWrapper<>();
            oaPrizeLevelEntityLqw.select(OaPrizeLevelEntity::getId, OaPrizeLevelEntity::getName).in(OaPrizeLevelEntity::getId, awardLevels);
            List<OaPrizeLevelEntity> list2 = oaPrizeLevelService.list(oaPrizeLevelEntityLqw);
            for (OaPrizeLevelEntity oaPrizeLevelEntity : list2) {
                if (!awardLevelsTextMap.containsKey(oaPrizeLevelEntity.getId())) {
                    awardLevelsTextMap.put(oaPrizeLevelEntity.getId(), oaPrizeLevelEntity.getName());
                }
            }
        }

        for (OaDeclareUserEntity record : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("realname", oConvertUtils.null2String(record.getRealname()));
            map.put("username", oConvertUtils.null2String(record.getUsername()));
            map.put("prizeId_dictText", oConvertUtils.null2String(prizedTitleMap.get(record.getPrizeId())));
            map.put("hours", oConvertUtils.null2String(record.getHours().setScale(1)));
            map.put("awardLevel_dictText", oConvertUtils.null2String(awardLevelsTextMap.get(record.getAwardLevel())));
            if (twPersonInCharge) {
                map.put("awardLevelResult_dictText", oConvertUtils.null2String(awardLevelsTextMap.get(record.getAwardLevelResult())));
            }
            dataList.add(map);
        }

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("奖项管理导出数据");
        exportParams.setSecondTitle("导出人：" + sysUser.getRealname() + "，导出时间：" + DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss") + "【 导出说明： 当导出数据量过大时系统将会把数据分成多个Sheet（工作表）导出,默认Sheet从0开始依次递增 】");
        exportParams.setSheetName("Sheet0");
        // 导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "奖项管理导出列表");
        mv.addObject(MapExcelConstants.ENTITY_LIST, entityList);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        return mv;
    }


    @AutoLog(value = "获取申报人员团支部部门列表")
    @ApiOperation(value = "获取申报人员团支部部门列表", notes = "获取申报人员团支部部门列表")
    @GetMapping(value = "/getUserDerprts")
    public Result<?> getUserDerprts(@RequestParam(name = "userId") String userId,
                                    @RequestParam(name = "depId", required = false) String depId) {
        Result<List<SysDepart>> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (StringUtils.isEmpty(userId)) {
            userId = sysUser.getId();
        }
        List<SysDepart> list = sysDepartService.applyUserIdDeparts(userId, "10,17");// 团支部
        // 2025-3-27 如何这个学生的团支部数据被删除了则，返回该部门的团支部数据
        if (StringKit.isEmpty(list) || list.size() == 0) {
            LambdaQueryWrapper<SysDepart> lam = new LambdaQueryWrapper<>();
            lam.eq(SysDepart::getId, StringKit.null2String(depId));
            lam.select(SysDepart::getId, SysDepart::getDepartName);
            list = sysDepartService.list(lam);
        }
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }

    @AutoLog(value = "获取团支部二课发布数和团员平均学时数")
    @ApiOperation(value = "获取团支部二课发布数和团员平均学时数", notes = "获取团支部二课发布数和团员平均学时数")
    @GetMapping(value = "/getYouthLeagueInfo")
    public Result<Map> getYouthLeagueInfo(@RequestParam(name = "depId", required = true) String depId) {
        Result<Map> result = new Result<>();
        Map<String, Object> map = oaDeclareUserService.getYouthLeagueInfo(depId);
        result.setSuccess(true);
        result.setResult(map);
        return result;
    }

    @AutoLog(value = "获取优秀共青团员自动带入数据")
    @ApiOperation(value = "获取优秀共青团员自动带入数据", notes = "获取优秀共青团员自动带入数据")
    @GetMapping(value = "/getExcellentYouthLeagueInfo")
    public Result<Map> getExcellentYouthLeagueInfo(@RequestParam(name = "id", required = false) String id) {
        Result<Map> result = new Result<>();
        Map<String, Object> map = oaDeclareUserService.getExcellentYouthLeagueInfo(id);
        result.setSuccess(true);
        result.setResult(map);
        return result;
    }

    @AutoLog(value = "申报青年大学习返回数据")
    @ApiOperation(value = "申报青年大学习返回数据", notes = "申报青年大学习返回数据")
    @GetMapping(value = "/getYouthLearningInfo")
    public Result<List<YouthLearningResult>> getYouthLearningInfo() {
        Result<List<YouthLearningResult>> result = new Result<>();
        List<YouthLearningResult> youthLearningInfo = oaDeclareUserService.getYouthLearningInfo();
        result.setResult(youthLearningInfo);
        return result;
    }


    /**
     * 奖项申报青年大学习添加返回数据
     *
     * @param prizeId  奖项id
     * @param username 学号
     * @return
     */
    @AutoLog(value = "返回奖项申报青年大学习添加返回数据，判断是否能打开页面")
    @ApiOperation(value = "返回奖项申报青年大学习添加返回数据，判断是否能打开页面", notes = "返回奖项申报青年大学习添加返回数据，判断是否能打开页面")
    @GetMapping(value = "/getDeclareUserInfo")
    public Result<OaDeclareUserInfoDto> getDeclareUserInfo(String prizeId, String username) {

        return oaDeclareUserService.checkDeclareUserInfo(prizeId, username);
    }


    /**
     * 添加
     */
    @AutoLog(value = "申报人员-添加")
    @ApiOperation(value = "申报人员-添加", notes = "申报人员-添加")
    @PostMapping(value = "/add")
    @NoRepeatSubmit(expireSeconds = 5)
    public Result<?> add(@RequestBody OaDeclareUserEntity oaDeclareUser) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        oaDeclareUser.setUserId(sysUser.getId())
                .setUsername(sysUser.getUsername())
                .setRealname(sysUser.getRealname())
                .setCreateTime(new Date());
        // 启动流程实例
        oaDeclareUserService.saveDeclarUser(oaDeclareUser);
        return Result.ok("添加成功");
    }

    /**
     * 查看
     */
    @AutoLog(value = "申报人员-查看")
    @ApiOperation(value = "申报人员-查看", notes = "申报人员-查看")
    @GetMapping(value = "/findUserInfo")
    public Result<Map> findUserInfo(@RequestParam(name = "declareId") String declareId) {
        Result<Map> result = new Result<>();
        Map<String, Object> map = oaDeclareUserService.findUserInfo(declareId);
        result.setResult(map);
        result.setSuccess(true);
        return result;
    }

    /**
     * 流程撤回操作
     *
     * @param taskId
     * @return
     */
    @AutoLog(value = "申报人员-流程撤回操作")
    @ApiOperation(value = "申报人员-流程撤回操作", notes = "申报人员-流程撤回操作")
    @GetMapping(value = "/withdrawTaskTo")
    public Result<?> withdrawTaskTo(@RequestParam(name = "taskId") String taskId,
                                    @RequestParam(name = "declareId") String declareId) {
        try {
            OaDeclareUserEntity oaDeclareUserEntity = oaDeclareUserService.getById(declareId);
            if (1 == oaDeclareUserEntity.getIsReview()) {
                throw new ZsxcBootException("奖项已被审核，无法撤回");
            }
            HistoricTaskInstance currTask = historyService
                    .createHistoricTaskInstanceQuery().taskId(taskId)
                    .singleResult();
            // 根据流程id查询代办任务中流程信息
            // Task task = taskService.createTaskQuery().processInstanceId(currTask.getProcessInstanceId()).singleResult();
            runtimeService.deleteProcessInstance(currTask.getProcessInstanceId(), "撤回");
            OaDeclareUserEntity entity = new OaDeclareUserEntity();
            entity.setId(declareId).setStatus("10");
            oaDeclareUserService.updateById(entity);
            // 推送消息
            campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(currTask.getProcessInstanceId()));
            return Result.ok("撤回成功!");
        } catch (Exception e) {
            return Result.error("撤回失败!");
        }
    }


    /**
     * 添加
     */
    @AutoLog(value = "申报人员-重新提交")
    @ApiOperation(value = "申报人员-重新提交", notes = "申报人员-重新提交")
    @PostMapping(value = "/publish/{id}")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> publish(@PathVariable String id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 启动流程实例
        oaDeclareUserService.savePublish(id, sysUser.getId());
        return Result.ok("提交成功");
    }


    /**
     * 编辑
     */
    @AutoLog(value = "申报人员-编辑")
    @ApiOperation(value = "申报人员-编辑", notes = "申报人员-编辑")
    @PutMapping(value = "/edit")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<OaDeclareUserEntity> edit(@RequestBody OaDeclareUserEntity oaDeclareUser) {
        Result<OaDeclareUserEntity> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        oaDeclareUser.setPrizeId(oaDeclareUser.getMemberFormData().get("prizeId").toString());
        oaDeclareUser.setMaterial(JSON.toJSONString(oaDeclareUser.getMemberFormData()));
        oaDeclareUser.setCreateBy(sysUser.getId());
        OaPrizeEntity prizeEntity = oaPrizeService.getById(oaDeclareUser.getPrizeId());
        if (prizeEntity == null) throw new ZsxcBootException("当前奖项不存在");
        SysDictItem sysDictItem = sysDictItemMapper.selectById(prizeEntity.getAuditId());
        if (sysDictItem == null) throw new ZsxcBootException("当前奖项不存在审核流程");

        OaDeclareUserEntity oaDeclareUserEntity = oaDeclareUserService.queryByUserId(oaDeclareUser.getPrizeId(), sysUser.getId());

        Date date = new Date();
//        非6 驳回待修改，未开始，已结束
//        撤回怎么处理？撤回依然有processInstansId

        // 23.09.25处理逻辑同save方法
        if (date.compareTo(prizeEntity.getEt()) > 0) {
            String processInstanceId = oaDeclareUserEntity.getProcessInstanceId();
//            -2时 process_instance_id 不为空代表之前提交过
            if ("-2".equals(oaDeclareUserEntity.getStatus()) && oConvertUtils.isEmpty(processInstanceId)) {
                throw new ZsxcBootException("当前时间不在申报时间内");
            }
//            判断是否已进入下一阶段（status>=4 && != 6、10）
            int count = oaDeclareUserService.count(new LambdaQueryWrapper<OaDeclareUserEntity>()
                    .eq(OaDeclareUserEntity::getPrizeId, oaDeclareUserEntity.getPrizeId())
                    .ge(OaDeclareUserEntity::getStatus, "4")
                    .notIn(OaDeclareUserEntity::getStatus, "6", "10"));
            if (count > 0) throw new ZsxcBootException("当前奖项已进入下一环节，无法再提交");
        }

        UpdateWrapper<OaDeclareUserEntity> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("prize_id", oaDeclareUser.getMemberFormData().get("prizeId").toString());
        updateWrapper.eq("user_id", sysUser.getId());

        OaPrizeEntity prize = oaPrizeService.getById(oaDeclareUser.getMemberFormData().get("prizeId").toString());
        if (!"6ydxs".equals(prize.getMaterialUrl())) {
            oaDeclareUserEntity.setDepId(oaDeclareUser.getMemberFormData().get("depId").toString());
        }
        oaDeclareUserEntity.setPrizeId(oaDeclareUser.getMemberFormData().get("prizeId").toString());
        oaDeclareUserEntity.setMaterial(JSON.toJSON(oaDeclareUser.getMemberFormData()).toString());
        if (sysDepartService.getById(oaDeclareUserEntity.getDepId()).getOrgType().equals("17")) {
            oaDeclareUserEntity.setTzbType("1");
        }
        oaDeclareUserEntity.setCreateTime(new Date());
        oaDeclareUser.setCreateTime(new Date());
        oaDeclareUserService.update(oaDeclareUserEntity, updateWrapper);

        // 编辑完成重新申报
        publish(oaDeclareUser.getMemberFormData().get("prizeId").toString());
        result.success("提交成功!");
        return result;
    }

    @AutoLog(value = "申报人员-进行中编辑")
    @ApiOperation(value = "申报人员-进行中编辑", notes = "申报人员-进行中编辑")
    @PutMapping(value = "/handEdit")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> handEdit(@RequestBody OaPrizeEntity oaPrizeEntity) {
        OaPrizeEntity entity = oaPrizeService.getById(oaPrizeEntity.getId());
        if (entity == null) {
            throw new RuntimeException("当前奖项不存在!");
        }
        Date newEt = oaPrizeEntity.getEt();
        Date oldEt = entity.getEt();
        if (newEt.compareTo(oldEt) != 0) {
            if (oldEt.before(new Date())) {
                throw new ZsxcBootException("奖项申报已结束，不可修改时间!");
            }
        }
        if (oaPrizeEntity.getMaterialUrl().equals("6ydxs")) {
            oaPrizeEntity.setCheckOption("5");
        }
        oaPrizeService.updateById(oaPrizeEntity);
        return Result.ok("编辑成功!");
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "申报人员-通过id删除")
    @ApiOperation(value = "申报人员-通过id删除", notes = "申报人员-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name = "id", required = true) String id) {
        try {
            oaDeclareUserService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败", e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "申报人员-批量删除")
    @ApiOperation(value = "申报人员-批量删除", notes = "申报人员-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<OaDeclareUserEntity> deleteBatch(@RequestParam(name = "ids", required = true) String ids) {
        Result<OaDeclareUserEntity> result = new Result<>();
        if (ids == null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        } else {
            this.oaDeclareUserService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "申报人员-通过id查询")
    @ApiOperation(value = "申报人员-通过id查询", notes = "申报人员-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OaDeclareUserEntity> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<OaDeclareUserEntity> result = new Result<>();
        OaDeclareUserEntity oaDeclareUser = oaDeclareUserService.getById(id);
        if (oaDeclareUser == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(oaDeclareUser);
            result.setSuccess(true);
        }
        return result;
    }


    @PostMapping("/auditPrize")
    @AutoLog(value = "奖项申报审核")
    @ApiOperation(value = "奖项申报审核", notes = "奖项申报审核")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> auditHonor(@RequestBody @Valid OaDeclareUserAuditDTO dto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        dto.setOnekeyReject(1);
        Map<String, Object> variables = new HashMap<>();
        variables.put("status", dto.getStatus());
        String processInstanceId = oaDeclareUserService.auditPrizeProcess(dto.getTaskId(), dto.getRejectstatus(),
                dto.getAuditNote(), sysUser.getId(), dto.getLevelId(), variables, dto.getOnekeyReject());
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
        return Result.ok("审核成功");
    }

    @PostMapping("/cheekPrize")
    @AutoLog(value = "效验批量审核")
    @ApiOperation(value = "效验批量审核", notes = "效验批量审核")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<Map> cheekPrize(@RequestBody Map map) {
        if (!map.containsKey("ids")) throw new RuntimeException("参数错误");
        Result<Map> result = new Result<>();
        try {
            Map<String, Object> content = oaDeclareUserService.cheekPrize(map.get("ids").toString());
            result.setSuccess(true);
            result.setResult(content);
        } catch (Exception e) {
            log.error("操作失败!:{}", e.getMessage());
            return result.error500(e.getMessage());
        }
        return result;
    }

    @PostMapping("/batchAuditPrize")
    @AutoLog(value = "批量奖项申报审核")
    @ApiOperation(value = "奖项申报审核", notes = "批量奖项申报审核")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> batchAuditPrize(@RequestBody OaDeclareUserDto oaDeclareUserDto) {
        if (StringUtils.isEmpty(oaDeclareUserDto.getIds())) {
            throw new RuntimeException("参数错误");
        }
        oaDeclareUserService.batchAuditPrize(oaDeclareUserDto);

        return Result.ok("审核成功");
    }

    /**
     * 奖项申报列表
     *
     * @return
     */
    @GetMapping("/prizeList")
    @AutoLog(value = "奖项申报列表")
    @ApiOperation(value = "奖项申报列表", notes = "奖项申报列表")
    public Result<?> prizeList(OaDeclareUserEntity entity) {
        List<OaDeclareUserEntity> list = oaDeclareUserService.prizeList(entity);
        return Result.ok(list);
    }

    /**
     * 奖项申报审核待办任务
     *
     * @return
     */
    @GetMapping("/taskList")
    @AutoLog(value = "奖项申报审核待办任务")
    @ApiOperation(value = "奖项申报审核待办任务", notes = "奖项申报审核待办任务")
    public Result<?> taskList(OaDeclareUserEntity entity,
                              @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                              @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        Page<OaDeclareUserEntity> page = oaDeclareUserService.taskPage(entity, pageNo, pageSize);
        return Result.ok(page);
    }


    @AutoLog(value = "奖项审核-导出表格")
    @ApiOperation(value = "奖项审核-导出表格", notes = "奖项审核-导出表格")
    @GetMapping(value = "/export/examinationExcel")
    public ModelAndView examinationExportExcl(OaDeclareUserEntity oaDeclareUserEntity) {
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        // 获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ExcelExportEntity> entityList = new ArrayList<>();
        ExcelExportEntity entity = new ExcelExportEntity("姓名", "realname");
        entity.setWidth(20);
        entityList.add(entity);
        entity = new ExcelExportEntity("学号", "username");
        entity.setWidth(20);
        entityList.add(entity);
        entity = new ExcelExportEntity("奖项标题", "itemName");
        entity.setWidth(20);
        entityList.add(entity);
        entity = new ExcelExportEntity("提交时间", "createTime");
        entity.setWidth(20);
        entityList.add(entity);
        entity = new ExcelExportEntity("状态", "status");
        entity.setWidth(20);
        entityList.add(entity);
        List<OaDeclareUserEntity> list = oaDeclareUserService.taskPageList(oaDeclareUserEntity);
        List<String> prizeIds = new ArrayList<>();
        list.forEach(i -> {
            if (!prizeIds.contains(i.getPrizeId())) {
                prizeIds.add(i.getPrizeId());
            }
            // 0 未申报 1申报中 2已获奖 3未获奖 4 带打分 5 已打分 6 修改后提交
            if ("0".equals(i.getStatus())) {
                i.setStatus("未申报");
            } else if ("1".equals(i.getStatus())) {
                i.setStatus("申报中");
            } else if ("2".equals(i.getStatus())) {
                i.setStatus("已审核");
            } else if ("3".equals(i.getStatus())) {
                i.setStatus("驳回");
            } else if ("4".equals(i.getStatus())) {
                i.setStatus("公示中");
            } else if ("5".equals(i.getStatus())) {
                i.setStatus("公示已结束");
            } else if ("6".equals(i.getStatus())) {
                i.setStatus("驳回待修改");
            } else if ("7".equals(i.getStatus())) {
                i.setStatus("待评审");
            } else if ("8".equals(i.getStatus())) {
                i.setStatus("评审中");
            } else if ("9".equals(i.getStatus())) {
                i.setStatus("已评审");
            } else if ("10".equals(i.getStatus())) {
                i.setStatus("撤回");
            } else if ("11".equals(i.getStatus())) {
                i.setStatus("已获奖");
            } else if ("12".equals(i.getStatus())) {
                i.setStatus("未获奖");
            } else if ("-2".equals(i.getStatus())) {
                i.setStatus("暂存");
            }
        });
        List<Map<String, Object>> dataList = new ArrayList<>();
        Map<String, String> prizedTitleMap = new HashMap<>();
        if (prizeIds.size() > 0) {
            LambdaQueryWrapper<OaPrizeEntity> oaPrizeEntityLqw = new LambdaQueryWrapper<>();
            oaPrizeEntityLqw.select(OaPrizeEntity::getId, OaPrizeEntity::getTitle).in(OaPrizeEntity::getId, prizeIds);
            List<OaPrizeEntity> list1 = oaPrizeService.list(oaPrizeEntityLqw);

            for (OaPrizeEntity oaPrizeEntity : list1) {
                if (!prizedTitleMap.containsKey(oaPrizeEntity.getId())) {
                    prizedTitleMap.put(oaPrizeEntity.getId(), oaPrizeEntity.getTitle());
                }
            }
        }
        for (OaDeclareUserEntity record : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("realname", oConvertUtils.null2String(record.getRealname()));
            map.put("username", oConvertUtils.null2String(record.getUsername()));
            map.put("itemName", oConvertUtils.null2String(prizedTitleMap.get(record.getPrizeId())));
            map.put("createTime", oConvertUtils.null2String(DateUtils.formatDate(record.getCreateTime(), "yyyy-MM-dd HH:mm:ss")));
            map.put("status", oConvertUtils.null2String(record.getStatus()));
            dataList.add(map);
        }

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("奖项审核导出数据");
        exportParams.setSecondTitle("导出人：" + sysUser.getRealname() + "，导出时间：" + DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss") + "【 导出说明： 当导出数据量过大时系统将会把数据分成多个Sheet（工作表）导出,默认Sheet从0开始依次递增 】");
        exportParams.setSheetName("Sheet0");
        // 导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "奖项审核导出列表");
        mv.addObject(MapExcelConstants.ENTITY_LIST, entityList);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        return mv;
    }

    /*
     * 手机端获奖公示人员查看
     * */
    @AutoLog(value = "手机端获奖公示人员查看")
    @ApiOperation(value = "手机端获奖公示人员查看")
    @GetMapping("/publicUserList")
    public Result<Map<String, Object>> publicUserList(@RequestParam(name = "prizeId", required = true) String prizeId) {
        Result<Map<String, Object>> result = new Result<>();
        Map<String, Object> map = oaDeclareUserService.publicUserList(prizeId);
        result.setSuccess(true);
        result.setResult(map);
        return result;
    }


    /**
     * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<OaDeclareUserEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                OaDeclareUserEntity oaDeclareUser = JSON.parseObject(deString, OaDeclareUserEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(oaDeclareUser, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        // Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<OaDeclareUserEntity> pageList = oaDeclareUserService.list(queryWrapper);
        // 导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "申报人员列表");
        mv.addObject(NormalExcelConstants.CLASS, OaDeclareUserEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("申报人员列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
     * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<OaDeclareUserEntity> listOaDeclareUsers = ExcelImportUtil.importExcel(file.getInputStream(), OaDeclareUserEntity.class, params);
                oaDeclareUserService.saveBatch(listOaDeclareUsers);
                return Result.ok("文件导入成功！数据行数:" + listOaDeclareUsers.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }

    /**
     * @param processId
     * @return
     */
    public List<UserTask> getTaskByProcessId(String processId) {
        /*
         *  获取流程实例
//         */
//        HistoricProcessInstance processInstance = historyService.createHistoricProcessInstanceQuery().processInstanceId(processId).singleResult();
//        // 根据流程对象获取流程对象模型
//        BpmnModel bpmnModel = repositoryService.getBpmnModel(processInstance.getProcessDefinitionId());

        BpmnModel bpmnModel = repositoryService.getBpmnModel(processId);
        // process中包含所有的节点信息,包括流程线
        // org.activiti.bpmn.model.Process  process = bpmnModel.getProcessById(processId);
        org.activiti.bpmn.model.Process process = bpmnModel.getProcesses().get(0);
        // 获取第一个节点信息
//        FlowElement startElement = process.getInitialFlowElement();
        // 获取全部的FlowElement（流元素）信息
        Collection<FlowElement> flowElements = process.getFlowElements();
        List<UserTask> userTaskList = new ArrayList<>();
        // 筛选流程中所有的UserTask任务节点
        for (FlowElement flowElement : flowElements) {
            if (flowElement instanceof UserTask) {
                UserTask userTask = ((UserTask) flowElement);
                userTaskList.add(userTask);
            }
        }
        return userTaskList;
    }

    /**
     * 获奖人员导出excel
     */
    @AutoLog(value = "获奖人员导出excel")
    @ApiOperation(value = "获奖人员导出excel")
    @RequestMapping(value = "/prizeExportXls")
    public ModelAndView prizeExportXls(HttpServletRequest request,
                                       HttpServletResponse response,
                                       OaDeclareUserEntity oaDeclareUserEntity) throws IOException {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // Step.1 组装查询条件
        if (oaDeclareUserEntity.getPrizeId() == null || "".equals(oaDeclareUserEntity.getPrizeId()))
            throw new RuntimeException("参数异常!");
        OaPrizeEntity prize = oaPrizeService.getById(oaDeclareUserEntity.getPrizeId());
        if (prize == null) throw new RuntimeException("奖项不存在!");
        List<Map<String, String>> users = oaDeclareUserService.exportUsers(oaDeclareUserEntity);

        // Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        if ("gqty".equals(prize.getMaterialUrl())) {
            List<OaPrizeGqty> pageList = new ArrayList<>();
            users.stream().forEach(e -> {
                OaPrizeGqty oaPrizeGqty = JSON.parseObject(JSON.toJSONString(e), OaPrizeGqty.class);
                pageList.add(oaPrizeGqty);
            });
            // 导出文件名称
            mv.addObject(NormalExcelConstants.FILE_NAME, "优秀团员申报汇总表");
            mv.addObject(NormalExcelConstants.CLASS, OaPrizeGqty.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("优秀团员申报汇总表", "导出人:" + sysUser.getRealname(), "导出信息"));
            mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        } else if ("gqtgb".equals(prize.getMaterialUrl())) {
            List<OaPrizeGqtgb> pageList = new ArrayList<>();
            users.stream().forEach(e -> {
                String flag = oaDeclareUserService.checkIsExcellentTzb(e.get("depId"));
                e.put("isExcellent", flag);
                OaPrizeGqtgb oaPrizeGqtgb = JSON.parseObject(JSON.toJSONString(e), OaPrizeGqtgb.class);
                pageList.add(oaPrizeGqtgb);
            });
            // 导出文件名称
            mv.addObject(NormalExcelConstants.FILE_NAME, "优秀共青团干部（优秀学生干部）申报汇总表");
            mv.addObject(NormalExcelConstants.CLASS, OaPrizeGqtgb.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("优秀共青团干部（优秀学生干部）申报汇总表", "导出人:" + sysUser.getRealname(), "导出信息"));
            mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        } else if ("tzb".equals(prize.getMaterialUrl())) {
            List<OaPrizeTzb> pageList = new ArrayList<>();
            users.stream().forEach(e -> {
                OaPrizeTzb OaPrizeTzb = JSON.parseObject(JSON.toJSONString(e), OaPrizeTzb.class);
                pageList.add(OaPrizeTzb);
            });
            // 导出文件名称
            mv.addObject(NormalExcelConstants.FILE_NAME, "优秀团支部申报汇总表");
            mv.addObject(NormalExcelConstants.CLASS, OaPrizeTzb.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("优秀团支部申报汇总表", "导出人:" + sysUser.getRealname(), "导出信息"));
            mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        } else if ("6ydxs".equals(prize.getMaterialUrl())) {
            List<OaPrize6y> pageList = new ArrayList<>();
            users.stream().forEach(p -> {
                OaPrize6y oaprize6y = JSON.parseObject(JSON.toJSONString(p), OaPrize6y.class);
                pageList.add(oaprize6y);
            });
            if (pageList.size() > 0) {
                for (int i = 0; i < pageList.size(); i++) {
                    pageList.get(i).setSort(i + 1);
                }
            }
            // 导出文件名称
            mv.addObject(NormalExcelConstants.FILE_NAME, prize.getTitle() + "汇总表");
            mv.addObject(NormalExcelConstants.CLASS, OaPrize6y.class);
            mv.addObject(NormalExcelConstants.PARAMS, new ExportParams(prize.getTitle() + "汇总表", "导出人:" + sysUser.getRealname(), "导出信息"));
            mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        }
        return mv;

    }

    @AutoLog(value = "奖项报名人员导出excel")
    @ApiOperation(value = "奖项报名人员导出excel")
    @RequestMapping(value = "/bmPersonExportXls")
//    @RequestMapping(value = "/list")
    public ModelAndView bmPersonExportXls(HttpServletRequest request,
                                          HttpServletResponse response,
                                          OaDeclareUserEntity oaDeclareUser) throws IOException {
        return oaDeclareUserService.bmPersonExportXls(oaDeclareUser);
    }


    @AutoLog(value = "奖项申报-一键驳回所有未审核完的报名流程")
    @ApiOperation(value = "奖项申报-一键驳回所有未审核完的报名流程", notes = "奖项申报-一键驳回所有未审核完的报名流程")
    @GetMapping(value = "/cancelUnfinished")
    public Result<?> cancelUnfinished(@RequestParam(value = "prizeId") String prizeId) {
        return oaDeclareUserService.cancelUnfinishedBmProcess(prizeId);
    }


    @AutoLog(value = "报名表单下载")
    @ApiOperation(value = "报名表单下载", notes = "报名表单下载")
    @PostMapping(value = "/exportBMToWord")
    public void downloadBmWord(HttpServletRequest request, HttpServletResponse response) throws IOException {
//    public ResponseEntity<?> downloadBmWord(@RequestParam(name = "prizeId")String prizeId,@RequestParam(name = "ids")String ids) throws IOException {

        String ids = request.getParameter("ids");
        String prizeId = request.getParameter("prizeId");
        String token = request.getParameter("key");
        if (oConvertUtils.isEmpty(token)) {
            throw new ZsxcBootException("非法请求，已拒绝访问！");
        }
        String userName = JwtUtil.getUsername(token);
        if (oConvertUtils.isEmpty(userName)) {
            throw new ZsxcBootException("非法请求，已拒绝访问！");
        }

        if (oConvertUtils.isEmpty(ids)) throw new ZsxcBootException("请至少选择一条记录");
        OaPrizeEntity prize = oaPrizeService.getById(prizeId);
        if (oConvertUtils.isEmpty(prize)) throw new ZsxcBootException("未找到对应奖项");
        List<String> idList = Arrays.stream(ids.split(",")).collect(Collectors.toList());

        oaDeclareUserService.downloadBmToWord(prize, idList, request, response);

    }

    @AutoLog(value = "奖项申报-点击申报时校验")
    @ApiOperation(value = "奖项申报-点击申报时校验", notes = "奖项申报-点击申报时校验")
    @GetMapping(value = "/declareCheck")
    public Result<?> declareCheck(@RequestParam(value = "prizeId") String prizeId) {
        return oaDeclareUserService.declareCheck(prizeId);
    }

}
