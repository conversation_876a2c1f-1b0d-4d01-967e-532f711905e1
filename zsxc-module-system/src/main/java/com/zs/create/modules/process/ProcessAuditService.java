package com.zs.create.modules.process;

import org.activiti.engine.task.Task;

/**
 * @ClassName suzheng
 * @Description TODO
 * @date 2022-12-08  10:08
 * @Version 1.0
 */
public interface ProcessAuditService {

    /**
     * 同一级审核人可相互查看已审核
     * @param taskId
     * @param businessKey
     * @param assignee
     * @param processInstanceId
     * @param auditNote
     */
    public void saveWorkflow(String taskId,String businessKey, String assignee,String processInstanceId,String auditNote);

    /**
     * 判断是否是流程最后一步
     * @param taskId
     * @return
     */
    public Boolean nextTaskIsEnd(String taskId);

    /**
     * 根据task 获取业务id
     * @param task
     * @return
     */
    public String getTaskBusinessKey(Task task);
}
