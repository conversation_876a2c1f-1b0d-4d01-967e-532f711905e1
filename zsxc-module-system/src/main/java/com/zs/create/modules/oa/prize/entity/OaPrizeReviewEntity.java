package com.zs.create.modules.oa.prize.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.zs.create.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 奖项评审
 * 
 * <AUTHOR>
 * @date 2021-01-19 11:01:56
 */
@Data
@TableName("oa_prize_review")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oa_prize_review对象", description="奖项评审")
public class OaPrizeReviewEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public static final String ZERO = "0";

	public static final String ONE = "1";

	/**
	 * 
	 */
	@TableId
	private String id;
	/**
	 * 标题
	 */
	@ApiModelProperty(value = "标题")
	private String title;
	/**
	 * 内容
	 */
	@ApiModelProperty(value = "内容")
	private String content;
	/**
	 * 奖项id
	 */
	@ApiModelProperty(value = "奖项id")
	private String prizeId;
	/**
	 * 奖项名字
	 */
	@ApiModelProperty(value = "奖项名字")
	@TableField(exist = false)
	private String prizeName;
	/**
	 * 评审方式：0 线下  1线上
	 */
	@ApiModelProperty(value = "0 线下  1线上")
	private String mode;
	/**
	 * 评审形式：0 投票  1 打分
	 */
	@ApiModelProperty(value = "0 投票  1 打分")
	private String type;
	/**
	 * 开始时间
	 */
	@ApiModelProperty(value = "开始时间")
	private Date st;
	/**
	 * 结束时间
	 */
	@ApiModelProperty(value = "结束时间")
	private Date et;
	/**
	 * 地点
	 */
	@ApiModelProperty(value = "地点")
	private String place;
	/**
	 * 最低分
	 */
	@ApiModelProperty(value = "最低分")
	private Integer minScore;
	/**
	 * 最高分
	 */
	@ApiModelProperty(value = "最高分")
	private Integer maxScore;
	/**
	 * 步进值
	 */
	@ApiModelProperty(value = "步进值")
	private Integer stepValues;
	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 数据状态标识 0 未开始 1 进行中 2 已结束
	 */
	@ApiModelProperty(value = "数据状态标识 0 未开始 1 进行中 2 已结束")
	@TableField(exist = false)
	private String status;
	/**
	 * 校内评委集合
	 */
	@ApiModelProperty(value = "校内评委集合")
	@TableField(exist = false)
	private List<OaPrizeJudgesEntity> judgesEntities;

	/**
	 * 是否选择 1：是 0：否（投票：是否展示票数，打分：是否去掉最高最低分）
	 */
	@ApiModelProperty(value = "投票：是否展示票数，打分：是否去掉最高最低分")
	private String options;

	/**
	 * 投票方式 ： 0 单选 1 多选
	 */
	@ApiModelProperty(value = "投票方式 ： 0 单选 1 多选")
	private Integer checkType;
	/**
	 * 最少选
	 */
	@ApiModelProperty(value = "最少选")
	private Integer checkMin;
	/**
	 * 最多选
	 */
	@ApiModelProperty(value = "最多选")
	private Integer checkMax;
	/**
	 * 是否计算平均分  0：否  1：是
	 */
	@ApiModelProperty(value = "是否计算平均分  0：否  1：是")
	private Integer averageType;
	/**
	 * 评审结果列表
	 */
	@ApiModelProperty(value = "评审结果列表")
	@TableField(exist = false)
	private List<OaReviewUserRatingEntity> oaReviewUserRatingEntities;


//	@ApiModelProperty(value = "打分项集合")
//	@TableField(exist = false)
//	private List<OaPrizeScoringEntity> scoringEntities;

	public String getStatus() {
		if (st==null||et==null) return status;
		Date data=new Date();
		if (data.compareTo(st)==-1){
			status="0";
		}else if (data.compareTo(st)==1
				&&data.compareTo(et)==-1){
			status="1";
		}else {
			status="2";
		}
		return status;
	}
}
