package com.zs.create.modules.communication.callcenter.service.impl;

import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.modules.communication.callcenter.constant.KfConstants;
import com.zs.create.modules.communication.callcenter.entity.CpKfUserEntity;
import com.zs.create.modules.communication.callcenter.repository.CpKfUserRepository;
import com.zs.create.modules.communication.callcenter.service.ICpKfUserService;
import com.zs.create.modules.system.entity.SysRole;
import com.zs.create.modules.system.entity.SysUserRole;
import com.zs.create.modules.system.service.ISysRoleService;
import com.zs.create.modules.system.service.ISysUserRoleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Example;
import org.springframework.data.domain.ExampleMatcher;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * @className: KfUserServiceImpl
 * @description: 客服人员service
 * @author: hy
 * @date: 2020-11-26
 **/
@Service
public class CpKfUserServiceImpl implements ICpKfUserService {
    @Autowired
    CpKfUserRepository kfUserRepository;
    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    ISysUserRoleService sysUserRoleService;
    @Autowired
    ISysRoleService sysRoleService;

    /**
     * 保存客服人员
     * @param kfUserEntity
     * @return
     */
    @Override
    @Transactional
    public CpKfUserEntity save(CpKfUserEntity kfUserEntity) {
        if(kfUserEntity.getStatus() == null) kfUserEntity.setStatus(0);
        if(StringUtils.isEmpty(kfUserEntity.getId())){
            //新增时 userId不可重复
            CpKfUserEntity one = new CpKfUserEntity().setUserId(kfUserEntity.getUserId());
            Example<CpKfUserEntity> kfUserEntityExample = Example.of(one);
            Optional<CpKfUserEntity> optional = kfUserRepository.findOne(kfUserEntityExample);
            if(optional.isPresent() )  throw new ZsxcBootException("此客服已添加,不可重复添加");

            CpKfUserEntity one2 = new CpKfUserEntity().setNickName(kfUserEntity.getNickName());
            Example<CpKfUserEntity> kfUserEntityExample2 = Example.of(one2);
            Long repeatKfNickNameNum = kfUserRepository.count(kfUserEntityExample2);
            if(repeatKfNickNameNum>0 )  throw new ZsxcBootException("此客服昵称重复");
            kfUserEntity.setId(SnowIdUtils.uniqueLongHex());
        }


        //删除单个客服的hashKey
        redisTemplate.opsForHash().delete(CpKfUserEntity.KF_USER_HASH_KEY , kfUserEntity.getUserId());
        CpKfUserEntity one = kfUserRepository.save(kfUserEntity);
        if(one == null) throw new ZsxcBootException("保存客服信息失败");

        this.save2KfRole(one.getUserId());
        try {
            Thread.sleep(1000);// 延迟双删
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        redisTemplate.opsForHash().delete(CpKfUserEntity.KF_USER_HASH_KEY , kfUserEntity.getUserId());
        return one;
    }

    @Transactional
    public Boolean save2KfRole(String userId){
        //添加到客服角色里面
        SysRole kfRole = sysRoleService.getRoleByRoleCode("role_kfry");
        if(null == kfRole) throw new ZsxcBootException("客服人员角色不存在,请添加客服人员");
        Map delMap = new HashMap(2);
        delMap.put("user_id" , userId);
        delMap.put("role_id" , kfRole.getId());
        sysUserRoleService.removeByMap(delMap);
        SysUserRole sysUserRole = new SysUserRole().setUserId(userId).setRoleId(kfRole.getId());
        sysUserRoleService.saveRoleUser(sysUserRole);
        return Boolean.TRUE;
    }


    @Transactional
    public Boolean delKfRole(String userId){
        //添加到客服角色里面
        SysRole kfRole = sysRoleService.getRoleByRoleCode("role_kfry");
        if(null == kfRole) throw new ZsxcBootException("客服人员角色不存在,请添加客服人员");
        Map delMap = new HashMap(2);
        delMap.put("user_id" , userId);
        delMap.put("role_id" , kfRole.getId());
        sysUserRoleService.removeByMap(delMap);
        return Boolean.TRUE;
    }

    /**
     * 根据id查询
     * @param id
     * @return
     */
    @Override
    public CpKfUserEntity findById(String id){
            Optional<CpKfUserEntity> optional = kfUserRepository.findById(id);
            if(optional.isPresent()) return optional.get();
            return null;
    }

    /**
     * 根据userId查询
     * @param userId
     * @return
     */
    @Override
    public CpKfUserEntity findByUserId(String userId) {
        CpKfUserEntity one = (CpKfUserEntity)redisTemplate.opsForHash()
                .get(CpKfUserEntity.KF_USER_HASH_KEY , userId);
        if(null != one) return one;
        CpKfUserEntity kfUserEntity = new CpKfUserEntity().setUserId(userId);
        Example<CpKfUserEntity> kfUserEntityExample = Example.of(kfUserEntity);
        Optional<CpKfUserEntity> optional = kfUserRepository.findOne(kfUserEntityExample);
        if(optional.isPresent()) {
            CpKfUserEntity user = optional.get();
            this.listKfUsers(); //续上缓存
            return user;
        }
        return null;
    }


    /**
     * 根据id删除
     * @param id
     */
    @Override
    public void delete(String id) {
        CpKfUserEntity one  = this.findById(id);
        if(null == one) throw new ZsxcBootException("id 参数错误");
        redisTemplate.opsForHash().delete(CpKfUserEntity.KF_USER_HASH_KEY , one.getUserId());
        redisTemplate.opsForSet().remove(KfConstants.WEBSOCKET_USER_ONLINE_SET , one.getUserId()+ "::kf");
        kfUserRepository.deleteById(id);
        delKfRole(one.getUserId());
        try {
            Thread.sleep(500);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        redisTemplate.opsForHash().delete(CpKfUserEntity.KF_USER_HASH_KEY , one.getUserId());
        redisTemplate.opsForSet().remove(KfConstants.WEBSOCKET_USER_ONLINE_SET , one.getUserId()+ "::kf");

    }

    /**
     * 查询所有客服
     * @return
     */
    @Override
    public List<CpKfUserEntity> listKfUsers() {
        Map kfUserMap = redisTemplate.opsForHash().entries(CpKfUserEntity.KF_USER_HASH_KEY);
        if(!CollectionUtils.isEmpty(kfUserMap)) { //缓存不为空
            List<CpKfUserEntity> kfList = new ArrayList<>();
            for(Object obj : kfUserMap.values()){
                if(null != obj){
                    kfList.add((CpKfUserEntity) obj);
                }
            }
            return kfList;
        }
        //缓存为空
        List<CpKfUserEntity> allKfUser = kfUserRepository.findAll();
        if(!CollectionUtils.isEmpty(allKfUser)){
            for(CpKfUserEntity kfUserEntity : allKfUser){
                redisTemplate.opsForHash().put(CpKfUserEntity.KF_USER_HASH_KEY , kfUserEntity.getUserId() , kfUserEntity);
            }
            redisTemplate.expire(CpKfUserEntity.KF_USER_HASH_KEY , CpKfUserEntity.KF_USER_HASH_EXPIRE_DAY , TimeUnit.DAYS);
        }
        return allKfUser;
    }

    @Override
    public Page<CpKfUserEntity> findAll(Pageable pageable, CpKfUserEntity kfUserEntity) {
        ExampleMatcher exampleMatcher = ExampleMatcher.matching()
                .withMatcher("nickName", ExampleMatcher.GenericPropertyMatchers.contains())
            .withMatcher("userName", ExampleMatcher.GenericPropertyMatchers.contains());
        Example example = Example.of(kfUserEntity , exampleMatcher);
        return kfUserRepository.findAll(example , pageable );
    }



    @Override
    public String getOnlineRandomKfSessionId(){
       return (String)redisTemplate.opsForSet().randomMember(KfConstants.KF_USER_ONLINE_SET);
    }

    @Override
    public String getOffLineRandomKfUserId() {
        List<CpKfUserEntity> kfUsers = this.listKfUsers();
        if(CollectionUtils.isEmpty(kfUsers)) return null;
        int size = kfUsers.size();
        Random random = new Random();
        int rand = random.nextInt(size);
        return kfUsers.get(rand).getUserId();
    }

}
