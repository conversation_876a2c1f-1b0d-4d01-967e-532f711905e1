<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.weixin.mapper.SysWeixinDeployMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.weixin.entity.SysWeixinDeployEntity" id="sysWeixinDeployMap">
        <result property="id" column="id"/>
        <result property="url" column="url"/>
        <result property="name" column="name"/>
        <result property="subName" column="sub_name"/>
        <result property="path" column="path"/>
        <result property="type" column="type"/>
        <result property="baseId" column="base_id"/>
        <result property="sort" column="sort"/>
        <result property="urlType" column="url_type"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>