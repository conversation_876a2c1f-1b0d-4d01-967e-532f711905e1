<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.item.mapper.ScItemDevolutionMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.item.entity.ScItemDevolutionEntity" id="scItemDevolutionMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="username" column="username"/>
        <result property="realname" column="realname"/>
        <result property="itemId" column="item_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


</mapper>