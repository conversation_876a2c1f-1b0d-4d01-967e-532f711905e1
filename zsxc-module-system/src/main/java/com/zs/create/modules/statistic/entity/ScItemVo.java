package com.zs.create.modules.statistic.entity;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;


import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_item导出对象", description="项目")
public class ScItemVo implements Serializable{
    private static final long serialVersionUID = 1L;
    /**
     * 项目名称
     */
    @Excel(name = "项目名称",width = 30)
    @ApiModelProperty(value = "项目名称")
    private String itemName;

    /**
     * 项目模块 数据字段中配置
     */
    @Excel(name = "项目模块",replace = {"德_d","智_z","体_t","美_m","劳_l"},width = 10)
    private String module;

    /**
     * 项目形式 数据字典中配置
     */
    @Excel(name = "项目形式",width = 20,replace = {"现场参与_0","提交作品_1"})
    private String form;

    /**
     * 项目级别 见字典
     */
    @Excel(name = "项目级别",width = 20,replace = {"班级_class","院级_college","校级_school","省级_provincial","国家级_country","国际级_international"})
    @ApiModelProperty(value = "项目级别")
    private String activityLevel;

    /**
     *学时
     */
    @Excel(name = "学时",width = 20)
    @ApiModelProperty(value = "学时")
    private BigDecimal serviceHour;

    /**
     * 业务申请单位id
     */
    @ApiModelProperty(value = "业务所属单位")
    private String businessDeptId;

    /**
     * 业务申请单位名称
     */
    @Excel(name = "组织方",width = 30)
    private String departName;

    /**
     * 联系人
     */
    @Excel(name = "联系人",width = 20)
    @ApiModelProperty(value = "联系人")
    private String linkMan;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间",exportFormat = "yyyy-MM-dd HH:mm:ss",width = 30)
    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    /**
     * 审核状态
     * "40 结项  -3异常结项 35 成绩驳回 34 成绩审核通过 " +
     " 33  成绩申请中  30 学时已确认 10为发布 " +
     ", 8为补充信息 ,  6为立项，1为申请中 ，-1为驳回  -2暂存  -4驳回不可修改"
     */
    @Excel(name = "状态",width = 20,replace = {"结项_40","异常结项_-3","成绩驳回_35","成绩审核通过_34" +
    "成绩申请中_33","学时已确认_30","发布_10","补充信息_8","立项_6","申请中_1","驳回_-1","暂存_-2","驳回不可修改_-4"})
    private Integer examineStatus;
}
