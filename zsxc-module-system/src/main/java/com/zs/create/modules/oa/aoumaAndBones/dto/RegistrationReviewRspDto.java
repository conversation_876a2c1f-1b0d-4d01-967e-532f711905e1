package com.zs.create.modules.oa.aoumaAndBones.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName suzheng
 * @Description TODO
 * @date 2022-11-09  20:22
 * @Version 1.0
 */
@Data
public class RegistrationReviewRspDto implements Serializable {
    private static final long serialVersionUID = 6620959008487054306L;

    /**
     * 报名id
     */
    private String id;

    /**
     * 学生学号
     */
    @ApiModelProperty(value = "学生学号")
    private String userId;
    /**
     * 学生姓名
     */
    @ApiModelProperty(value = "学生姓名")
    private String userName;
    /**
     * 班级id
     */
    @ApiModelProperty(value = "班级id")
    private String classId;
    /**
     * 班级名称
     */
    @ApiModelProperty(value = "班级名称")
    private String className;
    /**
     * 提交时间
     */
    @ApiModelProperty(value = "提交时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date submitTime;
    /**
     * 性别
     */
    @ApiModelProperty(value = "性别")
    private String sex;
    /**
     * 手机号
     */
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     * QQ
     */
    @ApiModelProperty(value = "QQ")
    private String qq;
    /**
     * 政治面貌
     */
    @ApiModelProperty(value = "政治面貌")
    private String politic;
    /**
     * 入党时间
     */
    @ApiModelProperty(value = "入党时间")
    private String applicationTime;
    /**
     * 现任职务
     */
    @ApiModelProperty(value = "现任职务")
    private String post;
    /**
     * -1暂存，0未审核（申请中），1审核中（申请中），2已审核，3不推荐报名，4修改再提交，5公式中，6公式已结束，7已报名，8已结业
     */
    @ApiModelProperty(value = "-1暂存，0未审核（申请中），1审核中（申请中），2已审核，3不推荐报名，4修改再提交，5公式中，6公式已结束，7已报名，8已结业")
    private Integer applyStatus;
    /**
     * 流程实例id
     */
    @ApiModelProperty(value = "流程实例id")
    private String processInstanceId;
    /**
     * 报名状态
     */
    @TableField(exist = false)
    private String applyStatusName;
    /**
     * 任务id
     */
    @ApiModelProperty(value = "任务id")
    private String taskId;
    /**
     * 任务名称
     */
    @ApiModelProperty(value = "任务名称")
    private String taskName;
    /**
     * 任务处理人
     */
    @ApiModelProperty(value = "任务处理人")
    private String assignee;
    /**
     * 学院
     */
    @ApiModelProperty(value = "学院")
    private String college;
    /**
     * 班级选择
     */
    @ApiModelProperty(value = "班级选择")
    private String classChoice;

}
