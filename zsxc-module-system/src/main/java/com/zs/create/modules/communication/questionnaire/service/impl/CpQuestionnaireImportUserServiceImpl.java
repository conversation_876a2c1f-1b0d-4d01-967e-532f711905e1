package com.zs.create.modules.communication.questionnaire.service.impl;

import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.communication.questionnaire.entity.CpQuestionnaireImportUserEntity;
import com.zs.create.modules.communication.questionnaire.mapper.CpQuestionnaireImportUserMapper;
import com.zs.create.modules.communication.questionnaire.service.CpQuestionnaireImportUserService;
import com.zs.create.modules.communication.questionnaire.util.QuestionUserImportHandler;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.service.ISysUserService;
import com.zs.create.modules.system.util.ExcelStyleUtil;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.DataFormat;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.jeecgframework.poi.excel.ExcelExportUtil;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.CollectionUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * @Description 问卷报名导入人员Service实现层
 *
 * <AUTHOR> @email 
 * @date 2023-03-22 15:07:20
 * @Version: V1.0
 */
@Service
public class CpQuestionnaireImportUserServiceImpl extends ServiceImpl<CpQuestionnaireImportUserMapper, CpQuestionnaireImportUserEntity> implements CpQuestionnaireImportUserService {

    @Autowired
    private ISysUserService userService;

    @Override
    public OutputStream exportXls(HttpServletResponse response) {
        Result<String> result = new Result<>();
        List<ExcelExportEntity> entity = new ArrayList<>();
        entity.add(new ExcelExportEntity("学/工号", "username"));
        Collection<Map<String, Object>> dataList = new ArrayList<Map<String, Object>>();

        Map<String, Object> map = new LinkedHashMap<>();
        map.put("username", null);
        dataList.add(map);
        response.setContentType("application/OCTET-STREAM;charset=UTF-8");

        String filename = new String(("人员导入模板" + System.currentTimeMillis() + ".xls").getBytes(), StandardCharsets.ISO_8859_1);
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        OutputStream out = null;
        try {
            out = response.getOutputStream();
            ExportParams ep = new ExportParams(
                    "人员导入模板"
                    , "");
            ep.setStyle(ExcelStyleUtil.class);

            Workbook workbook = ExcelExportUtil.exportExcel(ep, entity, dataList);

            /**设置单元格格式为文本格式*/
            CellStyle cellStyle = workbook.createCellStyle();
            DataFormat dataFormat = workbook.createDataFormat();
            cellStyle.setDataFormat(dataFormat.getFormat("@"));
            Sheet sheet = workbook.getSheetAt(0);
            sheet.setDefaultColumnStyle(0, cellStyle);//设置首列格式为"文本"
            //自适应表头长度
            for (ExcelExportEntity excelExportEntity : entity) {
                for (int i = 0; i < excelExportEntity.getName().length(); i++) {
                    sheet.autoSizeColumn(i);
                    sheet.setColumnWidth(i, sheet.getColumnWidth(i) * 17 / 10);
                }
            }
            workbook.write(out);
            return out;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (null != out)
                    out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return out;
    }


    @Override
    public Result<String> importExcel(InputStream in) {
        Result<String> result = new Result<>();
        Map<String, Object> fieldMap = new HashMap<>();
        fieldMap.put("username", "学/工号");
        ImportParams params = new ImportParams();
        params.setTitleRows(1); //表格标题行数
        params.setHeadRows(1); // 表头行数
        params.setDataHanlder(new QuestionUserImportHandler(fieldMap));
        int successCnt = 0;
        //错误数据记录集合
        List<String> errors = new LinkedList<>();
        List<Map<String, Object>> list = null;
        try {
            list = ExcelImportUtil.importExcel(in,  Map.class, params);
        } catch (Exception e) {
            e.printStackTrace();
            throw new ZsxcBootException("文件的格式不正确,请使用所下载的模板上传数据！！");
        }
        if (CollectionUtils.isEmpty(list)) throw new ZsxcBootException("未匹配到任何数据...");
        int nullContent = 0;
        String massge = "";
        //同一次导入同一个人是否存在两条数据
        List<String> useridExist=new ArrayList<>();
        ArrayList<CpQuestionnaireImportUserEntity> cpQuestionnaireImportUserEntities=new ArrayList<>();
        String key = UUID.randomUUID().toString().replaceAll("-", "");
        for (int i = 0; i < list.size(); i++) {
            try {
                Map<String, Object> userMap = list.get(i);
                String username =(String) userMap.get("username");
                if ((username == null || (username.trim().length() == 0))) {
                    nullContent++;
                    continue;
                }
                if (useridExist.contains(username)){
                    continue;
                }
                SysUser students = userService.getUserByName(username);
                if (null == students) {
                    massge = ("第" + (i + 3) + "行，学/工号不存在；");
                    errors.add(massge);
                } else {
                    CpQuestionnaireImportUserEntity userEntity=new CpQuestionnaireImportUserEntity();
                    userEntity.setUuidKey(key);
                    userEntity.setUserId(username);
                    userEntity.setUserName(students.getRealname());
                    successCnt++;
                    useridExist.add(username);
                    cpQuestionnaireImportUserEntities.add(userEntity);
                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        this.saveBatch(cpQuestionnaireImportUserEntities);
        result.success("共" + (list.size() - nullContent) + "条,成功导入：" + successCnt + "条,导入失败："
                + errors.size() + "条"
                + (CollectionUtils.isEmpty(errors) ? "" :
                ",错误原因：" + String.join("", errors)));
        result.setResult(key);
        return result;
    }
}
