package com.zs.create.modules.workflow.gateway.service.impl;

import com.zs.create.modules.workflow.common.utils.SpringUtils;
import com.zs.create.modules.workflow.gateway.entity.GateWayConfig;
import com.zs.create.modules.workflow.gateway.service.IGateWayConfigService;
import org.activiti.engine.RepositoryService;
import org.activiti.engine.repository.ProcessDefinition;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR> guodl
 * @Date: 2019/8/9 16:43
 * @Description:网关条件接口
 */
@Service("gateWayConfigService")
public class GateWayConfigService implements IGateWayConfigService {
    @Autowired
    JdbcTemplate jdbcTemplate;
    RepositoryService repositoryService;

    public void init() {
        if (null == repositoryService) {
            repositoryService = SpringUtils.getApplicationContext().getBean(RepositoryService.class);
        }
    }

    @Override
    public List<GateWayConfig> list(String deploymentId, String gateWayId, String gateWayType) {
        StringBuffer sql = new StringBuffer(" SELECT "
                +
                "   id,deployment_id ,gateway_id ,gateway_type , target_node_id ,condition_  FROM  `act_comm_gateway_config` "
                +
                "    WHERE  del_flag=0 and deployment_id=? and gateway_id=?   and gateway_type=?");
        List<GateWayConfig> configs = jdbcTemplate.query(sql.toString(),
                    new Object[]{deploymentId, gateWayId, gateWayType}, new GateWayConfig());
        return configs;
    }

    @Override
    public List<GateWayConfig> listByProcessDefinitionId(String processDefinitionId, String gateWayId, String gateWayType) {
        init();
        ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                    .processDefinitionId(processDefinitionId).singleResult();
        List<GateWayConfig> configs = this.list(processDefinition.getDeploymentId(), gateWayId, gateWayType);
        return configs;
    }


}
