package com.zs.create.modules.communication.suggest.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zs.create.modules.communication.suggest.entity.CpSuggestEntity;
import com.zs.create.modules.communication.suggest.entity.CpSuggestVo;
import com.zs.create.modules.system.entity.SysRole;
import com.zs.create.modules.system.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * @Description 投诉建议Mapper层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-23 09:51:39
 * @Version: V1.0
 */
public interface CpSuggestMapper extends BaseMapper<CpSuggestEntity> {

   SysRole getUserByRoleCode( @Param("roleCode") String roleCode,  @Param("userId") String  userId);

   List<SysUser> getUserListByRoleCode(@Param("roleCode") String roleCode);

   List<SysUser> getPersonInChargeList(@Param("orgCode") String orgCode,@Param("realname") String realname);

   List<CpSuggestVo> queryGzSuggest();

   List<CpSuggestVo> queryCaSuggest();

   List<CpSuggestVo> queryZaSuggest();
}
