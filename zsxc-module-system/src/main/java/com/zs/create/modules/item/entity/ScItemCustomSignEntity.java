package com.zs.create.modules.item.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 自定义报名范围
 * 
 * <AUTHOR>
 * @email null
 * @date 2020-11-05 10:04:36
 */
@Data
@TableName("sc_item_custom_sign")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_item_custom_sign对象", description="自定义报名范围")
public class ScItemCustomSignEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 
	 */
	@TableId
	private String id;
	/**
	 * 项目id
	 */
	private String itemId;
	/**
	 * 用户id
	 */
	private String username;
	/**
	 * 学生姓名
	 */
	private String realname;
	/**
	 * 学生姓名
	 */
	private String 	uuidKey;
	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;

}
