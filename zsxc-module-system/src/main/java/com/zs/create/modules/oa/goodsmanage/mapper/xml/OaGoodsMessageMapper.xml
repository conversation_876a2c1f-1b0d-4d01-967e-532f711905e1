<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.goodsmanage.mapper.OaGoodsMessageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.goodsmanage.entity.OaGoodsMessageEntity" id="oaGoodsMessageMap">
        <result property="id" column="id"/>
        <result property="goodsName" column="goods_name"/>
        <result property="goodsNum" column="goods_num"/>
        <result property="surplusNum" column="surplus_num"/>
        <result property="goodsDescribe" column="goods_describe"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="delFlag" column="del_flag"/>
        <result property="personInChargeNo" column="person_in_charge_no"/>
        <result property="personInChargeName" column="person_in_charge_name"/>
        <result property="isUse" column="is_use"/>
    </resultMap>


</mapper>