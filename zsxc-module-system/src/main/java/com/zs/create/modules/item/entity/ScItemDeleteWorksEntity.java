package com.zs.create.modules.item.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 项目作品删除记录
 *
 * <AUTHOR> @email
 * @date 2024-08-07 10:58:55
 */
@Data
@TableName("sc_item_delete_works")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "sc_item_delete_works对象", description = "项目作品删除记录")
public class ScItemDeleteWorksEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "id")
    private String id;
    /**
     * 项目id
     */
    @ApiModelProperty(value = "项目id")
    private String itemId;
    /**
     * 项目名称
     */
    @ApiModelProperty(value = "项目名称")
    private String itemName;
    /**
     * 学工号
     */
    @ApiModelProperty(value = "学工号")
    private String userCode;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;
    /**
     * 创建人
     */
    @ApiModelProperty(value = "创建人")
    private String createBy;
    /**
     * 创建时间
     */
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 修改人
     */
    @ApiModelProperty(value = "修改人")
    private String updateBy;
    /**
     * 修改时间
     */
    @ApiModelProperty(value = "修改时间")
    @JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;
    /**
     * 删除原因(1 作品质量不合格, 2恶意参与)
     */
    @ApiModelProperty(value = "删除原因(1 作品质量不合格, 2恶意参与)")
    private Integer deleteReasonType;
    /**
     * 附件名称集
     */
    @TableField(exist = false)
    @ApiModelProperty(value = "附件名称集")
    private List<ScItemWorksEntity> scItemWorksVo;
}
