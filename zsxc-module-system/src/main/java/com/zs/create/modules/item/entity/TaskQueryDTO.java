package com.zs.create.modules.item.entity;

/**
 * @createUser hy
 * @createTime 2020-6-30
 * @description 任务查询dto
 */

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="任务查询DTO", description="任务查询")
public class TaskQueryDTO implements Serializable {
    public static final String QUERY_TODO = "todo";
    public static final String QUERY_HISTORY = "history";
    @ApiModelProperty(value = "项目名称")
    private String itemName;
    @ApiModelProperty(value = "项目模块")
    private String itemModule;
    @ApiModelProperty(value = "项目形式")
    private String itemForm;
    /*@ApiModelProperty(value = "项目类别")
    private String itemType ;*/
    @ApiModelProperty(value = "任务处理人")
    private String assignee;
    @ApiModelProperty(value = "查询类型 ： todo ： 代办   history ：  已办")
    private String queryType;
}
