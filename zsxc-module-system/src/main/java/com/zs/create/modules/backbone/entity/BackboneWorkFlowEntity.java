package com.zs.create.modules.backbone.entity;

import java.io.Serializable;
import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 流程记录表
 *
 * <AUTHOR> @email
 * @date 2022-06-07 08:42:23
 */
@Data
@TableName("backbone_work_flow")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "backbone_work_flow对象", description = "流程记录表")
public class BackboneWorkFlowEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(type = IdType.ID_WORKER_STR)
    @ApiModelProperty(value = "主键")
    private String id;
    /**
     * 申请主表id
     */
    @ApiModelProperty(value = "申请主表id")
    private String applyId;

    /**
     * 被驳回人工号
     */
    @ApiModelProperty(value = "被驳回人工号")
    private String userId;

    /**
     * 驳回的时间
     */
    @ApiModelProperty(value = "驳回的时间")
    private Date time;

    /**
     * 流程负责人
     */
    @ApiModelProperty(value = "流程负责人")
    private String chargeId;

    /**
     * 状态:0为驳回,1为申请
     */
    @ApiModelProperty(value = "状态:0为驳回,1为申请")
    private int status;
}
