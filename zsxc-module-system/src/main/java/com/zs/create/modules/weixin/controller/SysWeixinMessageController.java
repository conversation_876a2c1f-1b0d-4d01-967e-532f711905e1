package com.zs.create.modules.weixin.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.weixin.entity.SysWeixinMessageEntity;
import com.zs.create.modules.weixin.service.SysWeixinMessageService;
import java.util.Date;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * @Description 微信管理Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-04-22 11:45:26
 * @Version: V1.0
 */
@Slf4j
@Api(tags="微信管理-消息回复配置")
@RestController
@RequestMapping("/weixin/sysWeixinMessage")
public class SysWeixinMessageController {
    @Autowired
    private SysWeixinMessageService sysWeixinMessageService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "微信管理-消息回复配置-分页列表查询")
    @ApiOperation(value="微信管理-消息回复配置-分页列表查询", notes="微信管理-消息回复配置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<SysWeixinMessageEntity>> queryPageList(SysWeixinMessageEntity sysWeixinMessage,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<SysWeixinMessageEntity>> result = new Result<IPage<SysWeixinMessageEntity>>();
        QueryWrapper<SysWeixinMessageEntity> queryWrapper = QueryGenerator.initQueryWrapper(sysWeixinMessage, req.getParameterMap());
        Page<SysWeixinMessageEntity> page = new Page<SysWeixinMessageEntity>(pageNo, pageSize);
        IPage<SysWeixinMessageEntity> pageList = sysWeixinMessageService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "微信管理-消息回复配置-添加")
    @ApiOperation(value="微信管理-消息回复配置-添加", notes="微信管理-消息回复配置-添加")
    @PostMapping(value = "/add")
    public Result<SysWeixinMessageEntity> add(@RequestBody SysWeixinMessageEntity sysWeixinMessage) {
            Result<SysWeixinMessageEntity> result = new Result<SysWeixinMessageEntity>();
            sysWeixinMessageService.save(sysWeixinMessage);
            result.success("添加成功！");
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "微信管理-消息回复配置-编辑")
    @ApiOperation(value="微信管理-消息回复配置-编辑", notes="微信管理-消息回复配置-编辑")
    @PutMapping(value = "/edit")
    public Result<SysWeixinMessageEntity> edit(@RequestBody SysWeixinMessageEntity sysWeixinMessage) {
        Result<SysWeixinMessageEntity> result = new Result<SysWeixinMessageEntity>();
        SysWeixinMessageEntity sysWeixinMessageEntity = sysWeixinMessageService.getById(sysWeixinMessage.getId());
        if(sysWeixinMessageEntity==null) {
           return result.error500("未找到对应实体");
        }else {
            boolean ok = sysWeixinMessageService.updateById(sysWeixinMessage);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "微信管理-消息回复配置-通过id删除")
    @ApiOperation(value="微信管理-消息回复配置-通过id删除", notes="微信管理-消息回复配置-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
            sysWeixinMessageService.removeById(id);
		    return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "微信管理-消息回复配置-批量删除")
    @ApiOperation(value="微信管理-消息回复配置-批量删除", notes="微信管理-消息回复配置-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<SysWeixinMessageEntity> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<SysWeixinMessageEntity> result = new Result<SysWeixinMessageEntity>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.sysWeixinMessageService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "微信管理-消息回复配置-通过id查询")
    @ApiOperation(value="微信管理-消息回复配置-通过id查询", notes="微信管理-消息回复配置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<SysWeixinMessageEntity> queryById(@RequestParam(name="id",required=true) String id) {
        Result<SysWeixinMessageEntity> result = new Result<SysWeixinMessageEntity>();
        SysWeixinMessageEntity sysWeixinMessage = sysWeixinMessageService.getById(id);
        if(sysWeixinMessage==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(sysWeixinMessage);
            result.setSuccess(true);
        }
        return result;
    }

}
