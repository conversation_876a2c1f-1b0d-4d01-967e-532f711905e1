package com.zs.create.modules.workflow.common.entity;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Auther: guodl
 * @Date: 2019/8/13 15:38
 * @Description:异常动作
 */
@Data
@NoArgsConstructor
public class BpmnYiCHangAction {
    /**
     * 主键ID
     */
    private Integer id;
    /**
     * 流程部署ID
     */
    private String deploymentId;
    /**
     * 当前任务节点ID
     */
    private String taskNodeId;
    /**
     * 驳回或者跳转的节点ID
     */
    private String targetNodeId;
    /**
     * 驳回或者跳转的节点名称
     */
    private String targetNodeName;
    /**
     * 动作类型
     */
    private String actionType;
    /**
     * 驳回或者跳转条件
     */
    private String condition;
}
