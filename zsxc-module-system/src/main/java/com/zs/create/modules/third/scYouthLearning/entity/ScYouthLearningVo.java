package com.zs.create.modules.third.scYouthLearning.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 青年大学习
 *
 * <AUTHOR> @email
 * @date 2022-05-09 16:19:37
 */
@Data
@Accessors(chain = true)
public class ScYouthLearningVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 姓名
     */
    @Excel(name = "姓名",width = 15)
    @ApiModelProperty(value = "姓名")
    private String username;
    /**
     * 性别:1男,2女,3未知
     */
    @Excel(name = "性别",width = 15,replace = {"男_1","女_2","未知_0"})
    @ApiModelProperty(value = "性别:1男,2女,3未知")
    private String gender;
    /**
     * 手机号
     */
    @Excel(name = "手机号",width = 15)
    @ApiModelProperty(value = "手机号")
    private String mobile;
    /**
     *
     */
    @Excel(name = "直属高校",width = 15)
    @ApiModelProperty(value = "直属高校")
    private String university;
    /**
     *
     */
    @Excel(name = "学校",width = 18)
    @ApiModelProperty(value = "学校")
    private String school;
    /**
     *
     */
    @Excel(name = "学院",width = 30)
    @ApiModelProperty(value = "学院")
    private String college;
    /**
     *
     */
    @Excel(name = "团支部",width = 35)
    @ApiModelProperty(value = "班级")
    private String classes;
    /**
     * 分数
     */
    @Excel(name = "分数",width = 10)
    @ApiModelProperty(value = "分数")
    private String score;
    /**
     * 学习时间
     */
    @Excel(name = "学习时间",width = 20,format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "学习时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date studytime;
    /**
     * 期数中文
     */
    @Excel(name = "期数",width = 15)
    @ApiModelProperty(value = "期数中文")
    private String qishu;
    /**
     * 创建时间
     */
    @Excel(name = "导入日期",width = 20,format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "导入日期")
    private Date createTime;
    /**
     * 0-未转化,  1-已转化
     */
    @Excel(name = "是否转化" ,width = 15,replace = {"未转化_0","已转化_1"})
    @ApiModelProperty(value = "0-未转化,1-已转化")
    private String dataType;

     /**
     * 学号
     */
     @Excel(name = "学号",width = 15)
    @ApiModelProperty(value = "学号")
    private String studentCode;

    /**
     * 年度
     */
    @Excel(name = "年度",width = 15)
    @ApiModelProperty(value = "年度")
    private String years;

    /**
     * 期数数字
     */
    @Excel(name = "期数数字",width = 15)
    @ApiModelProperty(value = "期数数字")
    private String periods;

    /**
     * 期数日期组合
     */
    @Excel(name = "期数日期组合",width = 15)
    @ApiModelProperty(value = "期数日期组合")
    private String times;

    /**
     * 微信id
     */
    @Excel(name = "微信id",width = 35)
    @ApiModelProperty(value = "微信id")
    private String openId;


}
