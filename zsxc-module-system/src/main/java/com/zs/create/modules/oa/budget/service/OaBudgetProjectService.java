package com.zs.create.modules.oa.budget.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.oa.budget.entity.OaBudgetProjectEntity;

/**
 * @Description 预算Service层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-18 10:10:04
 * @Version: V1.0
 */
public interface OaBudgetProjectService extends IService<OaBudgetProjectEntity> {

    /**
     * oa项目发起
     * @param id
     * @return
     */
    Boolean launchOaBudegetProject(Long id);

    Boolean cancleLaunchOaBudegetProject(Long id);

}

