package com.zs.create.modules.oa.practice.controller;


import java.util.*;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.StringKit;
import com.zs.create.common.util.oConvertUtils;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.item.entity.ItemAuditDTO;
import com.zs.create.modules.oa.practice.constant.PracticeTeamConstant;
import com.zs.create.modules.oa.practice.dto.PracticeVerifyReqDto;
import com.zs.create.modules.oa.practice.entity.OaPracticeFileEntity;
import com.zs.create.modules.oa.practice.entity.OaPracticePublicEntity;
import com.zs.create.modules.oa.practice.entity.OaPracticeTeamEntity;
import com.zs.create.modules.oa.practice.entity.OaPracticeTeamUserEntity;
import com.zs.create.modules.oa.practice.service.OaPracticeFileService;
import com.zs.create.modules.oa.practice.service.OaPracticePublicService;
import com.zs.create.modules.oa.practice.service.OaPracticeTeamService;
import com.zs.create.modules.oa.practice.service.OaPracticeTeamUserService;
import lombok.extern.slf4j.Slf4j;

import org.activiti.engine.runtime.ProcessInstance;
import org.apache.ibatis.annotations.Param;

import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR> @Description 团队上传附件信息Controller层
 * @email
 * @date 2023-01-11 14:59:00
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "团队上传附件信息")
@RestController
@RequestMapping("/oa/oaPracticeFile")
public class OaPracticeFileController {
    @Autowired
    private OaPracticeFileService oaPracticeFileService;
    @Autowired
    private OaPracticeTeamService oaPracticeTeamService;
    @Autowired
    private OaPracticeTeamUserService oaPracticeTeamUserService;
    @Autowired
    private OaPracticePublicService oaPracticePublicService;
    @Autowired
    private CampusAppService campusAppService;
    /**
     * 分页列表查询
     */
    @AutoLog(value = "团队上传附件信息-分页列表查询")
    @ApiOperation(value = "团队上传附件信息-分页列表查询", notes = "团队上传附件信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<OaPracticeFileEntity>> queryPageList(OaPracticeFileEntity oaPracticeFile,
                                                             @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                             @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                             HttpServletRequest req) {
        Result<IPage<OaPracticeFileEntity>> result = new Result<IPage<OaPracticeFileEntity>>();
        QueryWrapper<OaPracticeFileEntity> queryWrapper = new QueryWrapper();
        // 自行构造查询参数
        Page<OaPracticeFileEntity> page = new Page<OaPracticeFileEntity>(pageNo, pageSize);
        IPage<OaPracticeFileEntity> pageList = oaPracticeFileService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "团队结项附件信息-添加")
    @ApiOperation(value = "团队结项附件信息-添加", notes = "团队结项附件信息-添加")
    @PostMapping(value = "/add")
    public Result<OaPracticeFileEntity> add(@RequestBody JSONObject jsonObject) {
        Result<OaPracticeFileEntity> result = new Result<OaPracticeFileEntity>();
        List<OaPracticeFileEntity> oaPracticeFileList = null;
        if (StringKit.isNotEmpty(jsonObject.get("data"))) {
            Object data = jsonObject.get("data");
            JSONArray jsonArray = JSON.parseArray(StringKit.null2String(data));
            oaPracticeFileList = JSON.parseArray(jsonArray.toJSONString(), OaPracticeFileEntity.class);
        }
        if (StringKit.isEmpty(oaPracticeFileList) || oaPracticeFileList.size() == 0) {
            throw new ZsxcBootException("数据不能为空");
        }
        String teamId = oaPracticeFileList.stream().findFirst().orElseGet(OaPracticeFileEntity::new).getTeamId();
        if (oConvertUtils.isNotEmpty(oaPracticeFileList)) {
            for (OaPracticeFileEntity oaPracticeFileEntity : oaPracticeFileList) {
                oaPracticeFileEntity.setEndFile("1");
            }
            OaPracticeTeamEntity team = oaPracticeTeamService.getById(teamId);
            OaPracticePublicEntity oaPracticePublicEntity = oaPracticePublicService.qryByItemId(team.getPracticeItemId(), OaPracticePublicEntity.JXGS_TYPE);
            if (Objects.nonNull(oaPracticePublicEntity)) {
                throw new ZsxcBootException("社会实践已经结项公示，无法再提交结项申请");
            }

            oaPracticeFileService.remove(Wrappers.<OaPracticeFileEntity>lambdaQuery()
                    .eq(OaPracticeFileEntity::getTeamId, teamId)
                    .eq(OaPracticeFileEntity::getEndFile, "1"));
            oaPracticeFileService.saveOrUpdateBatch(oaPracticeFileList);
            team.setStatus(OaPracticeTeamEntity.JX_SQZ);
            team.setIsApply("2");
            OaPracticeTeamUserEntity oaPracticeTeamUserEntity = oaPracticeTeamUserService.selectUserByTeamIdAndStatus(team.getId(), "1").stream().findFirst().orElseGet(OaPracticeTeamUserEntity::new);
            // 启动流程实例
            ProcessInstance processInstance = oaPracticeTeamService.startPrizeProcess(team, oaPracticeTeamUserEntity.getUserCode(), PracticeTeamConstant.SOCIAL_PRACTICE_JX_KEY, "1");
            team.setJxProcessInstanceId(processInstance.getId());
            oaPracticeTeamService.updateById(team);
            result.success("添加成功！");
            return result;
        } else {
            result.success("提交材料为空！");
            return result;
        }
    }

    /**
     * 结项材料回显
     */
    @AutoLog(value = "团队结项材料回显")
    @ApiOperation(value = "团队结项材料回显", notes = "团队结项材料回显")
    @GetMapping(value = "/endFileList")
    public Result<List<OaPracticeFileEntity>> endFileList(@RequestParam(name = "teamId") String teamId) {
        Result<List<OaPracticeFileEntity>> result = new Result<List<OaPracticeFileEntity>>();
        LambdaQueryWrapper<OaPracticeFileEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaPracticeFileEntity::getTeamId, teamId);
        wrapper.eq(OaPracticeFileEntity::getEndFile, "1");
        List<OaPracticeFileEntity> list = oaPracticeFileService.list(wrapper);
        result.setResult(list);
        return result;
    }


    /**
     * 编辑
     */
    @AutoLog(value = "团队上传附件信息-编辑")
    @ApiOperation(value = "团队上传附件信息-编辑", notes = "团队上传附件信息-编辑")
    @PostMapping(value = "/edit")
    public Result<OaPracticeFileEntity> edit(@RequestBody OaPracticeFileEntity oaPracticeFile) {
        Result<OaPracticeFileEntity> result = new Result<OaPracticeFileEntity>();
        OaPracticeFileEntity oaPracticeFileEntity = oaPracticeFileService.getById(oaPracticeFile.getId());
        if (oaPracticeFileEntity == null) {
            return result.error500("未找到对应实体");
        } else {
            boolean ok = oaPracticeFileService.updateById(oaPracticeFile);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "团队上传附件信息-通过id删除")
    @ApiOperation(value = "团队上传附件信息-通过id删除", notes = "团队上传附件信息-通过id删除")
    @GetMapping(value = "/delete/{id}")
    public Result<?> delete(@PathVariable(name = "id") String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.error("请选择要删除的数据!");
        }
        OaPracticeFileEntity oaPracticeFile = oaPracticeFileService.getById(id);
        if (oConvertUtils.isNotEmpty(oaPracticeFile)) throw new ZsxcBootException("该文件不存在");
        oaPracticeFileService.removeById(oaPracticeFile.getId());
        return Result.ok("删除成功!");
    }

    /**
     * 批量删除
     */
    @AutoLog(value = "团队上传附件信息-批量删除")
    @ApiOperation(value = "团队上传附件信息-批量删除", notes = "团队上传附件信息-批量删除")
    @GetMapping(value = "/deleteBatch/{ids}")
    public Result<OaPracticeFileEntity> deleteBatch(@PathVariable(name = "ids") String ids) {
        Result<OaPracticeFileEntity> result = new Result<OaPracticeFileEntity>();
        if (ids == null || StringUtils.isEmpty(ids)) {
            result.error500("参数不识别！");
        } else {
            this.oaPracticeFileService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "团队上传附件信息-通过id查询")
    @ApiOperation(value = "团队上传附件信息-通过id查询", notes = "团队上传附件信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OaPracticeFileEntity> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<OaPracticeFileEntity> result = new Result<OaPracticeFileEntity>();
        OaPracticeFileEntity oaPracticeFile = oaPracticeFileService.getById(id);
        if (oaPracticeFile == null) {
            result.error500("未找到对应实体");
        } else {
            result.setResult(oaPracticeFile);
            result.setSuccess(true);
        }
        return result;
    }


    /**
     * 结项审核
     *
     * @return
     */
    @PostMapping("/registrationReview")
    @AutoLog(value = "社会实践-结项审核")
    @ApiOperation(value = "社会实践-结项审核", notes = "社会实践-结项审核")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> pdspReview(@RequestBody @Valid ItemAuditDTO itemAuditDTO) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String, Object> variables = new HashMap<>();
        variables.put("status", itemAuditDTO.getStatus());
        String processInstanceId = oaPracticeTeamService.auditPrizeProcess(itemAuditDTO.getTaskId(), itemAuditDTO.getAuditNote(), sysUser.getId(), variables, "1");
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
        return Result.ok("审核成功");
    }

    /**
     * 结项审核-批量/全部审核
     *
     * @return
     */
    @PostMapping("/jxBatchReview")
    @AutoLog(value = "社会实践-结项审核-批量/全部审核")
    @ApiOperation(value = "社会实践-结项审核-批量/全部审核", notes = "社会实践-结项审核-批量/全部审核")
    public Result<?> jxBatchReview(@RequestBody PracticeVerifyReqDto practiceVerifyReqDto) {

        return oaPracticeTeamService.jxBatchReview(practiceVerifyReqDto);
    }

    /**
     * 一键结项通过
     */
    @AutoLog(value = "社会实践-一键结项通过")
    @ApiOperation(value = "社会实践-一键结项通过", notes = "社会实践-一键结项通过")
    @RequestMapping(value = "/addJxRegistration")
    public Result<?> addJxRegistration(@RequestParam(value = "id") String id) {
        return oaPracticeTeamService.addJxRegistration(id);
    }

    /**
     * 一键结项取消
     */
    @AutoLog(value = "社会实践-一键结项取消")
    @ApiOperation(value = "社会实践-一键结项取消", notes = "社会实践-一键结项取消")
    @RequestMapping(value = "/cancelJxRegistration")
    public Result<?> cancelJxRegistration(@RequestParam(value = "id") String id) {
        return oaPracticeTeamService.cancelJxRegistration(id);
    }

}
