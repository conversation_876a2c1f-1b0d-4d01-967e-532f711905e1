package com.zs.create.modules.item.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.item.entity.ScEvaluationDetailEntity;

import java.util.List;

/**
 * @Description 项目评价维度分数详细Service层
 *
 * <AUTHOR> @email 
 * @date 2023-02-16 16:30:36
 * @Version: V1.0
 */
public interface ScEvaluationDetailService extends IService<ScEvaluationDetailEntity> {

    List<ScEvaluationDetailEntity> getByUserCodeAndItemId(String userCode, String itemId);
}

