package com.zs.create.modules.communication.salon.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;
import org.jeecgframework.poi.excel.annotation.Excel;

import javax.validation.constraints.NotNull;

/**
 * 话题评价
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-12-08 10:07:50
 */
@Data
@TableName("cp_proposal_evaluation")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="cp_proposal_evaluation对象", description="话题评价")
public class CpProposalEvaluationEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId
	private String id;
	/**
	 * 话题id
	 */
	@NotNull(message = "话题id不能为空")
	private String proposalId;

	/**
	 * 评价人学号
	 */
	@Excel(name = "学号", width = 15)
	private String userXuehao;
	/**
	 * 评价人姓名
	 */
	@Excel(name = "姓名", width = 15)
	private String userName;
	/**
	 * 评价人电话
	 */
	@Excel(name = "电话", width = 15)
	private String userPhone;
	/**
	 * 评价人邮箱
	 */
	@Excel(name = "邮箱", width = 15)
	private String userEmail;
	/**
	 * 评价人学院
	 */
	@Excel(name = "学院", width = 15)
	private String userCollege;

	/**
	 * 评价星级
	 */
	@Excel(name = "星级", width = 15)
	private Integer evaNum;

	/**
	 * 评价意见
	 */
	@Length(max=100, message = "评价意见长度不能超过100字符")
	@Excel(name = "评价意见", width = 20)
	private String remarks;
	/**
	 * 删除标记
	 */
	private Integer delFlag;
	/**
	 * 创建人id
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

}
