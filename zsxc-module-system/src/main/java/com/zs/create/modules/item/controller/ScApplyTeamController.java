package com.zs.create.modules.item.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.entity.ScApplyTeamEntity;
import com.zs.create.modules.item.service.ScApplyTeamService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.Arrays;
import java.util.List;
import java.util.Map;


/**
 * @Description 团队申请Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-06 15:02:35
 * @Version: V1.0
 */
@Slf4j
@Api(tags="团队申请")
@RestController
@RequestMapping("/item/scApplyTeam")
public class ScApplyTeamController {
    @Autowired
    private ScApplyTeamService scApplyTeamService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "团队申请-分页列表查询")
    @ApiOperation(value="团队申请-分页列表查询", notes="团队申请-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ScApplyTeamEntity>> queryPageList(ScApplyTeamEntity scApplyTeam,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<ScApplyTeamEntity>> result = new Result<>();
        QueryWrapper<ScApplyTeamEntity> queryWrapper = QueryGenerator.initQueryWrapper(scApplyTeam, req.getParameterMap());
        Page<ScApplyTeamEntity> page = new Page<>(pageNo, pageSize);
        IPage<ScApplyTeamEntity> pageList = scApplyTeamService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "团队申请-添加")
    @ApiOperation(value="团队申请-添加", notes="团队申请-添加")
    @PostMapping(value = "/add")
    public Result<ScApplyTeamEntity> add(@RequestBody ScApplyTeamEntity scApplyTeam) {
            Result<ScApplyTeamEntity> result = new Result<>();
            scApplyTeamService.save(scApplyTeam);
            result.success("添加成功！");
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "团队申请-编辑")
    @ApiOperation(value="团队申请-编辑", notes="团队申请-编辑")
    @PutMapping(value = "/edit")
    public Result<ScApplyTeamEntity> edit(@RequestBody ScApplyTeamEntity scApplyTeam) {
        Result<ScApplyTeamEntity> result = new Result<>();
        ScApplyTeamEntity scApplyTeamEntity = scApplyTeamService.getById(scApplyTeam.getId());
        if(scApplyTeamEntity==null) {
           return result.error500("未找到对应实体");
        }else {
            boolean ok = scApplyTeamService.updateById(scApplyTeam);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "团队申请-通过id删除")
    @ApiOperation(value="团队申请-通过id删除", notes="团队申请-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id") String id) {
            scApplyTeamService.removeById(id);
		    return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "团队申请-批量删除")
    @ApiOperation(value="团队申请-批量删除", notes="团队申请-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<ScApplyTeamEntity> deleteBatch(@RequestParam(name="ids") String ids) {
        Result<ScApplyTeamEntity> result = new Result<>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.scApplyTeamService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "团队申请-通过id查询")
    @ApiOperation(value="团队申请-通过id查询", notes="团队申请-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScApplyTeamEntity> queryById(@RequestParam(name="id") String id) {
        Result<ScApplyTeamEntity> result = new Result<>();
        ScApplyTeamEntity scApplyTeam = scApplyTeamService.getById(id);
        if(scApplyTeam==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(scApplyTeam);
            result.setSuccess(true);
        }
        return result;
    }


    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<ScApplyTeamEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                ScApplyTeamEntity scApplyTeam = JSON.parseObject(deString, ScApplyTeamEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(scApplyTeam, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<ScApplyTeamEntity> pageList = scApplyTeamService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "团队申请列表");
        mv.addObject(NormalExcelConstants.CLASS, ScApplyTeamEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("团队申请列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ScApplyTeamEntity> listScApplyTeams = ExcelImportUtil.importExcel(file.getInputStream(), ScApplyTeamEntity.class, params);
                scApplyTeamService.saveBatch(listScApplyTeams);
                return Result.ok("文件导入成功！数据行数:" + listScApplyTeams.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }

}
