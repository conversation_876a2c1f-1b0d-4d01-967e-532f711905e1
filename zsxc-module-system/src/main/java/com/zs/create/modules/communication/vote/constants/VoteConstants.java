package com.zs.create.modules.communication.vote.constants;

import com.zs.create.common.util.DateUtils;

import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * @className: VoteConstants
 * @description: 投票常量类
 * @author: hy
 * @date: 2020-11-18
 **/
public class VoteConstants {

    /**
     * 投票基本信息redis key 前缀 常量
     */
    public static final String VOTE_INFO_KEY_PREFIX = "vote:info:";

    /**
     * 投票选项id集合 前缀 常量
     */
    public static final String VOTE_OPTIDS_KEY_PREFIX = "vote:optIds:";



    /**
     * 投票选项 redis key 前缀 常量
     */
    public static final String VOTE_OPTION_KEY_PREFIX = "vote:option:";

    /**
     * 投票配置 redis key 前缀 常量
     */
    public static final String VOTE_CONFIG_KEY_PREFIX = "vote:config:";

    public static final String  VOTED_USER_OPTIDS_PREFIX = "user:option:";


    /**
     * 投票数前缀
     */
    public static final String VOTE_NUM_PREFIX = "vote:vote_num:";


   // public  static final String VOTE_CAN_PREFIX = "vote:vote_can";


    public final static String VOTE_START_MESSAGE_TOPIC = "vote:start:message:topic";

    public final static String VOTE_START_MES_ID_PREFIX = "vote:start:";




    /**
     * 获取投票cache的失效的微秒数
     * @param voteEndTime
     * @return
     */
    public static Long getVoteCacheExpire(Date voteEndTime){
        LocalDateTime currentTime = DateUtils.dateToLocalDateTime(new Date());
        LocalDateTime voteEt = DateUtils.dateToLocalDateTime(voteEndTime);
        Long timeDuration = Duration.between(currentTime , voteEt).toMillis();
        return (timeDuration > 0L ? timeDuration : 0L)  + 7 * 24 * 60 * 60 * 1000;
    }







}
