package com.zs.create.base.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

public enum ProcessEnum {
    STAGING("暂存", -2), REJECT("驳回", -1), APPLY("申请中", 0), APPROVED("审核通过", 1),
    IS("是",0),NO("否",1),TODO("待办",0),HAVEDONE("已办",1),SAMEMAJOR("本专业",0),SAMEGRADE("本年级",1),
    ALLSCHOOL("全校",2),SUCCESSPK("成功",0),EQUALPK("相等",2),FAILPK("失败",1);

    ProcessEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }
    private String name;
    @EnumValue
    private Integer code;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }
}
