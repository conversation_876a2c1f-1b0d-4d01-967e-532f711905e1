package com.zs.create.modules.oa.budget.entity.dto;

import com.zs.create.modules.oa.budget.entity.OaBudgetProjectEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * @className: AddOaBudgetDTO
 * @description:
 * @author: hy
 * @date: 2021-1-18
 **/
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="部门预算DTO", description="部门预算DTO")
public class OaBudgetDeptDTO {

    /**
     * 主键
     */
    @ApiModelProperty(value = "部门预算主键")
    private Long id;
    /**
     * 学年
     */
    @ApiModelProperty(value = "学年")
    @NotBlank(message = "学年不能为空")
    private String xn;
    /**
     * 学期
     */
    @ApiModelProperty(value = "学期")
    @NotBlank(message = "学期不能为空")
    private String xq;
    /**
     * 部门id
     */
    @ApiModelProperty(value = "部门id")
    @NotBlank(message = "部门不能为空")
    private String deptId;

    @ApiModelProperty(value = "总价")
    private BigDecimal sumFunds;

    /**
     * 预算项目明细
     */
    @ApiModelProperty(value = "预算项目明细")
    @Valid
    @NotNull(message = "预算项目不能为空")
    @Size(min = 1 , message = "预算项目不能为空")
    private List<OaBudgetProjectEntity> budgetProjectList;

}
