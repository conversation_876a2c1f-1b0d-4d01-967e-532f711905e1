<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.bigScreen.mapper.SysBigScreenBasicsMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.bigScreen.entity.SysBigScreenBasicsEntity" id="sysBigScreenBasicsMap">
        <result property="id" column="id"/>
        <result property="studentTotal" column="student_total"/>
        <result property="participateNumber" column="participate_number"/>
        <result property="hoursTotal" column="hours_total"/>
        <result property="avgHours" column="avg_hours"/>
        <result property="avgScientificqiValue" column="avg_scientificqi_value"/>
        <result property="participationRate" column="participation_rate"/>
        <result property="itemHours" column="item_hours"/>
        <result property="honorHours" column="honor_hours"/>
        <result property="jobHours" column="job_hours"/>
        <result property="itemCount" column="item_count"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>