package com.zs.create.modules.system.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.constant.CommonConstant;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.FastJsonConvert;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.modules.mq.constants.BrokerMessageLogConstants;
import com.zs.create.modules.mq.entity.BrokerMessageLogDto;
import com.zs.create.modules.mq.entity.MessageReciveUserDto;
import com.zs.create.modules.mq.entity.SysMessageDto;
import com.zs.create.modules.system.asynchandel.AnnouncementHand;
import com.zs.create.modules.system.entity.SysAnnouncement;
import com.zs.create.modules.system.entity.SysAnnouncementSend;
import com.zs.create.modules.system.mapper.SysAnnouncementMapper;
import com.zs.create.modules.system.mapper.SysAnnouncementSendMapper;
import com.zs.create.modules.system.service.ISysAnnouncementService;
import com.zs.create.modules.system.service.ISysLogService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

/**
 * @Description: 系统通告表
 * @Author: zsxc
 * @Date: 2019-01-02
 * @Version: V1.0
 */
@Service
public class SysAnnouncementServiceImpl extends ServiceImpl<SysAnnouncementMapper, SysAnnouncement> implements ISysAnnouncementService {

    @Resource
    private SysAnnouncementMapper sysAnnouncementMapper;

    @Resource
    private SysAnnouncementSendMapper sysAnnouncementSendMapper;

    @Autowired
    private ISysLogService sysLogService;

    @Autowired
    private AnnouncementHand announcementHand;

    @Autowired
    private MongoTemplate mongoTemplate;
    @Override
    public Page<SysAnnouncement> querySysCementPageByUserId(Page<SysAnnouncement> page, String userId, String msgCategory) {
        return page.setRecords(sysAnnouncementMapper.querySysCementListByUserId(page, userId, msgCategory));
    }

    @Override
    public Result<?> saveSysMessage(SysMessageDto sysMessageDto){
        Result<?> result = new Result<>();
        sysMessageDto.setCreateTime(new Date()).setSendStatus(CommonConstant.DEL_FLAG_0);
        if(CommonConstant.DEL_FLAG_1.toString().equals(sysMessageDto.getMsgCategory())){
            sysMessageDto.setStick(CommonConstant.DEL_FLAG_0);
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String selectUserIds = sysMessageDto.getSelectUserIds();
        String message = FastJsonConvert.convertObjectToJSON(sysMessageDto); //message 为发送的消息对象的json格式数据
        try {
            BrokerMessageLogDto brokerMessageLogDto = new BrokerMessageLogDto();
            String id = SnowIdUtils.uniqueLongHex();
            //保存主表信息
            brokerMessageLogDto.setId(id)
                    .setDelFlag(CommonConstant.DEL_FLAG_0)
                    .setTitle(sysMessageDto.getTitle())
                    .setMessageObject(message)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date())
                    .setShow(CommonConstant.DEL_FLAG_0)
                    .setStatus(BrokerMessageLogConstants.MESSAGE_SEND_SUCCESS)
                    .setSendUser(loginUser.getRealname())
                    .setSendUserId(loginUser.getId())
                    .setMsgType(CommonConstant.DEL_FLAG_0.toString())
                    .setType(sysMessageDto.getMsgCategory())
                    .setSendStatus(sysMessageDto.getSendStatus())
                    .setSelectUserIds(selectUserIds);
            //保存消息日志
            mongoTemplate.insert(brokerMessageLogDto);
            result.success("操作成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败!");
        }
        return result;
    }

    @Override
    public IPage<BrokerMessageLogDto> queryMyMessagePageList(String title, String type, Integer pageNo, Integer pageSize) {
        Page<BrokerMessageLogDto> page = new Page<>(pageNo, pageSize);
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<BrokerMessageLogDto> brokerMessageLogDtos = new ArrayList<>();
        String userId = sysUser.getId();
        //通知公告
        Query query = new Query();
        query.addCriteria(Criteria.where("reciveUser").is(userId));
        if(StringUtils.isNotEmpty(type)){
            query.addCriteria(Criteria.where("msgCategory").is(type));
        }
        query.with(new Sort(Sort.Direction.DESC, "createTime"));
        List<MessageReciveUserDto> messageReciveUserDtos = mongoTemplate.find(query, MessageReciveUserDto.class);
        for (MessageReciveUserDto messageReciveUserDto:messageReciveUserDtos) {
            String messageId=messageReciveUserDto.getMessageId();
            Query brokerMessageLogQuery = new Query();
            brokerMessageLogQuery.addCriteria(Criteria.where("_id").is(messageId));
            if(StringUtils.isNotEmpty(title)){
                brokerMessageLogQuery.addCriteria(Criteria.where("title").regex(".?" +title+ "."));
            }
            BrokerMessageLogDto brokerMessageLogDto =
                    mongoTemplate.findOne(brokerMessageLogQuery, BrokerMessageLogDto.class);
            if(brokerMessageLogDto!=null){
                //查询该条信息当前登录人是否已读
                String readFlag = messageReciveUserDto.getReadFlag();
                BrokerMessageLogDto brokerMessageLogDtoAdd = new BrokerMessageLogDto();
                BeanUtil.copyProperties(brokerMessageLogDto , brokerMessageLogDtoAdd,true, CopyOptions.create()
                        .setIgnoreNullValue(true).setIgnoreError(true));//非空赋值
                brokerMessageLogDtoAdd.setReadFlag(readFlag);
                brokerMessageLogDtos.add(brokerMessageLogDtoAdd);
            }
        }
        page.setRecords(brokerMessageLogDtos);
        page.setTotal(brokerMessageLogDtos.size());
        return page;
    }

    @Override
    public void deleteById(String id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        announcementHand.handelMessageSendUsers(id);
        Criteria crite = new Criteria();
        crite.and("_id").is(id);
        Query dtoquery = Query.query(crite);
        mongoTemplate.remove(dtoquery,BrokerMessageLogDto.SYS_MESSAGE_COLLECTION_NAME);
        sysLogService.addLog("删除消息,id:"+sysUser.getId(), CommonConstant.LOG_TYPE_2, 3);
    }

    @Override
    public Result<?> saveSysMessageForBackbone(SysMessageDto sysMessageDto) {
        Result<?> result = new Result<>();
        sysMessageDto.setCreateTime(new Date()).setSendStatus(CommonConstant.DEL_FLAG_0);
        if(CommonConstant.DEL_FLAG_1.toString().equals(sysMessageDto.getMsgCategory())){
            sysMessageDto.setStick(CommonConstant.DEL_FLAG_0);
        }
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String message = FastJsonConvert.convertObjectToJSON(sysMessageDto); //message 为发送的消息对象的json格式数据
        try {
            BrokerMessageLogDto brokerMessageLogDto = new BrokerMessageLogDto();
//            String id = SnowIdUtils.uniqueLongHex();
            //保存主表信息
            brokerMessageLogDto.setId(sysMessageDto.getId())
                    .setDelFlag(CommonConstant.DEL_FLAG_0)
                    .setTitle(sysMessageDto.getTitle())
                    .setMessageObject(message)
                    .setCreateTime(new Date())
                    .setUpdateTime(new Date())
                    .setShow(CommonConstant.DEL_FLAG_0)
                    .setStatus(BrokerMessageLogConstants.MESSAGE_SEND_SUCCESS)
                    .setSendUser(loginUser.getRealname())
                    .setMsgType(CommonConstant.DEL_FLAG_0.toString())
                    .setType(sysMessageDto.getMsgCategory())
                    .setSendStatus(sysMessageDto.getSendStatus());
            //保存消息日志
            mongoTemplate.insert(brokerMessageLogDto);
            result.success("操作成功！");
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            result.error500("操作失败!");
        }
        return result;
    }

}
