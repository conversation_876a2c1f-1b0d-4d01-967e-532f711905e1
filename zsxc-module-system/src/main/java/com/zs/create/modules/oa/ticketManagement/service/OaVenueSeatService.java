package com.zs.create.modules.oa.ticketManagement.service;

import com.zs.create.modules.oa.ticketManagement.entity.OaVenueSeatEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description 票券管理—座位信息Service层
 *
 * <AUTHOR> @email 
 * @date 2023-03-31 13:48:59
 * @Version: V1.0
 */
public interface OaVenueSeatService extends IService<OaVenueSeatEntity> {

    /**
     * 设置座位编号
     * @param seatInfoList
     * @return
     */
    List<List<OaVenueSeatEntity>> setSeatNumber(List<List<OaVenueSeatEntity>> seatInfoList);

    List<List<OaVenueSeatEntity>> packSeat(List<OaVenueSeatEntity> seatEntityList);

}

