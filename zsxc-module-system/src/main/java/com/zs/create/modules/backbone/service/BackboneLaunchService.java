package com.zs.create.modules.backbone.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.backbone.entity.BackboneLaunchApplyEntity;
import com.zs.create.modules.backbone.entity.BackboneLaunchEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * @Description 学生骨干Service层
 *
 * <AUTHOR>
 * @email null
 * @date 2021-05-22 10:19:25
 * @Version: V1.0
 */
public interface BackboneLaunchService extends IService<BackboneLaunchEntity> {

    /**
     * 通过主键id进行发布
     */
    Integer release(String id);

    /**
     * 修改
     * @param backboneLaunch
     * @return
     */
    boolean updateLaunch(BackboneLaunchEntity backboneLaunch);

    /**
     * 根据depid进行查询所有的apply
     */
    IPage<BackboneLaunchApplyEntity> getApplyInfoByDeptId(Page page, String depIds, String realNmae, String userName, String launchId);

    /**
     * 导出
     * @param depIds
     * @param realNmae
     * @param userName
     * @param launchId
     * @return
     */
    List<BackboneLaunchApplyEntity> getApplyInfoByDeptIdExport(String depIds, String realNmae, String userName, String launchId);

    List<BackboneLaunchEntity> getNeedBack(Integer type);

    List<String> getNeedUsersByBack(BackboneLaunchEntity launchEntity);

}

