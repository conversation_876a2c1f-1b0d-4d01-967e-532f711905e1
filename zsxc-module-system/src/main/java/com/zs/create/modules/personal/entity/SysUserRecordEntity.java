package com.zs.create.modules.personal.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户编辑记录表
 *
 * <AUTHOR> @email 
 * @date 2022-07-28 09:19:46
 */
@Data
@TableName("sys_user_record")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sys_user_record对象", description="用户编辑记录表")
public class SysUserRecordEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public static final String QUERY_TODO = "todo";
	public static final String QUERY_HISTORY = "history";

	/**
	 * id
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "id")
	    private String id;
	/**
	 * 用户id
	 */
	    @ApiModelProperty(value = "用户id")
	    private String userId;
	/**
	 * 用户名
	 */
	@ApiModelProperty(value = "用户名")
	private String username;
	/**
	 * 审核状态,0-待审核,1-通过,2-驳回,-1-撤回
	 */
	    @ApiModelProperty(value = "审核状态,0-待审核,1-通过,2-驳回,-1-撤回")
	    private Integer status;
	/**
	 * 审核人
	 */
	    @ApiModelProperty(value = "审核人")
	    private String checkUsers;
	/**
	 * 审核意见
	 */
	@ApiModelProperty(value = "审核意见")
	private String auditMind;
	/**
	 * 头像
	 */
	    @ApiModelProperty(value = "头像")
	    private String avatar;
	/**
	 * 生日
	 */
	    @ApiModelProperty(value = "生日")
		@JsonFormat(pattern = "yyyy-MM", locale = "zh", timezone = "Asia/Shanghai")
		@DateTimeFormat(pattern = "yyyy-MM",iso =DateTimeFormat.ISO.DATE )
	    private Date birthday;
	/**
	 * 邮箱
	 */
	    @ApiModelProperty(value = "邮箱")
	    private String email;
	/**
	 * 手机号
	 */
	    @ApiModelProperty(value = "手机号")
	    private String phone;
	/**
	 * 政治面貌
	 */
	    @ApiModelProperty(value = "政治面貌")
	    private String politic;
	/**
	 * 性别
	 */
	    @ApiModelProperty(value = "性别")
	    private Integer sex;
	/**
	 * 删除状态0-未删除,1-已删除
	 */
	    @ApiModelProperty(value = "删除状态0-未删除,1-已删除")
	    private Integer delFlag;
	/**
	 * 创建人
	 */
	    @ApiModelProperty(value = "创建人")
	    private String createBy;
	/**
	 * 创建时间
	 */
	    @ApiModelProperty(value = "创建时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date createTime;
	/**
	 * 更新人
	 */
	    @ApiModelProperty(value = "更新人")
	    private String updateBy;
	/**
	 * 更新人
	 */
	    @ApiModelProperty(value = "更新人")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date updateTime;

	/**
	 *查询类型 ： todo ： 代办   history ：  已办
	 */
	@ApiModelProperty(value = "查询类型 ： todo ： 代办   history ：  已办")
	@TableField(exist = false)
	private String queryType;

	/**
	 * 年级
	 */
	@ApiModelProperty(value = "年级")
	@TableField(exist = false)
	private String grade;

	/**
	 * 类型
	 */
	@ApiModelProperty(value = "类型")
	@TableField(exist = false)
	private String type;

	/**
	 * 部门名称
	 */
	@ApiModelProperty(value = "部门名称")
	@TableField(exist = false)
	private String deptName;

}
