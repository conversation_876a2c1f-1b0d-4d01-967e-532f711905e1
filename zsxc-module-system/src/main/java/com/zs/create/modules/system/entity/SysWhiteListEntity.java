package com.zs.create.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

/**
 * 白名单
 *
 * <AUTHOR> @email
 * @date 2022-05-07 09:51:46
 */
@Data
@TableName("sys_white_list")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "sys_white_list对象", description = "白名单")
public class SysWhiteListEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.ID_WORKER_STR)
	@ApiModelProperty(value = "主键")
	private String id;
	/**
	 * 学号
	 */
	@ApiModelProperty(value = "学号")
	private String userId;
	/**
	 * 创建人
	 */
	@ApiModelProperty(value = "创建人")
	private String createBy;
	/**
	 * 创建时间
	 */
	@ApiModelProperty(value = "创建时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;

	/**
	 * 姓名
	 */
	@ApiModelProperty(value = "姓名")
	@TableField(exist = false)
	private String userName;

	/**
	 * 用户类型
	 */
	@ApiModelProperty(value = "用户类型")
	@TableField(exist = false)
	private String type;

	/**
	 * 联系电话
	 */
	@ApiModelProperty(value = "联系电话")
	@TableField(exist = false)
	private String phone;

	/**
	 * 邮箱
	 */
	@ApiModelProperty(value = "邮箱")
	@TableField(exist = false)
	private String email;
}
