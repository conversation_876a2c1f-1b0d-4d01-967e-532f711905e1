<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.score.mapper.ScDayHoursMapper">


    <select id="hoursStatistics" resultType="com.zs.create.modules.score.dto.ScDayHoursDto">
        SELECT
        a.user_id,
        a.user_name,
        DATE_FORMAT(b.create_time,'%Y-%m-%d') day,
        a.module,
        sum(a.hours) hours
        FROM sc_item_hours a
        left JOIN (SELECT id,item_id,create_time from sc_item_finish GROUP BY item_id) b on a.item_id = b.item_id
        WHERE
        a.status = 10 and a.given=1 and a.del_flag=0
        and b.id is not null
        <if test="itemIds != null and itemIds.size >0">
            and a.item_id  in
            <foreach collection="itemIds" item="itemId" index="index" open="(" close=")" separator=",">
                #{itemId}
            </foreach>
        </if>
        GROUP BY a.user_id,DATE_FORMAT(b.create_time,'%Y-%m-%d'),a.module
				ORDER BY a.user_id,b.create_time
    </select>
    <select id="allUserHours" resultType="com.zs.create.modules.score.entity.ScYearScoreEntity">
        SELECT
        user_id,
        user_name,
        IFNULL(sum(sum_hours),0.00) countHours,
        IFNULL(sum(d_hours),0.00) dHours,
        IFNULL(sum(z_hours),0.00) zHours,
        IFNULL(sum(t_hours),0.00) tHours,
        IFNULL(sum(m_hours),0.00) mHours,
        IFNULL(sum(l_hours),0.00) lHours,
        IFNULL(sum(qxt),0.00) qxtHours,
        IFNULL(sum(zyh),0.00) zyhHours
        FROM `sc_day_hours`
        <where>
            <if test="st != null ">
                and  day &gt;= #{st}
            </if>
            <if test="et != null">
                and  day &lt;= #{et}
            </if>
        </where>
        GROUP BY user_id
        ORDER BY sum_hours desc
    </select>

    <select id="queryScDayHours" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        SELECT sdh.id,sdh.user_id AS userId,sdh.user_name AS userName,sdh.day,SUM(sdh.sum_hours) AS sumHours,SUM(sdh.d_hours) dHours ,SUM(sdh.z_hours) zHours,SUM(sdh.t_hours) tHours,SUM(sdh.m_hours) mHours,SUM(sdh.l_hours) lHours,sdh.create_by AS createBy,sdh.create_time AS createTime,sdh.del_flag AS delFlag,
        SUM(sdh.qxt) AS qxtHours,SUM(sdh.zyh) AS zyhHours
        FROM sc_day_hours AS sdh
        <where>
            <if test="scDayHoursEntity.userId != null and scDayHoursEntity.userId != ''">
                AND sdh.user_id = #{scDayHoursEntity.userId}
            </if>
            <if test="scDayHoursEntity.userName != null and scDayHoursEntity.userName != ''">
                AND sdh.user_name = #{scDayHoursEntity.userName}
            </if>
        </where>
        GROUP BY sdh.user_id
    </select>
    <select id="studentAnalysis" resultType="com.zs.create.modules.statistic.entity.StudentAnalysisDto">
        SELECT
        t1.id,
        t1.userId,
        t1.username,
        t1.college,
        t1.classes,
        CASE
        WHEN t2.module = 'd' THEN
        t1.dHours + sum(t2.hours)
        ELSE t1.dHours
        END as dHours,
        CASE
        WHEN t2.module = 'z' THEN
        t1.zHours + sum(t2.hours)
        ELSE t1.zHours
        END as zHours,
        CASE
        WHEN t2.module = 't' THEN
        t1.tHours + sum(t2.hours)
        ELSE t1.tHours
        END as tHours,
        CASE
        WHEN t2.module = 'm' THEN
        t1.mHours + sum(t2.hours)
        ELSE t1.mHours
        END as mHours,
        CASE
        WHEN t2.module = 'l' THEN
        t1.lHours + sum(t2.hours)
        ELSE t1.lHours
        END as lHours,
        t1.workHours,
        t1.warningNum,
        dHours+ zHours+tHours+mHours+lHours sumHours,
        ifnull(sum(t2.hours),0.00) honorHours
        FROM (
        SELECT
        a.id,
        a.userId,
        a.username,
        a.college,
        a.classes,
        IFNULL(b.d_hours,0) dHours,
        IFNULL(b.z_hours,0) zHours,
        IFNULL(b.t_hours,0) tHours,
        IFNULL(b.m_hours,0) mHours,
        IFNULL(IFNULL(b.l_hours,0.00)+IFNULL(c.hours,0.00),0) lHours,
        IFNULL(IFNULL(b.sum_hours,0.00)+IFNULL(c.hours,0.00),0) sumHours,
        IFNULL(c.hours,0) workHours,
        IFNULL(e.num,0) as warningNum
        FROM
        (SELECT t1.id,
        t1.username as userId,
        t1.realname as username,
        t1.college,
        t1.classes
        FROM sys_user t1
        left join sys_user_depart t2 on t1.id=t2.user_id
        WHERE t1.type in ('S', 'G')
        <if test="dto.userId != null and dto.userId !=''">
            and t1.username like concat('%',#{dto.userId},'%')
        </if>
        <if test="dto.username != null and dto.username!=''">
            and t1.realname like concat('%',#{dto.username},'%')
        </if>
        <if test="depIds != null and depIds.size>0">
            and  t2.dep_id in
            <foreach collection="depIds" item="depId" separator="," open="(" close=")" index="index">
                #{depId}
            </foreach>
        </if>
        ) a
        LEFT JOIN
        (
        SELECT
        user_id,
        ifnull(sum(sum_hours),0.00) sum_hours,
        ifnull(sum(d_hours),0.00) d_hours,
        ifnull(sum(z_hours),0.00) z_hours,
        ifnull(sum(t_hours),0.00) t_hours,
        ifnull(sum(m_hours),0.00) m_hours,
        ifnull(sum(l_hours),0.00) l_hours
        FROM sc_day_hours
        <where>
            <if test="dto.st != null">
                and day >= #{dto.st}
            </if>
            <if test="dto.et != null">
                and day &lt;= #{dto.et}
            </if>
        </where>
        GROUP BY user_id
        ) b on a.id = b.user_id
        LEFT JOIN
        (
        SELECT
        a.user_name userId,
        sum(b.hours) hours
        FROM backbone_launch_apply a
        left join sc_item_hours b  on a.id = b.item_id
        WHERE
        a.status = 3
        and b.module = 'l'
        and b.`status` = 10 and b.del_flag = 0
        group by a.user_name
        ) c on a.id = c.userId
        LEFT JOIN (SELECT
        user_id,
        count(1) num
        FROM sc_item_hours_warning_detail
        GROUP BY user_id ) e on a.id = e.user_id
        LEFT join sc_item_finish e on a.id = e.item_id
        )  t1
        left join (
        SELECT
        user_id,
        module,
        sum(hours) hours
        FROM sc_item_hours
        WHERE item_id = '-100'and `status`= 10 and del_flag = 0 and given =1
        GROUP BY user_id, module
        ) t2 on t1.id= t2.user_id
        GROUP BY t1.id
        <if test="dto.column != null and dto.column!=''">
            order by ${dto.column}
        </if>
        <if test="dto.order != null and dto.order!=''">
            ${dto.order}
        </if>
    </select>
    <select id="exportList" resultType="com.zs.create.modules.statistic.entity.StudentAnalysisDto">
        SELECT
        a.id,
        a.id userId,
        a.realname username,
        a.college,
        a.classes,
        ROUND(IFNULL(t1.dHours, 0.0),1) dHour,
        round(IFNULL(t1.zHours,0.0),1) zHour,
        round(IFNULL(t1.tHours,0.0),1) tHour,
        round(IFNULL(t1.mHours,0.0),1) mHour,
        round(IFNULL(t1.lHours,0.0),1) lHour,
        round(IFNULL(t1.dHours+t1.zHours+t1.tHours+t1.mHours+t1.lHours ,0.0),1) sumHour,
        round(IFNULL(t1.zyh,0.0),1) zyhHour,
        round(IFNULL(t1.qxt,0.0),1) qxtHour,
        round(IFNULL(t1.`work`,0.0),1) workHour,
        round(IFNULL(t1.honor,0.0),1) honorHour,
        round(IFNULL(t1.txHour,0.0),1) txHourSums,
        round(IFNULL(t1.socialHour,0.0),1) socialHourSums,
        IFNULL(ROUND(CASE WHen
        ((CASE when t1.dHours >100 then 100 ELSE t1.dHours end)/20 +
        (CASE when t1.zHours >100 then 100 ELSE t1.zHours end)/20 +
        (CASE when t1.tHours >100 then 100 ELSE t1.tHours end)/20 +
        (CASE when t1.mHours >100 then 100 ELSE t1.mHours end)/20 +
        (CASE when t1.lHours >100 then 100 ELSE t1.lHours end)/20 )/5 >
        LEAST ((CASE when t1.dHours >100 then 100 ELSE t1.dHours end)/20 + 2 ,
        (CASE when t1.zHours >100 then 100 ELSE t1.zHours end)/20+2,
        (CASE when t1.tHours >100 then 100 ELSE t1.tHours end)/20+2,
        (CASE when t1.mHours >100 then 100 ELSE t1.mHours end)/20+2,
        (CASE when t1.lHours >100 then 100 ELSE t1.lHours end)/20 +2) then
        LEAST ((CASE when t1.dHours >100 then 100 ELSE t1.dHours end)/20 + 2 ,
        (CASE when t1.zHours >100 then 100 ELSE t1.zHours end)/20+2,
        (CASE when t1.tHours >100 then 100 ELSE t1.tHours end)/20+2,
        (CASE when t1.mHours >100 then 100 ELSE t1.mHours end)/20+2,
        (CASE when t1.lHours >100 then 100 ELSE t1.lHours end)/20 +2)
        ELSE
        ((CASE when t1.dHours >100 then 100 ELSE t1.dHours end)/20 +
        (CASE when t1.zHours >100 then 100 ELSE t1.zHours end)/20 +
        (CASE when t1.tHours >100 then 100 ELSE t1.tHours end)/20 +
        (CASE when t1.mHours >100 then 100 ELSE t1.mHours end)/20 +
        (CASE when t1.lHours >100 then 100 ELSE t1.lHours end)/20 )/5 end,2),0.00) star
        FROM sys_user a
        LEFT JOIN (
        SELECT
        user_id,
        IFNULL(sum(CASE WHEN module= 'd' THEN hours ELSE 0.0 END),0.0) dhours,
        IFNULL(sum(CASE WHEN module= 'z' THEN hours ELSE 0.0 END),0.0) zhours,
        IFNULL(sum(CASE WHEN module= 't' THEN hours ELSE 0.0 END),0.0) thours,
        IFNULL(sum(CASE WHEN module= 'm' THEN hours ELSE 0.0 END),0.0) mhours,
        IFNULL(sum(CASE WHEN module= 'l' THEN hours ELSE 0.0 END),0.0) lhours,
        sum(CASE WHEN zyh_type = 1 THEN hours ELSE 0.0 END) zyh,
        sum(CASE WHEN qxt_type = 1 THEN hours ELSE 0.0 END) qxt,
        sum(CASE WHEN work_type = 1 THEN hours ELSE 0.0 END) work,
        sum(CASE WHEN item_id ='-100' THEN hours ELSE 0.0 END) honor,
        sum(CASE WHEN tx_type = 1 THEN hours ELSE 0.0 END) txHour,
        sum(CASE WHEN social_type = 1 THEN hours ELSE 0.0 END) socialHour
        from sc_item_hours
        WHERE `status` = 10 and del_flag = 0
        <if test="dto.st != null">
            and update_time &gt;= #{dto.st}
        </if>
        <if test="dto.et != null">
            and update_time &lt;= #{dto.et}
        </if>
        GROUP BY user_id
        ORDER BY user_id
        ) t1 on a.id = t1.user_id
        WHERE
        a.type != 'T'
        <if test="dto.username != null and dto.username!=''">
            and (a.realname like concat('%',#{dto.username},'%') or a.username like concat('%',#{dto.username},'%') )
        </if>
        <if test="users.size > 0">
            and a.id in
            <foreach collection="users" index="index" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        <if test="dto.column != null and dto.column!=''">
            order by  ${dto.column}
        </if>
        <if test="dto.order != null and dto.order!=''">
            ${dto.order}
        </if>
    </select>
    <select id="studentAnalysisTw" resultType="com.zs.create.modules.statistic.entity.StudentAnalysisDto">
        SELECT
        a.id,
        a.id userId,
        a.realname username,
        a.college,
        a.classes,
        ROUND(IFNULL(t1.dHours, 0.0),1) dHour,
        round(IFNULL(t1.zHours,0.0),1) zHour,
        round(IFNULL(t1.tHours,0.0),1) tHour,
        round(IFNULL(t1.mHours,0.0),1) mHour,
        round(IFNULL(t1.lHours,0.0),1) lHour,
        round(IFNULL(t1.dHours+t1.zHours+t1.tHours+t1.mHours+t1.lHours ,0.0),1) sumHour,
        round(IFNULL(t1.zyh,0.0),1) zyhHour,
        round(IFNULL(t1.qxt,0.0),1) qxtHour,
        round(IFNULL(t1.`work`,0.0),1) workHour,
        round(IFNULL(t1.honor,0.0),1) honorHour,
        round(IFNULL(t1.txHour,0.0),1) txHourSums,
        round(IFNULL(t1.socialHour,0.0),1) socialHourSums,
        IFNULL(ROUND(CASE
        WHen  ((CASE
        when t1.dHours >100 then 100
        ELSE t1.dHours end
        )/20 +
        (CASE
        when t1.zHours >100 then 100
        ELSE t1.zHours end
        )/20 +
        (CASE
        when t1.tHours >100 then 100
        ELSE t1.tHours end
        )/20 +
        (CASE
        when t1.mHours >100 then 100
        ELSE t1.mHours end
        )/20 +
        (CASE
        when t1.lHours >100 then 100
        ELSE t1.lHours end
        )/20 )/5 > LEAST ((CASE
        when t1.dHours >100 then 100
        ELSE t1.dHours end
        )/20 + 2 ,
        (CASE
        when t1.zHours >100 then 100
        ELSE t1.zHours end
        )/20+2,
        (CASE
        when t1.tHours >100 then 100
        ELSE t1.tHours end
        )/20+2,
        (CASE
        when t1.mHours >100 then 100
        ELSE t1.mHours end
        )/20+2,(CASE
        when t1.lHours >100 then 100
        ELSE t1.lHours end
        )/20 +2) then LEAST ((CASE
        when t1.dHours >100 then 100
        ELSE t1.dHours end
        )/20 + 2 ,
        (CASE
        when t1.zHours >100 then 100
        ELSE t1.zHours end
        )/20+2,
        (CASE
        when t1.tHours >100 then 100
        ELSE t1.tHours end
        )/20+2,
        (CASE
        when t1.mHours >100 then 100
        ELSE t1.mHours end
        )/20+2,(CASE
        when t1.lHours >100 then 100
        ELSE t1.lHours end
        )/20 +2)
        ELSE
        ((CASE
        when t1.dHours >100 then 100
        ELSE t1.dHours end
        )/20 +
        (CASE
        when t1.zHours >100 then 100
        ELSE t1.zHours end
        )/20 +
        (CASE
        when t1.tHours >100 then 100
        ELSE t1.tHours end
        )/20 +
        (CASE
        when t1.mHours >100 then 100
        ELSE t1.mHours end
        )/20 +
        (CASE
        when t1.lHours >100 then 100
        ELSE t1.lHours end
        )/20 )/5
        end,2),0.00) star
        FROM sys_user a
        LEFT JOIN (
        SELECT
        user_id,
        IFNULL(sum(
        CASE
        WHEN module= 'd' THEN
        hours
        ELSE
        0.0
        END
        ),0.0) dhours,
        IFNULL(sum(
        CASE
        WHEN module= 'z' THEN
        hours
        ELSE
        0.0
        END
        ),0.0) zhours,
        IFNULL(sum(
        CASE
        WHEN module= 't' THEN
        hours
        ELSE
        0.0
        END
        ),0.0) thours,
        IFNULL(sum(
        CASE
        WHEN module= 'm' THEN
        hours
        ELSE
        0.0
        END
        ),0.0) mhours,
        IFNULL(sum(
        CASE
        WHEN module= 'l' THEN
        hours
        ELSE
        0.0
        END
        ),0.0) lhours,

        sum(CASE
        WHEN item_id in (SELECT item_id from sc_volunteer_service GROUP BY item_id) THEN
        hours
        ELSE
        0.0
        END) zyh,
        sum(CASE
        WHEN item_id in (SELECT id from sc_youth_learning ) THEN
        hours
        ELSE
        0.0
        END) qxt,
        sum(CASE
        WHEN item_id in (SELECT id from backbone_launch_apply ) THEN
        hours
        ELSE
        0.0
        END) work,
        sum(CASE
        WHEN item_id ='-100' THEN
        hours
        ELSE
        0.0
        END) honor,

        sum(CASE
        WHEN item_id in (SELECT id from oa_league_school ) THEN
        hours
        ELSE
        0.0
        END) txHour,

        sum(CASE
        WHEN item_id in (SELECT id from oa_practice_item ) THEN
        hours
        ELSE
        0.0
        END) socialHour

        from sc_item_hours
        WHERE `status` = 10 and del_flag = 0
        <if test="dto.st != null">
            and update_time >= #{dto.st}
        </if>
        <if test="dto.et != null">
            and update_time &lt;= #{dto.et}
        </if>
        GROUP BY user_id
        ORDER BY user_id
        ) t1 on a.id = t1.user_id
        WHERE
         a.type in ('S', 'G')
        <if test="dto.userId != null and dto.userId !=''">
            and a.username like concat('%',#{dto.userId},'%')
        </if>
        <if test="dto.username != null and dto.username!=''">
            and (a.realname like concat('%',#{dto.username},'%') or a.username like concat('%',#{dto.username},'%') )
        </if>
        <if test="dto.column != null and dto.column!=''">
            order by ${dto.column}
        </if>
        <if test="dto.order != null and dto.order!=''">
            ${dto.order}
        </if>
    </select>
    <select id="studentAnalysisByUserIds"
            resultType="com.zs.create.modules.statistic.entity.StudentAnalysisDto">
        SELECT
        t1.id,
        t1.id userId,
        t1.realname username,
        t1.college,
        t1.classes,
        ROUND(IFNULL(t1.dHours, 0.0), 1) dHour,
        round(IFNULL(t1.zHours, 0.0), 1) zHour,
        round(IFNULL(t1.tHours, 0.0), 1) tHour,
        round(IFNULL(t1.mHours, 0.0), 1) mHour,
        round(IFNULL(t1.lHours, 0.0), 1) lHour,
        round(IFNULL(t1.dHours + t1.zHours + t1.tHours + t1.mHours + t1.lHours, 0.0), 1) sumHour,
        round(IFNULL(t1.zyh, 0.0), 1) zyhHour,
        round(IFNULL(t1.qxt, 0.0), 1) qxtHour,
        round(IFNULL(t1.`work`, 0.0), 1) workHour,
        round(IFNULL(t1.honor, 0.0), 1) honorHour,
        round(IFNULL(t1.txHour, 0.0), 1) txHourSums,
        round(IFNULL(t1.socialHour,0.0),1) socialHourSums,
        IFNULL(ROUND(CASE WHen
        ((CASE when t1.dHours >100 then 100 ELSE t1.dHours end)/20 +
        (CASE when t1.zHours >100 then 100 ELSE t1.zHours end)/20 +
        (CASE when t1.tHours >100 then 100 ELSE t1.tHours end)/20 +
        (CASE when t1.mHours >100 then 100 ELSE t1.mHours end)/20 +
        (CASE when t1.lHours >100 then 100 ELSE t1.lHours end)/20 )/5 >
        LEAST ((CASE when t1.dHours >100 then 100 ELSE t1.dHours end)/20 + 2 ,
        (CASE when t1.zHours >100 then 100 ELSE t1.zHours end)/20+2,
        (CASE when t1.tHours >100 then 100 ELSE t1.tHours end)/20+2,
        (CASE when t1.mHours >100 then 100 ELSE t1.mHours end)/20+2,
        (CASE when t1.lHours >100 then 100 ELSE t1.lHours end)/20 +2) then
        LEAST ((CASE when t1.dHours >100 then 100 ELSE t1.dHours end)/20 + 2 ,
        (CASE when t1.zHours >100 then 100 ELSE t1.zHours end)/20+2,
        (CASE when t1.tHours >100 then 100 ELSE t1.tHours end)/20+2,
        (CASE when t1.mHours >100 then 100 ELSE t1.mHours end)/20+2,
        (CASE when t1.lHours >100 then 100 ELSE t1.lHours end)/20 +2)
        ELSE
        ((CASE when t1.dHours >100 then 100 ELSE t1.dHours end)/20 +
        (CASE when t1.zHours >100 then 100 ELSE t1.zHours end)/20 +
        (CASE when t1.tHours >100 then 100 ELSE t1.tHours end)/20 +
        (CASE when t1.mHours >100 then 100 ELSE t1.mHours end)/20 +
        (CASE when t1.lHours >100 then 100 ELSE t1.lHours end)/20 )/5 end,2),0.00) star
        FROM
        (
        SELECT
        u.id,
        u.realname,
        u.college,
        u.classes,
        ih.user_id,
        IFNULL(sum(CASE WHEN ih.module = 'd' THEN ih.hours ELSE 0.0 END), 0.0) dhours,
        IFNULL(sum(CASE WHEN ih.module = 'z' THEN ih.hours ELSE 0.0 END), 0.0) zhours,
        IFNULL(sum(CASE WHEN ih.module = 't' THEN ih.hours ELSE 0.0 END), 0.0) thours,
        IFNULL(sum(CASE WHEN ih.module = 'm' THEN ih.hours ELSE 0.0 END), 0.0) mhours,
        IFNULL(sum(CASE WHEN ih.module = 'l' THEN ih.hours ELSE 0.0 END), 0.0) lhours,
        sum(CASE WHEN ih.zyh_type = 1 THEN ih.hours ELSE 0.0 END) zyh,
        sum(CASE WHEN ih.qxt_type = 1 THEN ih.hours ELSE 0.0 END) qxt,
        sum(CASE WHEN ih.work_type = 1 THEN ih.hours ELSE 0.0 END) WORK,
        sum(CASE WHEN ih.item_id = '-100' THEN ih.hours ELSE 0.0 END) honor,
        sum(CASE WHEN ih.tx_type = 1 THEN ih.hours ELSE 0.0 END) txHour,
        sum(CASE WHEN ih.social_type = 1 THEN ih.hours ELSE 0.0 END) socialHour
        FROM sc_item_hours ih
        RIGHT JOIN sys_user u ON u.id = ih.user_id
        WHERE
        ih.`status` = 10
        AND ih.del_flag = 0
        AND u.type != 'T'
        <if test="dto.st != null">
            and ih.update_time &gt;= #{dto.st}
        </if>
        <if test="dto.et != null">
            and ih.update_time &lt;= #{dto.et}
        </if>
        <if test="dto.username != null and dto.username!=''">
            and (u.realname like concat('%',#{dto.username},'%') or u.username like concat('%',#{dto.username},'%') )
        </if>
        <if test="users.size > 0">
            and u.id in
            <foreach collection="users" index="index" separator="," open="(" close=")" item="id">
                #{id}
            </foreach>
        </if>
        GROUP BY
        ih.user_id
        ORDER BY
        ih.user_id) t1
        <if test="dto.column != null and dto.column!=''">
         order by  ${dto.column}
        </if>
        <if test="dto.order != null and dto.order!=''">
            ${dto.order}
        </if>
    </select>
    <select id="getAllUser" resultType="java.lang.String">
        SELECT
        user_id
        FROM sc_year_score
        WHERE
        xn = #{xn}
        GROUP BY user_id
    </select>
    <select id="getDayZyh" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        SELECT
        a.day,
        b.user_id userId,
        b.user_name userName,
        b.hours as lHours,
        b.hours as sumHours,
        b.hours as zyh
        FROM
        (SELECT  item_id,DATE_FORMAT(create_time,'%Y-%m-%d') day FROM sc_volunteer_service GROUP BY item_id ) a
        LEFT JOIN
        (SELECT item_id,user_id,user_name,hours FROM sc_item_hours WHERE item_id in (SELECT DISTINCT item_id FROM sc_volunteer_service ) and del_flag = 0) b on a.item_id = b.item_id
        WHERE b.user_id is not NULL
    </select>
    <select id="getQxtHours" resultType="java.lang.Integer">
        SELECT count(1) FROM sc_day_hours WHERE type = '2'
    </select>
    <select id="getQxtHoursByUserId" resultType="java.math.BigDecimal">
        select ifnull(sum(qxt),0.00) from sc_day_hours
        <where>
            type = '2'
            <if test="userId!=null and userId!=''">
                and user_id = #{userId}
            </if>
            <if test="st != null">
                and day >= #{st}
            </if>
            <if test="et != null">
                and day &lt;= #{et}
            </if>
        </where>
    </select>
    <select id="selectTimeHours" resultType="com.zs.create.modules.score.dto.ScDayHoursDto">
        SELECT
        a.user_id,
        a.user_name,
        DATE_FORMAT(a.update_time,'%Y-%m-%d') day,
        a.module,
        sum(a.hours) hours
        FROM sc_item_hours a
        WHERE
        a.status = 10 and a.given=1 and a.del_flag=0
        GROUP BY a.user_id,DATE_FORMAT(a.update_time,'%Y-%m-%d'),a.module
		ORDER BY a.user_id,a.update_time
    </select>
    <select id="timeIsNullHours" resultType="com.zs.create.modules.score.dto.ScDayHoursDto">
        SELECT
        a.user_id,
        a.user_name,
        DATE_FORMAT(a.create_time,'%Y-%m-%d') day,
        a.module,
        sum(a.hours) hours
        FROM sc_item_hours a
        WHERE
        a.status = 10 and a.given=1 and a.del_flag=0 and a.update_time is  null
        GROUP BY a.user_id,a.module
				ORDER BY a.user_id
    </select>
    <select id="userZyhHours" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(hours),0.0) FROM sc_item_hours WHERE item_id in (SELECT DISTINCT item_id FROM sc_volunteer_service )
		and status = 10 and given=1 and del_flag=0 and user_id = #{userId}
    </select>
    <select id="userQxtHours" resultType="java.math.BigDecimal">
        SELECT IFNULL(sum(hours),0.0) FROM sc_item_hours WHERE  status = 10 and given=1 and del_flag=0 and item_id  = '-300' and user_id = #{userId}
    </select>
    <select id="getDayHours" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        select * from  sc_day_hours WHERE user_id = #{userId} and DATE_FORMAT(#{day},'%Y-%m-%d') = day
        <if test="type!=null and type!=null">
            and type =#{type}
        </if>
    </select>
    <select id="xnScors" resultType="com.zs.create.modules.score.entity.ScYearScoreEntity">
        SELECT
a.user_id,
a.user_name,
a.xn,
a.countHours,
a.dHours,
a.zHours,
a.tHours,
a.mHours,
a.lHours,
IFNULL(b.zyh,0.00) zyhHours,
IFNULL(c.qxt,0.00) qxtHours,
IFNULL(d.work,0.00) work
FROM (
SELECT
        user_id,
        user_name,
        xn,
        sum(hours) countHours,
        sum(
        CASE
            WHEN module = 'd' THEN hours
            ELSE 0.00
        END
        ) dHours,
        sum(
        CASE
            WHEN module = 'z' THEN hours
            ELSE 0.00
        END
        ) zHours,
        sum(
        CASE
            WHEN module = 't' THEN hours
            ELSE 0.00
        END
        ) tHours,
        sum(
        CASE
            WHEN module = 'm' THEN hours
            ELSE 0.00
        END
        ) mHours,
        sum(
        CASE
            WHEN module = 'l' THEN hours
            ELSE 0.00
        END
        ) lHours
        FROM sc_item_hours
        WHERE `status`= 10 and given = 1 and del_flag =0
        GROUP BY user_id,xn
        ) a
        left join (
        SELECT  user_id,xn,sum(hours) zyh
                FROM sc_item_hours
                WHERE `status`= 10 and given = 1 and del_flag =0
                and item_id in (SELECT item_id from sc_volunteer_service )
                        GROUP BY user_id,xn
        ) b on a.user_id = b.user_id and a.xn =b.xn
        left join (
        SELECT user_id,xn,sum(hours) qxt
                FROM sc_item_hours
                WHERE `status`= 10 and given = 1 and del_flag =0  and item_id in (SELECT id from sc_youth_learning WHERE data_type = 1)
                        GROUP BY user_id,xn
        ) c on a.user_id = c.user_id and a.xn =c.xn
        left join (
        	SELECT
	a.user_id,
	a.xn,
	sum(a.hours) work
	FROM sc_item_hours a
	left join backbone_launch_apply b on a.item_id = b.id
	WHERE
	b.id is not null and a.`status` = 10 and a.del_flag = 0
	GROUP BY a.user_id,a.xn
        ) d on a.user_id = d.user_id and a.xn =d.xn
        left join (
        SELECT
	user_id,
	xn,
	sum(hours) honor
	from sc_item_hours
	WHERE item_id = '-100' and `status`= 10  and del_flag =0
	GROUP BY  user_id,xn
        ) e on a.user_id = e.user_id and a.xn =e.xn
        WHERE a.user_id = #{userId} and a.xn = #{xn}
    </select>
    <select id="getUserZyh" resultType="java.math.BigDecimal">
        SELECT sum(hours)
        FROM sc_item_hours
        WHERE `status`= 10 and given = 1 and del_flag =0 and xn = #{xn} and user_id = #{userId}
        and item_id in (SELECT item_id from sc_volunteer_service )
    </select>
    <select id="getUserQxt" resultType="java.math.BigDecimal">
        SELECT sum(hours)
        FROM sc_item_hours
        WHERE `status`= 10 and given = 1 and del_flag =0 and xn = #{xn} and user_id = #{userId} and item_id = '-300'
    </select>
    <select id="getUserScoreByUserId" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        SELECT
            t1.user_id id,
            t1.user_id,
            t1.user_name,
            ROUND( IFNULL( t1.dHours, 0.0 ), 1 ) dHour,
            ROUND( IFNULL( t1.zHours, 0.0 ), 1 ) zHour,
            ROUND( IFNULL( t1.tHours, 0.0 ), 1 ) tHour,
            ROUND( IFNULL( t1.mHours, 0.0 ), 1 ) mHour,
            ROUND( IFNULL( t1.lHours, 0.0 ), 1 ) lHour,
            ROUND(
                    IFNULL( t1.dHours + t1.zHours + t1.tHours + t1.mHours + t1.lHours, 0.0 ),
                    1
                ) countHour,
            ROUND( IFNULL( t1.zyh, 0.0 ), 1 ) zyhs,
            ROUND( IFNULL( t1.qxt, 0.0 ), 1 ) qxts,
            ROUND( IFNULL( t1.`work`, 0.0 ), 1 ) backBoneHours,
            ROUND( IFNULL( t1.honor, 0.0 ), 1 ) honorHourSums,
            ROUND( IFNULL( t1.txHour, 0.0 ), 1 ) txHourSums,
            ROUND( IFNULL( t1.socialHour, 0.0 ), 1 ) socialHourSums,
            IFNULL(
                    ROUND(
                            CASE

                                WHEN (
                                                 ( CASE WHEN t1.dHours > 100 THEN 100 ELSE t1.dHours END ) / 20 +
                                                 ( CASE WHEN t1.zHours > 100 THEN 100 ELSE t1.zHours END ) / 20 +
                                                 ( CASE WHEN t1.tHours > 100 THEN 100 ELSE t1.tHours END ) / 20 +
                                                 ( CASE WHEN t1.mHours > 100 THEN 100 ELSE t1.mHours END ) / 20 +
                                                 ( CASE WHEN t1.lHours > 100 THEN 100 ELSE t1.lHours END ) / 20
                                         ) / 5 > LEAST (
                                                     ( CASE WHEN t1.dHours > 100 THEN 100 ELSE t1.dHours END ) / 20 + 2,
                                                     ( CASE WHEN t1.zHours > 100 THEN 100 ELSE t1.zHours END ) / 20+2,
                                                     ( CASE WHEN t1.tHours > 100 THEN 100 ELSE t1.tHours END ) / 20+2,
                                                     ( CASE WHEN t1.mHours > 100 THEN 100 ELSE t1.mHours END ) / 20+2,
                                                     ( CASE WHEN t1.lHours > 100 THEN 100 ELSE t1.lHours END ) / 20 + 2
                                         ) THEN
                                    LEAST (
                                                    ( CASE WHEN t1.dHours > 100 THEN 100 ELSE t1.dHours END ) / 20 + 2,
                                                    ( CASE WHEN t1.zHours > 100 THEN 100 ELSE t1.zHours END ) / 20+2,
                                                    ( CASE WHEN t1.tHours > 100 THEN 100 ELSE t1.tHours END ) / 20+2,
                                                    ( CASE WHEN t1.mHours > 100 THEN 100 ELSE t1.mHours END ) / 20+2,
                                                    ( CASE WHEN t1.lHours > 100 THEN 100 ELSE t1.lHours END ) / 20 + 2
                                        ) ELSE (
                                                           ( CASE WHEN t1.dHours > 100 THEN 100 ELSE t1.dHours END ) / 20 +
                                                           ( CASE WHEN t1.zHours > 100 THEN 100 ELSE t1.zHours END ) / 20 +
                                                           ( CASE WHEN t1.tHours > 100 THEN 100 ELSE t1.tHours END ) / 20 +
                                                           ( CASE WHEN t1.mHours > 100 THEN 100 ELSE t1.mHours END ) / 20 +
                                                           ( CASE WHEN t1.lHours > 100 THEN 100 ELSE t1.lHours END ) / 20
                                                   ) / 5
                                END,
                            2
                        ),
                    0.00
                ) star
        FROM
            (
                SELECT
                    user_id,
                    user_name,
                    IFNULL( sum( CASE WHEN module = 'd' THEN hours ELSE 0.0 END ), 0.0 ) dhours,
                    IFNULL( sum( CASE WHEN module = 'z' THEN hours ELSE 0.0 END ), 0.0 ) zhours,
                    IFNULL( sum( CASE WHEN module = 't' THEN hours ELSE 0.0 END ), 0.0 ) thours,
                    IFNULL( sum( CASE WHEN module = 'm' THEN hours ELSE 0.0 END ), 0.0 ) mhours,
                    IFNULL( sum( CASE WHEN module = 'l' THEN hours ELSE 0.0 END ), 0.0 ) lhours,
                    sum(CASE WHEN zyh_type = 1 THEN hours ELSE 0.0 END) zyh,
                    sum(CASE WHEN qxt_type = 1 THEN hours ELSE 0.0 END) qxt,
                    sum(CASE WHEN work_type = 1 THEN hours ELSE 0.0 END) work,
                    sum(CASE WHEN item_id ='-100' THEN hours ELSE 0.0 END) honor,
                    sum(CASE WHEN tx_type = 1 THEN hours ELSE 0.0 END) txHour,
                    sum(CASE WHEN social_type = 1 THEN hours ELSE 0.0 END) socialHour
                FROM
                    sc_item_hours
                WHERE
                    `status` = 10
                  AND del_flag = 0
                GROUP BY
                    user_id
                ORDER BY
                    user_id
            ) t1
        WHERE
            t1.user_id = #{userId}
    </select>
    <select id="getHonorHoursByUserId" resultType="java.math.BigDecimal">
        SELECT round(IFNULL(SUM(HOURS),0.0),1) as honorHours
        FROM sc_item_hours
        WHERE item_id = '-100' and `status`= 10 and del_flag = 0 and given =1 and user_id =#{userId}
    </select>
    <select id="getBackboneHoursByUserId" resultType="java.math.BigDecimal">
        SELECT
        round(ifnull(sum(b.hours),0.0),1) hours
        FROM backbone_launch_apply a
        left join sc_item_hours b  on a.id = b.item_id
        WHERE
        a.status = 3
        and b.module = 'l'
        and b.`status` = 10 and b.del_flag = 0 and user_id = #{userId}
        group by a.user_name
    </select>
    <select id="selectZyh" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        SELECT
	    user_id,
        user_name,
        DATE_FORMAT(update_time,'%Y-%m-%d') day,
        '1' as type,
        IFNULL( sum(hours ), 0.0 ) as zyh
	    from sc_item_hours
	    WHERE `status` = 10 and given = 1 and del_flag = 0
	    and item_id in (SELECT  item_id from sc_volunteer_service GROUP BY item_id)
	    GROUP BY user_id,DATE_FORMAT(update_time,'%Y-%m-%d')
    </select>
    <select id="selectqxt" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        SELECT
	    user_id,
        user_name,
        DATE_FORMAT(update_time,'%Y-%m-%d') day,
        '2' as type,
        IFNULL( sum(hours ), 0.0 ) as qxt
	    from sc_item_hours
	    WHERE `status` = 10 and given = 1 and del_flag = 0
	    and item_id in (SELECT  id from sc_youth_learning )
	    GROUP BY user_id,DATE_FORMAT(update_time,'%Y-%m-%d')
    </select>
    <select id="selectWork" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        SELECT
	    user_id,
        user_name,
        DATE_FORMAT(update_time,'%Y-%m-%d') day,
        '3' as type,
        IFNULL( sum(hours ), 0.0 ) as work
	    from sc_item_hours
	    WHERE `status` = 10 and given = 1 and del_flag = 0
	    and item_id in (SELECT  id from backbone_launch_apply)
	    GROUP BY user_id,DATE_FORMAT(update_time,'%Y-%m-%d')
    </select>
    <select id="selectHonor" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        SELECT
	    user_id,
        user_name,
        DATE_FORMAT(update_time,'%Y-%m-%d') day,
        '4' as type,
        IFNULL( sum(hours ), 0.0 ) as work
	    from sc_item_hours
	    WHERE `status` = 10 and given = 1 and del_flag = 0
	    and item_id = '-100'
	    GROUP BY user_id,DATE_FORMAT(update_time,'%Y-%m-%d')
    </select>
    <select id="selectPrize" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        SELECT
	    user_id,
        user_name,
        DATE_FORMAT(update_time,'%Y-%m-%d') day,
        '5' as type,
        IFNULL( sum(hours ), 0.0 ) as work
	    from sc_item_hours
	    WHERE `status` = 10 and given = 1 and del_flag = 0
	    and item_id = '-200'
	    GROUP BY user_id,DATE_FORMAT(update_time,'%Y-%m-%d')
    </select>
    <select id="getDayHoursByUserIdS" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        select * from  sc_day_hours
        <where>
            <if test = "dates !=null and dates.size()>0">
                and day in
                <foreach collection="dates" open="(" close=")" separator="," item="day">
                    DATE_FORMAT(#{day},'%Y-%m-%d')
                </foreach>
            </if>
            <if test = "userIds !=null and userIds.size()>0">
                and user_id in
                <foreach collection="userIds" open="(" close=")" separator="," item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="type!=null and type!=null">
                and type =#{type}
            </if>
        </where>
    </select>
    <select id="xnScoreData" resultType="com.zs.create.modules.score.entity.ScYearScoreEntity">
        SELECT
            a.user_id,
            a.user_name,
            a.xn,
            IFNULL(a.countHours,0.00) countHours,
            IFNULL(a.dHours,0.00) dHours,
            IFNULL(a.zHours,0.00) zHours,
            IFNULL(a.tHours,0.00) tHours,
            IFNULL( a.mHours,0.00) mHours,
            IFNULL(a.lHours,0.00) lHours,
            IFNULL(b.zyh,0.00) zyhHours,
            IFNULL(c.qxt,0.00) qxtHours,
            IFNULL(d.work,0.00) work,
             IFNULL(e.honor,0.00) honor,
            IFNULL(f.tx,0.00) txHours,
            IFNULL(g.social,0.00) socialHours
        FROM (
            SELECT
            user_id,
            user_name,
            xn,
            sum(hours) countHours,
            sum(
            CASE
            WHEN module = 'd' THEN hours
            ELSE 0.00
            END
            ) dHours,
            sum(
            CASE
            WHEN module = 'z' THEN hours
            ELSE 0.00
            END
            ) zHours,
            sum(
            CASE
            WHEN module = 't' THEN hours
            ELSE 0.00
            END
            ) tHours,
            sum(
            CASE
            WHEN module = 'm' THEN hours
            ELSE 0.00
            END
            ) mHours,
            sum(
            CASE
            WHEN module = 'l' THEN hours
            ELSE 0.00
            END
            ) lHours
            FROM sc_item_hours
            WHERE `status`= 10 and given = 1 and del_flag =0
            GROUP BY user_id,xn
            ) a
            left join (
            SELECT  user_id,xn,sum(hours) zyh
            FROM sc_item_hours
            WHERE `status`= 10 and given = 1 and del_flag =0
            and volunteer_id in (SELECT id from sc_volunteer_service) or item_id in (SELECT item_id from sc_volunteer_service)
            GROUP BY user_id,xn
            ) b on a.user_id = b.user_id and a.xn =b.xn
            left join (
            SELECT user_id,xn,sum(hours) qxt
            FROM sc_item_hours
            WHERE `status`= 10 and given = 1 and del_flag =0  and item_id in (SELECT id from sc_youth_learning WHERE data_type = 1)
            GROUP BY user_id,xn
            ) c on a.user_id = c.user_id and a.xn =c.xn
            left join (
            SELECT
            a.user_id,
            a.xn,
            sum(a.hours) work
            FROM sc_item_hours a
            left join backbone_launch_apply b on a.item_id = b.id
            WHERE
            b.id is not null and a.`status` = 10 and a.del_flag = 0
            GROUP BY a.user_id,a.xn
            ) d on a.user_id = d.user_id and a.xn =d.xn
            left join (
            SELECT
            user_id,
            xn,
            sum(hours) honor
            from sc_item_hours
            WHERE item_id = '-100' and `status`= 10  and del_flag =0
            GROUP BY  user_id,xn
            ) e on a.user_id = e.user_id and a.xn =e.xn
            left join (
            SELECT user_id,xn,sum(hours) tx
            FROM sc_item_hours
            WHERE `status`= 10 and given = 1 and del_flag =0
            and item_id in(SELECT id FROM oa_league_school)
            GROUP BY user_id,xn
            ) f on a.user_id = f.user_id and a.xn =f.xn
            left join (
            SELECT user_id,xn,sum(hours) social
            FROM sc_item_hours
            WHERE `status`= 10 and given = 1 and del_flag =0
            and item_id in(SELECT id FROM oa_practice_item)
            GROUP BY user_id,xn
            ) g on a.user_id = g.user_id and a.xn =g.xn
        GROUP BY   a.user_id ,  a.xn
    </select>
    <select id="getScDayHoursEntites" resultType="com.zs.create.modules.score.entity.ScDayHoursEntity">
        select * from  sc_day_hours
        <where>
            <if test = "date !=null and date.size()>0">
                and day in
                <foreach collection="date" open="(" close=")" separator="," item="day">
                    DATE_FORMAT(#{day},'%Y-%m-%d')
                </foreach>
            </if>
            <if test = "ids !=null and ids.size()>0">
                and user_id in
                <foreach collection="ids" open="(" close=")" separator="," item="userId">
                    #{userId}
                </foreach>
            </if>
            <if test="type!=null and type!=null">
                and type =#{type}
            </if>
        </where>
    </select>

    <select id="qryOtherHours" resultType="com.zs.create.modules.statistic.entity.StudentAnalysisDto">
        SELECT
        s.user_id,
        round( IFNULL(sum( CASE WHEN item_id IN ( SELECT item_id FROM sc_volunteer_service GROUP BY item_id ) THEN hours ELSE 0.0 END ), 0.0),1) zyhHour,
        round( IFNULL(sum( CASE WHEN item_id IN ( SELECT id FROM sc_youth_learning ) THEN hours ELSE 0.0 END ), 0.0),1) qxtHour,
        round( IFNULL(sum( CASE WHEN item_id IN ( SELECT id FROM backbone_launch_apply ) THEN hours ELSE 0.0 END ), 0.0),1) workHour,
        round( IFNULL(sum( CASE WHEN item_id = '-100' THEN hours ELSE 0.0 END ), 0.0),1) honorHour,
        round( IFNULL(sum( CASE WHEN item_id IN ( SELECT id FROM oa_league_school ) THEN hours ELSE 0.0 END ), 0.0),1) txHourSums,
        round( IFNULL(sum( CASE WHEN item_id IN ( SELECT id FROM oa_practice_item ) THEN hours ELSE 0.0 END ), 0.0),1) socialHourSums
        from sc_item_hours s
        WHERE s.`status` = 10 and s.del_flag = 0
        <if test="dto.st != null">
            and s.update_time &gt;= #{dto.st}
        </if>
        <if test="dto.et != null">
            and s.update_time &lt;= #{dto.et}
        </if>
        and s.user_id in
        <foreach collection="userIds" item="userId" open="(" close=")" separator=",">
            #{userId}
        </foreach>
        GROUP BY s.user_id
    </select>
</mapper>
