package com.zs.create.modules.statistic.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.enums.ScEnum;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.DictModel;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.statistic.entity.SuperviseMonthlyReportEntity;
import com.zs.create.modules.statistic.entity.SuperviseMonthlyReportQueryDto;
import com.zs.create.modules.statistic.mapper.SuperviseMonthlyReportMapper;
import com.zs.create.modules.statistic.service.SuperviseMonthlyReportService;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.system.service.ISysDictService;
import com.zs.create.modules.system.util.DocUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.net.URLCodec;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.ByteArrayInputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 督导月报Controller层
 * @date 2022-04-08 14:28:38
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "督导月报")
@RestController
@RequestMapping("/statistic/superviseMonthlyReport")
public class SuperviseMonthlyReportController {

    @Autowired
    private ISysDictService dictService;
    @Autowired
    private ISysDepartService departService;
    @Autowired
    private SuperviseMonthlyReportService superviseMonthlyReportService;

    @AutoLog(value = "督导月报-查询学院列表")
    @ApiOperation(value = "督导月报-查询学院列表", notes = "督导月报-查询学院列表")
    @GetMapping(value = "/collegeList")
    public Result<Collection<DictModel>> queryCollegeList() {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new ZsxcBootException("当前无登录用户");
        }

        Map<String, DictModel> depDictMap = getDepDictMap(loginUser.getId());

        Result<Collection<DictModel>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(depDictMap.values());
        return result;
    }

    /**
     * 分页列表查询
     */
    @AutoLog(value = "督导月报-分页列表查询")
    @ApiOperation(value = "督导月报-分页列表查询", notes = "督导月报-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<SuperviseMonthlyReportEntity>> queryPageList(SuperviseMonthlyReportQueryDto vo,
                                                                     @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                     @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new ZsxcBootException("当前无登录用户");
        }

        // 当前登录用户负责的部门及子部门
        Map<String, DictModel> depDictMap = getDepDictMap(loginUser.getId());
        if (vo.getCollegeId() != null && !depDictMap.containsKey(vo.getCollegeId())) {
            throw new ZsxcBootException("当前用户无权限查看该学院的月报");
        }

        Result<IPage<SuperviseMonthlyReportEntity>> result = new Result<>();
        Page<SuperviseMonthlyReportEntity> page = new Page<>(pageNo, pageSize);

        if (vo.getCollegeId() == null && depDictMap.isEmpty()) {
            result.setSuccess(true);
            result.setResult(page);
            return result;
        }

        IPage<SuperviseMonthlyReportEntity> pageList = ((SuperviseMonthlyReportMapper) superviseMonthlyReportService.getBaseMapper()).page(page, vo, depDictMap.keySet());
        for (SuperviseMonthlyReportEntity entity : pageList.getRecords()) {
            DictModel dictModel = depDictMap.get(entity.getCollegeId());
            if (dictModel != null) {
                entity.setCollegeName(dictModel.getText());
            }
        }

        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 通过id查询
     */
    @AutoLog(value = "督导月报-通过id下载")
    @ApiOperation(value = "督导月报-通过id下载", notes = "督导月报-通过id下载")
    @GetMapping(value = "/download/{id}")
    public ResponseEntity<?> downloadWord(@PathVariable String id) throws Exception {
        SuperviseMonthlyReportEntity entity = download(id);

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月");
        String filename = entity.getCollegeName() + sdf.format(entity.getTime()) + "督导月报.xls";
        filename = new URLCodec().encode(filename, "UTF-8");

        byte[] bytes = superviseMonthlyReportService.download(entity);
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .body(bytes);
    }

    @AutoLog(value = "督导月报-通过id下载")
    @ApiOperation(value = "督导月报-通过id下载", notes = "督导月报-通过id下载")
    @GetMapping(value = "/downloadPdf/{id}")
    public ResponseEntity<?> downloadPdf(@PathVariable String id) throws Exception {
        SuperviseMonthlyReportEntity entity = download(id);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy年MM月");
        String filename = entity.getCollegeName() + sdf.format(entity.getTime()) + "月报.pdf";
        filename = new URLCodec().encode(filename, "UTF-8");
        byte[] docxBytes = superviseMonthlyReportService.download(entity);
        byte[] pdfBytes = DocUtils.convertWordToPdf(new ByteArrayInputStream(docxBytes));
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .header(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + filename)
                .body(pdfBytes);
    }

    private SuperviseMonthlyReportEntity download(String id) {
        SuperviseMonthlyReportEntity entity = superviseMonthlyReportService.getById(id);
        if (entity == null) {
            throw new RuntimeException("月报不存在");
        }

        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (loginUser == null) {
            throw new ZsxcBootException("当前无登录用户");
        }

        // 当前登录用户负责的部门及子部门
        List<String> depIds = departService.getChargeDepAndChildrenByUserId(loginUser.getId());
        if (!depIds.contains("409")){
            if (!depIds.contains(entity.getCollegeId())) {
                throw new ZsxcBootException("当前用户无权限下载该学院的月报");
            }
        }

        return entity;
    }

    private Map<String, DictModel> getDepDictMap(String userId) {
        // 当前登录用户负责的部门及子部门
        List<String> depIds = departService.getChargeDepAndChildrenByUserId(userId);
        List<DictModel> dictModelList = dictService.queryDictItemsByCode(ScEnum.STATISTIC_COLLEGE.getCode());
        List<String> statisticDepIds = dictModelList.stream()
                .map(DictModel::getValue)
                .collect(Collectors.toList());

        //23.03.22 校团委能看到所有
        if (depIds.contains("409")){
            depIds = statisticDepIds;
        }

        // 交集
        depIds.retainAll(statisticDepIds);

        if (depIds.isEmpty()) {
            return Collections.emptyMap();
        }

//        Map<String, String> depMap = departService.listByIds(depIds)
//                .stream()
//                .collect(Collectors.toMap(SysDepart::getId, SysDepart::getDepartName));

        Map<String, DictModel> result = new LinkedHashMap<>();
        for (DictModel dictModel : dictModelList) {
            String depId = dictModel.getValue();
            if (depIds.contains(depId)) {
//                dictModel.setText(depMap.get(depId));
                result.put(depId, dictModel);
            }
        }
        return result;
    }
}
