package com.zs.create.modules.communication.vote.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 投票
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-19 14:36:28
 */
@Data
@TableName("cp_vote_user")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="cp_vote_user对象", description="投票")
public class CpVoteUserEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId(type = IdType.AUTO)
	private Long id;
	/**
	 * 投票id
	 */
	private Long voteId;
	/**
	 * 选项内容
	 */
	private Long optId;
	/**
	 * 投票人
	 */
	private String createBy;
	/**
	 * ip
	 */
	private String ip;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

}
