package com.zs.create.modules.oa.aoumaAndBones.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.enums.QdStatusEnum;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.FastJsonConvert;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.delayqueue.constants.ApplyStatusQueueConstants;
import com.zs.create.modules.delayqueue.core.DelayQueue;
import com.zs.create.modules.delayqueue.core.DelayQueueJob;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.enums.ItemAuditStatusEnum;
import com.zs.create.modules.item.service.ScItemService;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.oa.aoumaAndBones.constants.DelayPublicConstants;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdClassEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdParticipateEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdPublicEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdScoreEntity;
import com.zs.create.modules.oa.aoumaAndBones.mapper.QdPublicMapper;
import com.zs.create.modules.oa.aoumaAndBones.redis.PublicityProducer;
import com.zs.create.modules.oa.aoumaAndBones.service.QdClassService;
import com.zs.create.modules.oa.aoumaAndBones.service.QdParticipateService;
import com.zs.create.modules.oa.aoumaAndBones.service.QdPublicService;
import com.zs.create.modules.oa.aoumaAndBones.service.QdScoreService;
import com.zs.create.modules.system.util.DictUtils;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 青马大骨班Service实现层
 *
 * <AUTHOR> @email 
 * @date 2022-11-03 10:39:18
 * @Version: V1.0
 */
@Service
@Slf4j
public class QdPublicServiceImpl extends ServiceImpl<QdPublicMapper, QdPublicEntity> implements QdPublicService {
    @Resource
    private QdParticipateService qdParticipateService;
    @Autowired
    private QdScoreService qdScoreService;
    @Autowired
    private QdClassService qdClassService;
    @Resource
    private QdPublicMapper qdPublicMapper;
    @Autowired
    private SysWeixinUserService sysWeixinUserService;
    @Resource
    private PublicityProducer publicityProducer;
    @Autowired
    @Lazy
    private WxMessageSender wxMessageSender;
    @Autowired
    private ScItemService scItemService;
    @Value("${gzhAppid}")
    private String appid;

    //成绩未公示状态
    private static final Integer CJWGS_STATUS =0;

    //成绩公示中状态
    private static final Integer CJGSZ_STATUS =1;


    @Override
    @Transactional
    public Result addPublic(String classId, Integer type) {
        Result result = new Result();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        if (oConvertUtils.isEmpty(sysUser)) throw new ZsxcBootException("登录信息为空");
        if (oConvertUtils.isEmpty(classId)) throw new ZsxcBootException("未获取到班级信息！");
        if (oConvertUtils.isEmpty(type)) throw new ZsxcBootException("公示类型为空，请联系管理员");
        String className = qdClassService.getById(classId).getClassName();
        if (type.equals(QdPublicEntity.BMGS_TYPE)) { //报名公示
            //从报名表中取对应班级通过报名审核人员
            LambdaQueryWrapper<QdParticipateEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(QdParticipateEntity::getClassId, classId)
                    .eq(QdParticipateEntity::getApplyStatus, QdStatusEnum.ysh_status.getCode());
            List<QdParticipateEntity> list = qdParticipateService.list(queryWrapper);
            if (list.size() == 0) throw new ZsxcBootException("该班级未找到已通过报名审核人员");

            //11.25班级中有未审核完成学生不允许公示
            List<Integer> noSh = new ArrayList<>();
            noSh.add(0);
            noSh.add(1);
            LambdaQueryWrapper<QdParticipateEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QdParticipateEntity::getClassId, classId)
                    .in(QdParticipateEntity::getApplyStatus,noSh);
            List<QdParticipateEntity> noShList = qdParticipateService.list(wrapper);
            if (noShList.size() > 0){
                throw new ZsxcBootException("还有学生报名暂未审核");
            }

            //向公示表插入数据
            QdPublicEntity qdPublicEntity = new QdPublicEntity();
            //字典配置公示3天
            Integer publicDuration = Integer.valueOf(DictUtils.queryDictTextByKey("item_public_perioid",
                    "hours", "1"));
            LocalDateTime publicSt = LocalDateTime.now();//公示开始时间取now
            ZoneId defaultZoneId = ZoneId.systemDefault();
            LocalDateTime publicEt = publicSt.plusHours(publicDuration);//结束时间为 开始 +3天
            qdPublicEntity.setClassId(classId).setClassName(className).setIsPublic(1).setType(0).setPublicStatus(1)
                    .setPublicStartTime(Date.from(publicSt.atZone(defaultZoneId).toInstant()))
                    .setPublicEndTime(Date.from(publicEt.atZone(defaultZoneId).toInstant()))
                    .setCreateTime(new Date()).setCreateBy(sysUser.getId())
                    .setUpdateTime(new Date()).setUpdateBy(sysUser.getId());
            this.save(qdPublicEntity);

            //更新报名表报名状态
            for (QdParticipateEntity param : list) {
                param.setApplyStatus(QdStatusEnum.gsz_status.getCode());
                param.setUpdateBy(sysUser.getId());
                param.setUpdateTime(new Date());
            }
            qdParticipateService.updateBatchById(list);

            String jsonStr = FastJsonConvert.convertObjectToJSON(qdPublicEntity);
            long ttrTime = 10L;
            //发送延时消息，三天后公示结束
            DelayQueueJob delayQueueEndJob2 = new DelayQueueJob();
            delayQueueEndJob2.setTopic(DelayPublicConstants.DELAY_PUBLIC_MESSAGE_TOPIC);
            delayQueueEndJob2.setDelayTime(Date.from(publicEt.atZone(defaultZoneId).toInstant()).getTime());
            delayQueueEndJob2.setMessage(jsonStr);
            delayQueueEndJob2.setTtrTime(ttrTime);
            delayQueueEndJob2.setId(SnowIdUtils.uniqueLong());
            DelayQueue.push(delayQueueEndJob2);
            //发送消息提醒
            for (QdParticipateEntity qdParticipateEntity : list) {
                sendMassage2ClassParticipate(classId,qdParticipateEntity.getUserId());
            }

            result.success("报名公示成功");
        }

        if (type.equals(QdPublicEntity.PYJGGS_TYPE)){ //评优结果公示
            //获取该班级内所有未公示学生成绩
            LambdaQueryWrapper<QdScoreEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(QdScoreEntity::getClassId,classId)
                    .eq(QdScoreEntity::getPublicStatus,CJWGS_STATUS);
            List<QdScoreEntity> list = qdScoreService.list(queryWrapper);
            if (list.size() == 0) throw new ZsxcBootException("该班级没有成绩未公示学生数据");

            //22.12.06增加校验，必须先筛选才能公示
            for (QdScoreEntity qdScoreEntity : list) {
                if (oConvertUtils.isEmpty(qdScoreEntity.getSuggest())){
                    throw new ZsxcBootException("请先进行结业与评优结果筛选");
                }
            }
            //22.12.06增加校验，班级内必须所有课程学时审核通过才可公示，包括暂存
            LambdaQueryWrapper<ScItemEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ScItemEntity::getQdClassId,classId);
            List<ScItemEntity> courseList = scItemService.list(wrapper);
            if (courseList.size() == 0){
                throw new ZsxcBootException("未查询到该班级课程");
            }
            for (ScItemEntity scItemEntity : courseList) {
                if (!scItemEntity.getExamineStatus().equals(ItemAuditStatusEnum.SCORE_IMPORT_ADOPT.getCode())){
                    throw new ZsxcBootException("班级内有未通过学时审核课程，请处理后再进行公示");
                }
            }

            List<BigDecimal> scoreList = list.stream().map(QdScoreEntity::getScore).collect(Collectors.toList());
            List<Integer> experienceList = list.stream().map(QdScoreEntity::getExperience).collect(Collectors.toList());

            if (scoreList.contains(null) || experienceList.contains(null)){
                throw new ZsxcBootException("还有学生暂未导入成绩!");
            }

            if (list.size()>0){
                //更新成绩表中公示状态字段
                for (QdScoreEntity param : list) {
                    param.setPublicStatus(CJGSZ_STATUS);
                }
                qdScoreService.updateBatchById(list);
            }
            //向公示表插入数据
            QdPublicEntity qdPublicEntity = new QdPublicEntity();
            //字典配置公示3天
            int publicDuration = Integer.parseInt(DictUtils.queryDictTextByKey("item_public_perioid",
                    "hours", "1"));
            LocalDateTime publicSt = LocalDateTime.now();//公示开始时间取now
            ZoneId defaultZoneId = ZoneId.systemDefault();
            LocalDateTime publicEt = publicSt.plusHours(publicDuration);//结束时间为 开始 +3天

            qdPublicEntity.setClassId(classId).setClassName(className).setIsPublic(1).setType(2).setPublicStatus(1)
                    .setPublicStartTime(Date.from(publicSt.atZone(defaultZoneId).toInstant()))
                    .setPublicEndTime(Date.from(publicEt.atZone(defaultZoneId).toInstant()))
                    .setCreateTime(new Date()).setCreateBy(sysUser.getId())
                    .setUpdateTime(new Date()).setUpdateBy(sysUser.getId());
            this.save(qdPublicEntity);

            String jsonStr = FastJsonConvert.convertObjectToJSON(qdPublicEntity);
            long ttrTime = 10L;
            //发送延时消息，三天后公示结束
            DelayQueueJob delayQueueEndJob2 = new DelayQueueJob();
            delayQueueEndJob2.setTopic(DelayPublicConstants.DELAY_PUBLIC_MESSAGE_TOPIC);
            delayQueueEndJob2.setDelayTime(Date.from(publicEt.atZone(defaultZoneId).toInstant()).getTime());
            delayQueueEndJob2.setMessage(jsonStr);
            delayQueueEndJob2.setTtrTime(ttrTime);
            delayQueueEndJob2.setId(SnowIdUtils.uniqueLong());
            DelayQueue.push(delayQueueEndJob2);

            //发送消息提醒
            for (QdScoreEntity scoreEntity : list) {
                sendMassage2EvaluationResult(classId,scoreEntity.getUserId());
            }
            result.success("评优结果公示成功");
        }

        return result;
    }
    Integer tab = 3;

    //结业与评优结果公示提醒
    public void sendMassage2EvaluationResult(String classId,String userId) {
        QdClassEntity classEntity = qdClassService.getById(classId);
        if (oConvertUtils.isEmpty(classEntity)) throw new ZsxcBootException("班级信息不存在");

        String title = "结业与评优公示提醒";
        StringBuilder contentBuilder = new StringBuilder();
        WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(userId, appid);
        if (null == wxMesUserInfo) {
            log.error("给课程导入人员发送消息，未找到接收人：{} 的信息", userId);
            return;
        }
        String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有新的消息提醒";
        String openId = wxMesUserInfo.getOpenId();
        contentBuilder.append("“"+classEntity.getClassName() + "”结业与评优结果已公示");
        String remark = "点击可查看详情";
        WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                .setUseCommonTemplate(Boolean.TRUE)
                .setTheme(theme)
                .setTitle(title)
                .setUserId(userId)
                .setCreateDate(new Date())
                .setMiniAppUrl("pagesB/projectpublic/projectpublic?curTopTab="+tab)
                .setContent(contentBuilder.toString())
                .setOpenId(openId)
                .setRemark(remark);
        wxMessageSender.wxMessageSend(wxCommonMsgInfo);
    }


    //报名公示提醒
    public void sendMassage2ClassParticipate(String classId,String userId) {
        QdClassEntity classEntity = qdClassService.getById(classId);
        if (oConvertUtils.isEmpty(classEntity)) throw new ZsxcBootException("班级信息不存在");

        String title = "报名公示提醒";
        StringBuilder contentBuilder = new StringBuilder();
        WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(userId, appid);
        if (null == wxMesUserInfo) {
            log.error("给课程导入人员发送消息，未找到接收人：{} 的信息", userId);
            return;
        }
        String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有新的消息提醒";
        String openId = wxMesUserInfo.getOpenId();
        contentBuilder.append("\""+classEntity.getClassName() + "\"报名已公示");
        String remark = "点击可查看详情";

        WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                .setUseCommonTemplate(Boolean.TRUE)
                .setTheme(theme)
                .setTitle(title)
                .setUserId(userId)
                .setCreateDate(new Date())
                .setMiniAppUrl("pagesB/projectpublic/projectpublic?curTopTab="+tab)
                .setContent(contentBuilder.toString())
                .setOpenId(openId)
                .setRemark(remark);
        wxMessageSender.wxMessageSend(wxCommonMsgInfo);
    }



    @Override
    public IPage<QdPublicEntity> pagePublic(Integer queryFlag,String userId, Integer pageNo, Integer pageSize) {

        Page<QdPublicEntity> page = new Page<QdPublicEntity>(pageNo, pageSize);
        if (queryFlag.equals(0)){//0 我参与的项目  1 我未参与项目
            page.setRecords(qdPublicMapper.myInPublic(page, userId));
        }else {
            page.setRecords(qdPublicMapper.myNotInPublic(page,userId));
        }
            return page;

    }

    /**
     * 根据班级id查询数据
     * @param classId
     * @return
     */
    @Override
    public QdPublicEntity qryByClassId(String classId) {
        return qdPublicMapper.qryByClassId(classId);
    }

    /**
     * 查询结业与评优公示数据
     * @param classId
     * @return
     */
    @Override
    public QdPublicEntity qryJYByClassId(String classId) {
        return qdPublicMapper.qryJYByClassId(classId);
    }
}
