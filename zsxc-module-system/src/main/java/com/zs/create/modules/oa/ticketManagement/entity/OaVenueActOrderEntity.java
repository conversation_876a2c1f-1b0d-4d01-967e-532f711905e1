package com.zs.create.modules.oa.ticketManagement.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.List;

/**
 * 票券管理—订单记录
 *
 * <AUTHOR> @email 
 * @date 2023-04-10 13:45:16
 */
@Data
@TableName("oa_venue_act_order")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oa_venue_act_order对象", description="票券管理—订单记录")
public class OaVenueActOrderEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * id
	 */
	    @TableId(type = IdType.ID_WORKER_STR)
	    @ApiModelProperty(value = "id")
	    private String id;
	/**
	 * 人员学工号
	 */
	    @ApiModelProperty(value = "人员学工号")
	    private String userId;
	/**
	 * 姓名
	 */
	    @ApiModelProperty(value = "姓名")
	    private String userName;
	/**
	 * 场馆id
	 */
	    @ApiModelProperty(value = "场馆id")
	    private String venueId;
	/**
	 * 活动id
	 */
	    @ApiModelProperty(value = "活动id")
	    private String activityId;
	/**
	 * 活动—座位id
	 */
	    @ApiModelProperty(value = "活动—座位id")
	    private String actSeatId;
	/**
	 * 座位编号
	 */
	    @ApiModelProperty(value = "座位编号")
	    private String seatNumber;
	/**
	 * 订单状态：0:锁定未确定；1:已确认
	 */
	    @ApiModelProperty(value = "订单状态：0:锁定未确定；1:已确认")
	    private Integer status;

	/**
	 * 是否参加(核验)  0:未参加  1:已参加 默认0
	 */
		@ApiModelProperty(value = "是否参加(核验)  0:未参加  1:已参加 默认0")
		private Integer isJoin;

	/**
	 * 条形码Base64编码
	 */
		@ApiModelProperty(value = "条形码Base64编码")
		private String barCode;
	/**
	 * 创建人
	 */
	    @ApiModelProperty(value = "创建人")
	    private String createBy;
	/**
	 * 创建时间
	 */
	    @ApiModelProperty(value = "创建时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date createTime;
	/**
	 * 更新人
	 */
	    @ApiModelProperty(value = "更新人")
	    private String updateBy;
	/**
	 * 更新时间
	 */
	    @ApiModelProperty(value = "更新时间")
	    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	    private Date updateTime;

	/**
	 * 序号
	 */
		@ApiModelProperty(value = "序号")
		@TableField(exist = false)
		private Integer sort;

	/**
	 * 座位id集合
	 */
		@ApiModelProperty(value = "座位id集合")
		@TableField(exist = false)
		private List<String> seatIdList;
	/**
	 * 座位编号集合
	 */
		@ApiModelProperty(value = "座位编号集合")
		@TableField(exist = false)
		private List<String> seatNumberList;

}
