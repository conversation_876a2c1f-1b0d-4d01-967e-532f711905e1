package com.zs.create.modules.oa.practice.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @ClassName suzheng
 * @Description TODO
 * @date 2023-01-30  17:38
 * @Version 1.0
 */
@Data
public class PracticeReviewReqDto implements Serializable {
    private static final long serialVersionUID = -6296498790192173772L;

    public static final String QUERY_TODO = "todo";
    public static final String QUERY_HISTORY = "history";

    /**
     * 团队名称
     */
    @ApiModelProperty(value = "团队名称")
    private String teamName;

    /**
     * 实践项目id
     */
    @ApiModelProperty(value = "实践项目id")
    private String practiceItemId;

    /**
     * 任务处理人
     */
    @ApiModelProperty(value = "任务处理人")
    private String assignee;

    /**
     * 查询类型
     */
    @ApiModelProperty(value = "查询类型 ： todo ： 代办   history ：  已办")
    private String queryType;
}
