<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.system.mapper.SysMoveMenuMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.system.entity.SysMoveMenuEntity" id="sysMoveMenuMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="path" column="path"/>
        <result property="icon" column="icon"/>
        <result property="sort" column="sort"/>
        <result property="menuType" column="menu_type"/>
        <result property="tag" column="tag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <collection column="id" property="children" javaType="java.util.ArrayList"
                    ofType="com.zs.create.modules.system.entity.SysMoveMenuEntity" select="getNextNodeTree"/>
    </resultMap>

    <resultMap type="com.zs.create.modules.system.entity.SysMoveMenuEntity" id="NextTreeResultMap">
        <result property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="path" column="path"/>
        <result property="icon" column="icon"/>
        <result property="sort" column="sort"/>
        <result property="menuType" column="menu_type"/>
        <result property="tag" column="tag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="delFlag" column="del_flag"/>
        <collection column="id" property="children" javaType="java.util.ArrayList"
                    ofType="com.zs.create.modules.system.entity.SysMoveMenuEntity" select="getNextNodeTree"/>
    </resultMap>

    <select id="getNextNodeTree" resultMap="NextTreeResultMap">
        SELECT * FROM sys_move_menu
        WHERE parent_id = #{id}
        and del_flag = 0
        ORDER BY sort ASC
    </select>

    <select id="getNodeTree" resultMap="sysMoveMenuMap">
        SELECT * FROM sys_move_menu where menu_type=0 and del_flag = 0 ORDER BY sort ASC
    </select>



    <select id="getUserRoleTree" resultType="com.zs.create.modules.system.entity.SysMoveMenuEntity">
          SELECT p.*
		   FROM  sys_move_menu p
		   WHERE  p.id in  (
		   	SELECT c.id FROM sys_user_role a
            INNER JOIN sys_move_role b on a.role_id=b.role_id
            INNER JOIN sys_move_menu c ON b.move_id=c.id and c.del_flag = 0
            WHERE a.user_id=#{userId}
           )
		   order by p.sort ASC
    </select>


    <select id="getUserRoleIds" resultType="java.lang.String">
         SELECT c.id FROM sys_move_role b
         INNER JOIN sys_move_menu c ON b.move_id=c.id and c.del_flag = 0
         where b.role_id=#{roleId}
    </select>

</mapper>