package com.zs.create.modules.system.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.zs.create.common.util.DateUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.util.Date;

/**
 * @createUser hy
 * @createTime 2020-5-23
 * @description
 */
@Data
@Accessors(chain = true)
@Document(collection = "sys_file_catalog")
@ApiModel(value="系统文件分类", description="系统文件分类")
public class SysFileCatalog {
    @Id
    @ApiModelProperty(value = "主键")
    private String id ;
    @ApiModelProperty(value = "目录名称")
    @NotBlank(message = "目录名称不为空")
    private String name ;
    @ApiModelProperty(value = "删除标记 0 未删除 1已经删除")
    private Integer delFlag ;
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date ct;
    @ApiModelProperty(value = "修改时间")
    private Date ut;
    @ApiModelProperty(value = "创建人")
    private String createBy;


    public String getCtFormat() {
            return DateUtils.formatDate(this.ct);
    }

}
