package com.zs.create.modules.third.scYouthLearning.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.MogoResult;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.DateUtils;
import com.zs.create.common.util.DesensitizedUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysUserDepart;
import com.zs.create.modules.system.entity.SysUserRole;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.system.service.ISysRoleService;
import com.zs.create.modules.system.service.ISysUserDepartService;
import com.zs.create.modules.third.pushrecord.entity.ScPushRecordEntity;
import com.zs.create.modules.third.pushrecord.service.ScPushRecordService;
import com.zs.create.modules.third.scYouthLearning.entity.ScYouthLearningEntity;
import com.zs.create.modules.third.scYouthLearning.entity.ScYouthLearningVo;
import com.zs.create.modules.third.scYouthLearning.service.ScYouthLearningService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.MapExcelConstants;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;
import org.jeecgframework.poi.excel.view.JeecgMapExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @Description 青年大学习Controller层
 * @email
 * @date 2022-05-09 16:19:37
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "青年大学习")
@RestController
@RequestMapping("/leran/scYouthLearning")
public class ScYouthLearningController {
    @Autowired
    private ScYouthLearningService scYouthLearningService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISysRoleService sysRoleService;

    @Autowired
    private ISysUserDepartService sysUserDepartService;
    @Autowired
    private ScPushRecordService scPushRecordService;


    /**
     * 添加
     */
    @AutoLog(value = "青年大学习-添加")
    @ApiOperation(value = "青年大学习-添加", notes = "青年大学习-添加")
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody List<ScYouthLearningEntity> list) {
        Result<?> result = new Result<>();
        ScPushRecordEntity scPushRecordEntity = new ScPushRecordEntity();
        try {
            log.warn("青年大学习推送数据...{}", JSON.toJSONString(list));
            String messge = scYouthLearningService.add(list);
            result.success(messge);
        } catch (Exception e) {
            scPushRecordEntity.setStatus(0);
            scPushRecordService.SendYouthLearning(scPushRecordEntity, "推送过程异常");
            log.error("青年大学习推送出错,{}", e.getMessage());
            result.error500("推送失败,请检查数据格式是否正确,是否有重复数据!");
        }
        return result;
    }


    /**
     * 分页列表查询
     */
    @AutoLog(value = "青年大学习-分页列表查询")
    @ApiOperation(value = "青年大学习-分页列表查询", notes = "青年大学习-分页列表查询")
    @GetMapping(value = "/list")
    public MogoResult<IPage<Map<String, Object>>> queryPageList(@RequestParam(name = "departId", required = false) String departId,
                                                                @RequestParam(name = "studentCode", required = false) String studentCode,
                                                                @RequestParam(name = "realname", required = false) String realname,
                                                                @RequestParam(name = "times", required = false) String times,
                                                                @RequestParam(name = "dataType", required = false) String dataType,
                                                                @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize) {
        MogoResult<IPage<Map<String, Object>>> result = new MogoResult<IPage<Map<String, Object>>>();
        List<String> currentDateList = new ArrayList<>();
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

        // 23.05.18改为团委和二领办看所有，下面所有权限判断除二领办全按部门表负责人字段判断
        SysUserRole youthLeagueCommittee = sysRoleService.currentUserRile("role_xtwsj", loginUser.getId());// 二领办
        Boolean isTw = sysDepartService.isTwPersonInCharge(loginUser.getUsername());// 团委

        // 23.05.18 改为学院负责人和院团委负责人看本院
//        level = 2 代表包括学院的二级机构， orgType = 3 代表院团委
        List<SysDepart> chargeInXY = sysDepartService.XyChargeByUserId(loginUser.getUsername());// 负责的学院
        // 院团委则默认取所在学院
        List<SysDepart> chargeInYTW = sysDepartService.ChargeByUserId(loginUser.getUsername(), "3");// 负责的院团委
        List<String> xyByYtw = chargeInYTW.stream().map(SysDepart::getParentId).collect(Collectors.toList());
        // 班级负责人
        LambdaQueryWrapper<SysDepart> sysDepartLambdaQueryWrapper = Wrappers.<SysDepart>lambdaQuery();
        sysDepartLambdaQueryWrapper.eq(SysDepart::getOrgType, 4);
        String sql = "FIND_IN_SET('" + loginUser.getUsername() + "',person_in_charge)";
        sysDepartLambdaQueryWrapper.apply(sql);
        sysDepartLambdaQueryWrapper.eq(SysDepart::getDelFlag, "0");
        List<SysDepart> sysDeparts = sysDepartService.list(sysDepartLambdaQueryWrapper);
        IPage<Map<String, Object>> pageList = new Page<>();
        String collegeId = "";
        String classesId = "";
        String phone = "";
        if (youthLeagueCommittee != null || isTw) {
            collegeId = "";
            classesId = "";
        } else if (chargeInXY.size() > 0 || chargeInYTW.size() > 0) {
            List<String> xyIdList = chargeInXY.stream().map(SysDepart::getId).collect(Collectors.toList());
            xyIdList.addAll(xyByYtw);
            Set<String> set = new HashSet<>(xyIdList);
            collegeId = String.join(",", set);
            classesId = sysDeparts.stream().map(SysDepart::getId).collect(Collectors.joining(","));
        } else if (CollectionUtils.isNotEmpty(sysDeparts)) {
            classesId = sysDeparts.stream().map(SysDepart::getId).collect(Collectors.joining(","));
        } else {// 学生看自己
            // 查询自己所有数据
            studentCode = loginUser.getUsername();
            phone = loginUser.getPhone();
        }
        if (oConvertUtils.isEmpty(departId)
                && oConvertUtils.isEmpty(realname)
                && oConvertUtils.isEmpty(times) && oConvertUtils.isEmpty(dataType)) {
            // 获取今年最新的一期
            LambdaQueryWrapper<ScYouthLearningEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.orderByDesc(ScYouthLearningEntity::getCreateTime);
            wrapper.last("limit 1");

            ScYouthLearningEntity scYouthLearningEntity = scYouthLearningService.getOne(wrapper);
            if (oConvertUtils.isNotEmpty(scYouthLearningEntity)) {
                currentDateList.add(scYouthLearningEntity.getTimes());
            }
        }
        if (oConvertUtils.isNotEmpty(times)) {
            currentDateList = Arrays.asList(times.split(","));
            Collections.sort(currentDateList);
        }
        // 期数大于1
        Page<Map<String, Object>> page = new Page<Map<String, Object>>(pageNo, pageSize);
        if (oConvertUtils.isNotEmpty(departId)) {
            SysDepart depart = sysDepartService.getById(departId);
            if (oConvertUtils.isNotEmpty(depart) && (depart.getParentId() == null || depart.getParentId().equals(""))) {
                departId = "";
            }
        }

        if (currentDateList.size() > 1) {
            pageList = scYouthLearningService.pageListMore(page, departId, studentCode, phone, realname, currentDateList, dataType, collegeId, classesId);
        } else {
            pageList = scYouthLearningService.pageList(page, departId, studentCode, phone, realname, currentDateList, dataType, collegeId, classesId);
        }
//        }
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 青年大学习-列表表头
     */
    @AutoLog(value = "青年大学习-列表表头")
    @ApiOperation(value = "青年大学习-列表表头", notes = "青年大学习-列表表头")
    @GetMapping(value = "/queryHeader")
    public Result<List<JSONObject>> queryHeaderd(@RequestParam(name = "departId", required = false) String departId,
                                                 @RequestParam(name = "studentCode", required = false) String studentCode,
                                                 @RequestParam(name = "realname", required = false) String realname,
                                                 @RequestParam(name = "dataType", required = false) String dataType,
                                                 @RequestParam(name = "times", required = false) String times) {
        Result<List<JSONObject>> result = new Result<List<JSONObject>>();
        List<String> currentDateList = new ArrayList<>();
        if (oConvertUtils.isNotEmpty(times)) {
            currentDateList = Arrays.asList(times.split(","));
            Collections.sort(currentDateList);
        } else {
            Calendar date = Calendar.getInstance();
            int year = date.get(Calendar.YEAR);
            // 获取今年最新的一期
            LambdaQueryWrapper<ScYouthLearningEntity> w = new LambdaQueryWrapper<>();
//            w.eq(ScYouthLearningEntity::getYears, oConvertUtils.null2String(year));
//            w.orderByDesc(ScYouthLearningEntity::getPeriods);
            w.orderByDesc(ScYouthLearningEntity::getCreateTime);
            w.last("limit 1");
            ScYouthLearningEntity scYouthLearningEntity = scYouthLearningService.getOne(w);
            if (oConvertUtils.isNotEmpty(scYouthLearningEntity)) {
                currentDateList.add(scYouthLearningEntity.getTimes());
            }
        }
        LambdaQueryWrapper<ScYouthLearningEntity> wrapper = new LambdaQueryWrapper<>();
        if (oConvertUtils.isNotEmpty(currentDateList) && currentDateList.size() > 0) {
            wrapper.in(ScYouthLearningEntity::getTimes, currentDateList);
        }
        if (oConvertUtils.isNotEmpty(dataType)) {
            if (dataType.equals("1")) {
                wrapper.eq(ScYouthLearningEntity::getDataType, dataType);
            } else {
                List<String> ids = new ArrayList<>();
                ids.add("0");
                ids.add("1");
                wrapper.in(ScYouthLearningEntity::getDataType, ids);
            }
        }
        wrapper.orderByAsc(ScYouthLearningEntity::getCreateTime);
        wrapper.groupBy(ScYouthLearningEntity::getQishu);
        wrapper.groupBy(ScYouthLearningEntity::getPeriods);
        List<ScYouthLearningEntity> scYouthLearningEntityList = scYouthLearningService.list(wrapper);
        List<JSONObject> list = new ArrayList<>();
        JSONObject jsonObject1 = new JSONObject();
        jsonObject1.put("text", "学号");
        jsonObject1.put("value", "studentCode");
        jsonObject1.put("sort", 1);
        JSONObject jsonObject2 = new JSONObject();
        jsonObject2.put("text", "姓名");
        jsonObject2.put("value", "username");
        jsonObject2.put("sort", 2);
        JSONObject jsonObject3 = new JSONObject();
        jsonObject3.put("text", "学院");
        jsonObject3.put("value", "college");
        jsonObject3.put("sort", 3);
        JSONObject jsonObject4 = new JSONObject();
        jsonObject4.put("text", "团支部");
        jsonObject4.put("value", "classes");
        jsonObject4.put("sort", 4);
        JSONObject jsonObject5 = new JSONObject();
        jsonObject5.put("text", "手机号");
        jsonObject5.put("value", "mobile");
        jsonObject5.put("sort", 5);
        if (currentDateList.size() > 1) {
            // 如果期数大于一期的化，改变表头
            JSONObject jsonObject6 = new JSONObject();
            jsonObject6.put("text", "累计学习次数");
            jsonObject6.put("value", "total");
            jsonObject6.put("sort", 6);
            list.add(jsonObject1);
            list.add(jsonObject2);
            list.add(jsonObject3);
            list.add(jsonObject4);
            list.add(jsonObject5);
            list.add(jsonObject6);
            // 循环遍历导入日期
            for (int j = 1; j <= scYouthLearningEntityList.size(); j++) {
                JSONObject jsonObject7 = new JSONObject();
                jsonObject7.put("text", "导入日期");
                jsonObject7.put("value", "importTime" + j);
                jsonObject7.put("sort", j + 5);
                list.add(jsonObject7);
            }
            JSONObject jsonObject8 = new JSONObject();
            jsonObject8.put("text", "学院");
            jsonObject8.put("value", "collegeTemp");
            jsonObject8.put("sort", 8);
            JSONObject jsonObject9 = new JSONObject();
            jsonObject9.put("text", "班级");
            jsonObject9.put("value", "classesTemp");
            jsonObject9.put("sort", 9);
            list.add(jsonObject8);
            list.add(jsonObject9);
        } else {
            JSONObject jsonObject6 = new JSONObject();
            jsonObject6.put("text", "期数");
            jsonObject6.put("value", "qishu");
            jsonObject6.put("sort", 6);
            JSONObject jsonObject7 = new JSONObject();
            jsonObject7.put("text", "学习日期");
            jsonObject7.put("value", "studytime");
            jsonObject7.put("sort", 7);
            JSONObject jsonObject8 = new JSONObject();
            jsonObject8.put("text", "导入日期");
            jsonObject8.put("value", "importTime");
            jsonObject8.put("sort", 8);
            JSONObject jsonObject9 = new JSONObject();
            jsonObject9.put("text", "累计学习次数（自始至当前）");
            jsonObject9.put("value", "total");
            jsonObject9.put("sort", 9);
            JSONObject jsonObject10 = new JSONObject();
            jsonObject10.put("text", "学院");
            jsonObject10.put("value", "collegeTemp");
            jsonObject10.put("sort", 10);
            JSONObject jsonObject11 = new JSONObject();
            jsonObject11.put("text", "班级");
            jsonObject11.put("value", "classesTemp");
            jsonObject11.put("sort", 11);
            list.add(jsonObject1);
            list.add(jsonObject2);
            list.add(jsonObject3);
            list.add(jsonObject4);
            list.add(jsonObject5);
            list.add(jsonObject6);
            list.add(jsonObject7);
            list.add(jsonObject8);
            list.add(jsonObject9);
            list.add(jsonObject10);
            list.add(jsonObject11);
        }
        result.setResult(list);
        return result;
    }


    /**
     * 青年大学习-列表excel导出
     *
     * @param departId
     * @param studentCode
     * @param realname
     * @param times
     * @return
     */

    @AutoLog(value = "青年大学习-列表excel导出")
    @ApiOperation(value = "青年大学习-列表excel导出", notes = "青年大学习-列表excel导出")
    @GetMapping(value = "/export/excel")
    public ModelAndView exportServiceExcl(@RequestParam(name = "departId", required = false) String departId,
                                          @RequestParam(name = "studentCode", required = false) String studentCode,
                                          @RequestParam(name = "realname", required = false) String realname,
                                          @RequestParam(name = "dataType", required = false) String dataType,
                                          @RequestParam(name = "times", required = false) String times) {
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        // 获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ExcelExportEntity> entityList = new ArrayList<>();

        // 团委负责人查看所有
        // 23.05.18改为团委和二领办看所有，下面所有权限判断除二领办全按部门表负责人字段判断
        SysUserRole YouthLeagueCommittee = sysRoleService.currentUserRile("role_xtwsj", sysUser.getId());// 二领办
        Boolean isTw = sysDepartService.isTwPersonInCharge(sysUser.getUsername());// 团委

        // 学院负责人查看本学院
        // 23.05.18 改为学院负责人和院团委负责人看本院
//        level = 2 代表包括学院的二级机构， orgType = 3 代表院团委
        List<SysDepart> chargeInXY = sysDepartService.XyChargeByUserId(sysUser.getUsername());// 负责的学院
        // 院团委则默认取所在学院
        List<SysDepart> chargeInYTW = sysDepartService.ChargeByUserId(sysUser.getUsername(), "3");// 负责的院团委
        List<String> xyByYtw = chargeInYTW.stream().map(SysDepart::getParentId).collect(Collectors.toList());
        // 班级负责人
        LambdaQueryWrapper<SysDepart> sysDepartLambdaQueryWrapper = Wrappers.<SysDepart>lambdaQuery();
        sysDepartLambdaQueryWrapper.eq(SysDepart::getOrgType, 4);
        String sql = "FIND_IN_SET('" + sysUser.getUsername() + "',person_in_charge)";
        sysDepartLambdaQueryWrapper.apply(sql);
        sysDepartLambdaQueryWrapper.eq(SysDepart::getDelFlag, "0");
        List<SysDepart> sysDeparts = sysDepartService.list(sysDepartLambdaQueryWrapper);
        IPage<Map<String, Object>> pageList = new Page<>();
        String collegeId = "";
        String classesId = "";
        String phone = "";
        if (YouthLeagueCommittee != null || isTw) {
            collegeId = "";
            classesId = "";
        } else if (chargeInXY.size() > 0 || chargeInYTW.size() > 0) {
            List<String> xyIdList = chargeInXY.stream().map(SysDepart::getId).collect(Collectors.toList());
            xyIdList.addAll(xyByYtw);
            Set<String> set = new HashSet<>(xyIdList);
            collegeId = String.join(",", set);
            classesId = sysDeparts.stream().map(SysDepart::getId).collect(Collectors.joining(","));
        } else if (sysDeparts.size() > 0) {
            if (oConvertUtils.isNotEmpty(sysDeparts)) {
                classesId = sysDeparts.stream().map(SysDepart::getId).collect(Collectors.joining(","));
            }
        } else {// 学生看自己
            // 查询自己所有数据
            studentCode = sysUser.getUsername();
            phone = sysUser.getPhone();
        }

        List<String> currentDateList = new ArrayList<>();
        if (oConvertUtils.isEmpty(departId) && oConvertUtils.isEmpty(realname)
                && oConvertUtils.isEmpty(times) && oConvertUtils.isEmpty(dataType)) {
            // 获取今年最新的一期
            LambdaQueryWrapper<ScYouthLearningEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.orderByDesc(ScYouthLearningEntity::getCreateTime);
            wrapper.last("limit 1");
            ScYouthLearningEntity scYouthLearningEntity = scYouthLearningService.getOne(wrapper);
            if (oConvertUtils.isNotEmpty(scYouthLearningEntity)) {
                currentDateList.add(scYouthLearningEntity.getTimes());
            }
        }
        if (oConvertUtils.isNotEmpty(times)) {
            currentDateList = Arrays.asList(times.split(","));
            Collections.sort(currentDateList);
        }
        // 获取导入日期动态表头
        LambdaQueryWrapper<ScYouthLearningEntity> wrapper = new LambdaQueryWrapper<>();

        if (oConvertUtils.isNotEmpty(currentDateList) && currentDateList.size() > 0) {
            wrapper.in(ScYouthLearningEntity::getTimes, currentDateList);
        }
        if (oConvertUtils.isNotEmpty(dataType)) {
            List<String> ids = new ArrayList<>();
            ids.add("1");
            if (dataType.equals("0")) {
                ids.add("0");
            }
            wrapper.in(ScYouthLearningEntity::getDataType, ids);
        }
        wrapper.groupBy(ScYouthLearningEntity::getPeriods);
        wrapper.groupBy(ScYouthLearningEntity::getQishu);
        wrapper.orderByAsc(ScYouthLearningEntity::getCreateTime);
        List<ScYouthLearningEntity> scYouthLearningEntityList = scYouthLearningService.list(wrapper);
        ExcelExportEntity entity = new ExcelExportEntity("学号", "studentCode");
        entity.setWidth(15);
        entityList.add(entity);
        entity = new ExcelExportEntity("姓名", "username");
        entity.setWidth(15);
        entityList.add(entity);
        entity = new ExcelExportEntity("学院", "college");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("团支部", "classes");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("手机号", "mobile");
        entity.setWidth(15);
        entityList.add(entity);
        if (currentDateList.size() > 1) {
            // 如果期数大于1则改变表头
            entity = new ExcelExportEntity("累计学习次数", "total");
            entity.setWidth(30);
            entityList.add(entity);
            for (int j = 1; j <= scYouthLearningEntityList.size(); j++) {
                entity = new ExcelExportEntity("导入日期", "importTime" + j);
                entity.setWidth(35);
                entityList.add(entity);
            }
            entity = new ExcelExportEntity("学院", "collegeTemp");
            entity.setWidth(15);
            entityList.add(entity);
            entity = new ExcelExportEntity("班级", "classesTemp");
            entity.setWidth(15);
            entityList.add(entity);
        } else {
            entity = new ExcelExportEntity("期数", "qishu");
            entity.setWidth(15);
            entityList.add(entity);
            entity = new ExcelExportEntity("学习日期", "studytime");
            entity.setWidth(20);
            entityList.add(entity);
            entity = new ExcelExportEntity("导入日期", "importTime");
            entity.setWidth(35);
            entityList.add(entity);
            entity = new ExcelExportEntity("累计学习次数（自始至当前）", "total");
            entity.setWidth(30);
            entityList.add(entity);
            entity = new ExcelExportEntity("学院", "collegeTemp");
            entity.setWidth(15);
            entityList.add(entity);
            entity = new ExcelExportEntity("班级", "classesTemp");
            entity.setWidth(15);
            entityList.add(entity);
        }
        List<Map<String, Object>> dataList = new ArrayList<>();
        // 查询并封装部门数据
        Map<String, Object> departMap = scYouthLearningService.getDepartMap();
        List<ScYouthLearningEntity> list = new ArrayList<>();
        if (oConvertUtils.isNotEmpty(departId)) {
            SysDepart depart = sysDepartService.getById(departId);
            if (oConvertUtils.isNotEmpty(depart) && (depart.getParentId() == null || depart.getParentId().equals(""))) {
                departId = "";
            }
        }
        // 如果期数大于1
        if (currentDateList.size() > 1) {
            list = scYouthLearningService.getExportListMore(studentCode, phone, realname, currentDateList, dataType, departId, collegeId, classesId);
        } else {
            list = scYouthLearningService.getExportList(studentCode, phone, realname, currentDateList, dataType, departId, collegeId, classesId);
        }
        Map<String, List<ScYouthLearningEntity>> getStudentDateMap = scYouthLearningService.getStudentDateMap(studentCode, realname, currentDateList, dataType, departId, collegeId, classesId);
        for (ScYouthLearningEntity scYouthLearningEntity : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("studentCode", oConvertUtils.null2String(scYouthLearningEntity.getStudentCode()));
            map.put("username", oConvertUtils.null2String(scYouthLearningEntity.getUsername()));
            String mobile = scYouthLearningEntity.getMobile();
            if (oConvertUtils.isNotEmpty(mobile)) {
                mobile = DesensitizedUtils.mobilePhone(mobile);
            }
            map.put("mobile", mobile);
            map.put("college", oConvertUtils.null2String(scYouthLearningEntity.getCollege()));
            map.put("classes", oConvertUtils.null2String(scYouthLearningEntity.getClasses()));
            if (oConvertUtils.isNotEmpty(scYouthLearningEntity.getCollegeId())) {
                String collegeName = "";
                String colId = scYouthLearningEntity.getCollegeId();
                // 如果存在多个学院或班级进行拼接
                List<String> collegeList = Arrays.asList(colId.split(","));
                if (collegeList.size() > 1) {
                    for (String str : collegeList) {
                        if (departMap.containsKey(str)) {
                            collegeName = oConvertUtils.null2String(departMap.get(str)) + "," + collegeName;
                        }
                    }
                    if (oConvertUtils.isNotEmpty(collegeName)) {
                        collegeName = collegeName.substring(0, collegeName.length() - 1);
//                            scYouthLearningEntity.setCollege(collegeName);
                    }
                    if (oConvertUtils.isEmpty(map.get("collegeTemp"))) {
                        map.put("collegeTemp", collegeName);
                    }
                } else {
                    if (oConvertUtils.isEmpty(map.get("collegeTemp"))) {
                        map.put("collegeTemp", oConvertUtils.null2String(departMap.get(colId)));
                    }
                }
            }
            if (oConvertUtils.isNotEmpty(scYouthLearningEntity.getClassesId())) {
                String clasId = scYouthLearningEntity.getClassesId();
                String classesName = "";
                // 如果存在多个学院或班级进行拼接
                List<String> classesList = Arrays.asList(clasId.split(","));
                if (classesList.size() > 1) {
                    for (String str : classesList) {
                        if (departMap.containsKey(str)) {
                            classesName = oConvertUtils.null2String(departMap.get(str)) + "," + classesName;
                        }
                    }
                    if (oConvertUtils.isNotEmpty(classesName)) {
                        classesName = classesName.substring(0, classesName.length() - 1);
//                            scYouthLearningEntity.setClasses(classesName);
                    }
                    if (oConvertUtils.isEmpty(map.get("classesTemp"))) {
                        map.put("classesTemp", classesName);
                    }
                } else {
                    if (oConvertUtils.isEmpty(map.get("classesTemp"))) {
                        map.put("classesTemp", oConvertUtils.null2String(departMap.get(clasId)));
                    }
                }
            }
            boolean result = true;
            // 如果期数大于1则改变表头数据
            if (currentDateList.size() > 1) {
                List<ScYouthLearningEntity> scYouthLearningEntityList1 = getStudentDateMap.get(scYouthLearningEntity.getMobile());
                int flag = 0;
                if (scYouthLearningEntityList1 != null && scYouthLearningEntityList1.size() > 0) {
                    int j = 1;
                    int flag2 = 0;
                    for (ScYouthLearningEntity sc1 : scYouthLearningEntityList) {
                        flag2 = 0;
                        for (ScYouthLearningEntity sc : scYouthLearningEntityList1) {
                            if (sc1.getQishu().equals(sc.getQishu())) {
                                flag2 = 1;
                                // 查询每个学生的导入日期（多期）
                                if (oConvertUtils.null2String(sc.getDataType()).equals("1")) {
                                    map.put("importTime" + j, DateUtils.formatDate(sc.getCreateTime(), ("yyyy-MM-dd HH:mm:ss")));
                                    result = false;
                                } else {
                                    flag = 1;
                                    String mobilePhone = "";
                                    if (oConvertUtils.isNotEmpty(sc.getMobile())) {
                                        mobilePhone = DesensitizedUtils.mobilePhone(oConvertUtils.null2String(sc.getMobile()));
                                    }
                                    map.put("importTime" + j, "未转化(" + mobilePhone + "手机号码不匹配" + ")");

                                }
                                break;
                            }
                        }
                        if (flag2 == 0) {
                            map.put("importTime" + j, "未导入");
                        }
                        j = j + 1;
                    }
                } else {
                    int c = scYouthLearningEntityList.size();
                    for (int m = 1; m <= c; m++) {
                        map.put("importTime" + m, "未导入");
                    }
                }
                if (flag == 1 && result) {
                    map.put("studentCode", "--");
                }
            } else {
                map.put("qishu", scYouthLearningEntity.getQishu());
                String studytime = "";
                if (oConvertUtils.isNotEmpty(scYouthLearningEntity.getStudytime())) {
                    studytime = DateUtils.formatDate(scYouthLearningEntity.getStudytime(), "yyyy-MM-dd HH:mm:ss");
                }
                map.put("studytime", studytime);
                if (scYouthLearningEntity.getDataType() == 1) {
                    map.put("importTime", DateUtils.formatDate(scYouthLearningEntity.getCreateTime(), "yyyy-MM-dd HH:mm:ss"));
                } else {
                    String mobilePhone = DesensitizedUtils.mobilePhone(scYouthLearningEntity.getMobile());
                    map.put("importTime", "未转化(" + mobilePhone + "手机号码不匹配" + ")");
                    map.put("studentCode", "--");
                }
            }
            map.put("total", scYouthLearningEntity.getTotal());
            dataList.add(map);
        }
//        }
        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("青年大学习导出数据");
        exportParams.setSecondTitle("导出人：" + sysUser.getRealname() + "，导出时间：" + DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss") + "【 导出说明： 当导出数据量过大时系统将会把数据分成多个Sheet（工作表）导出,默认Sheet从0开始依次递增 】");
        exportParams.setSheetName("Sheet0");
        // 导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "青年大学习导出列表");
        mv.addObject(MapExcelConstants.ENTITY_LIST, entityList);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        return mv;
    }


    /**
     * 青年大学习-期数
     */
    @AutoLog(value = "青年大学习-期数")
    @ApiOperation(value = "青年大学习-期数", notes = "青年大学习-期数")
    @GetMapping(value = "/number/periods")
    public Result<List<String>> numberPeriods(@RequestParam(name = "year", required = true) String year) {
        Result<List<String>> result = new Result<List<String>>();
        List<String> list = scYouthLearningService.numberPeriods(year);
        java.util.Collections.sort(list, new Comparator<String>() {
            @Override
            public int compare(String o1, String o2) {
                if (o1.contains("特") || o2.contains("特")) {
                    return 0;
                }
                return Integer.parseInt(o1) - Integer.parseInt(o2);
            }
        });
        result.setResult(list);
        result.setSuccess(true);
        return result;
    }


    /**
     * 青年大学习-源数据列表excel导出
     *
     * @param departId
     * @param studentCode
     * @param realname
     * @param times
     * @return
     */
    @AutoLog(value = "青年大学习-源数据列表excel导出")
    @ApiOperation(value = "青年大学习-源数据列表excel导出", notes = "青年大学习-源数据列表excel导出")
    @GetMapping(value = "/exportAll/excel")
    public ModelAndView exportAllServiceExcl(@RequestParam(name = "departId", required = false) String departId,
                                             @RequestParam(name = "studentCode", required = false) String studentCode,
                                             @RequestParam(name = "realname", required = false) String realname,
                                             @RequestParam(name = "dataType", required = false) String dataType,
                                             @RequestParam(name = "times", required = false) String times) {
        // 获取当前用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // 团委负责人查看所有
        SysUserRole YouthLeagueCommittee = sysRoleService.currentUserRile("role_twfzr", sysUser.getId());
        // 学院负责人查看本学院
        SysUserRole college = sysRoleService.currentUserRile("role_xyfzr", sysUser.getId());
        String collId = "";
        String clasId = "";
        if (oConvertUtils.isNotEmpty(college)) {
            LambdaQueryWrapper<SysUserDepart> sysUserDepartLambdaQueryWrapper = Wrappers.<SysUserDepart>lambdaQuery();
            sysUserDepartLambdaQueryWrapper.eq(SysUserDepart::getUserId, sysUser.getId());
            List<SysUserDepart> sysUserDepartList = sysUserDepartService.list(sysUserDepartLambdaQueryWrapper);
            List<String> ids = new ArrayList<>();
            if (sysUserDepartList.size() > 0) {
                for (SysUserDepart sysUserDepart : sysUserDepartList) {
                    ids.add(sysUserDepart.getDepId());
                }
                LambdaQueryWrapper<SysDepart> sysDepartLambdaQueryWrapper = Wrappers.<SysDepart>lambdaQuery();
                sysDepartLambdaQueryWrapper.in(SysDepart::getId, ids);
                sysDepartLambdaQueryWrapper.eq(SysDepart::getOrgType, 2);
                sysDepartLambdaQueryWrapper.last("LIMIT 1");
                SysDepart sysDeparts = sysDepartService.getOne(sysDepartLambdaQueryWrapper);
                if (oConvertUtils.isNotEmpty(sysDeparts)) {
                    collId = sysDeparts.getId();
                }
                if (oConvertUtils.isNotEmpty(YouthLeagueCommittee)) {
                    collId = "";
                }
            }
        } else {
            LambdaQueryWrapper<SysDepart> sysDepartLambdaQueryWrapper = Wrappers.<SysDepart>lambdaQuery();
            sysDepartLambdaQueryWrapper.eq(SysDepart::getOrgType, 4);
            String sql = "FIND_IN_SET('" + sysUser.getUsername() + "',headmaster)";
            sysDepartLambdaQueryWrapper.apply(sql);
            List<SysDepart> sysDeparts = sysDepartService.list(sysDepartLambdaQueryWrapper);
            if (oConvertUtils.isNotEmpty(sysDeparts)) {
                clasId = sysDeparts.stream().map(SysDepart::getId).collect(Collectors.joining("|"));
            }
            if (oConvertUtils.isNotEmpty(college) || oConvertUtils.isNotEmpty(YouthLeagueCommittee)) {
                clasId = "";
            }
        }
        List<String> currentDateList = new ArrayList<>();
        if (oConvertUtils.isEmpty(departId) && oConvertUtils.isEmpty(studentCode)
                && oConvertUtils.isEmpty(realname)
                && oConvertUtils.isEmpty(times) && oConvertUtils.isEmpty(dataType)) {
            Calendar date = Calendar.getInstance();
            int year = date.get(Calendar.YEAR);
            // 获取今年最新的一期
            LambdaQueryWrapper<ScYouthLearningEntity> wrapper = new LambdaQueryWrapper<>();
//            wrapper.eq(ScYouthLearningEntity::getYears, oConvertUtils.null2String(year));
//            wrapper.orderByDesc(ScYouthLearningEntity::getPeriods);
            wrapper.orderByDesc(ScYouthLearningEntity::getCreateTime);
            wrapper.last("limit 1");
            ScYouthLearningEntity scYouthLearningEntity = scYouthLearningService.getOne(wrapper);
            if (oConvertUtils.isNotEmpty(scYouthLearningEntity)) {
                currentDateList.add(scYouthLearningEntity.getTimes());
            }
        }
        if (oConvertUtils.isNotEmpty(times)) {
            currentDateList = Arrays.asList(times.split(","));
            Collections.sort(currentDateList);
        }

        Map<String, Object> departMap = scYouthLearningService.getDepartMap();
        if (oConvertUtils.isNotEmpty(departId)) {
            SysDepart depart = sysDepartService.getById(departId);
            if (oConvertUtils.isNotEmpty(depart) && (depart.getParentId() == null || depart.getParentId().equals(""))) {
                departId = "";
            }
        }

        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<ScYouthLearningVo> list = new ArrayList<>();
        if (oConvertUtils.isNotEmpty(YouthLeagueCommittee) || oConvertUtils.isNotEmpty(collId) || oConvertUtils.isNotEmpty(clasId)) {
            list = scYouthLearningService.getDate(studentCode, realname, currentDateList, dataType, departId, collId, clasId);
        }
        Collection datalist = new ArrayList();
        if (!CollectionUtils.isEmpty(list)) {
            for (ScYouthLearningVo scYouthLearningVo : list) {
                datalist.add(scYouthLearningVo);
            }
        }

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("青年大学习导出源数据");
        exportParams.setSecondTitle("导出人：" + sysUser.getRealname() + "，导出时间：" + DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss") + "【 导出说明： 当导出数据量过大时系统将会把数据分成多个Sheet（工作表）导出,默认Sheet从0开始依次递增 】");
        exportParams.setSheetName("Sheet0");
        // 导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "青年大学习源数据导出列表");
        // 注解对象Class
        mv.addObject(NormalExcelConstants.CLASS, ScYouthLearningVo.class);
        // 自定义表格参数
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        // 导出数据列表
        mv.addObject(NormalExcelConstants.DATA_LIST, datalist);
        return mv;
    }
}
