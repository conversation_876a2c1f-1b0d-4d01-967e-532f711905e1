package com.zs.create.modules.oa.credit.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.oa.credit.entity.OaCreditApplyInfoEntity;
import com.zs.create.modules.oa.credit.mapper.OaCreditApplyInfoMapper;
import com.zs.create.modules.oa.credit.service.OaCreditApplyInfoService;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysUserRole;
import com.zs.create.modules.system.service.ISysDepartService;
import com.zs.create.modules.system.service.ISysRoleService;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Description 学分申请统计Service实现层
 *
 * <AUTHOR> @email 
 * @date 2024-04-30 19:26:31
 * @Version: V1.0
 */
@Service
public class OaCreditApplyInfoServiceImpl extends ServiceImpl<OaCreditApplyInfoMapper, OaCreditApplyInfoEntity> implements OaCreditApplyInfoService {

    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private ISysRoleService sysRoleService;

    @Override
    public IPage<OaCreditApplyInfoEntity> qryCreditInfoPage(String userName, String departId, Integer pageNo, Integer pageSize) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Page<OaCreditApplyInfoEntity> page = new Page<OaCreditApplyInfoEntity>(pageNo, pageSize);

        List<String> depIds = new ArrayList<>();
        if (!StringUtils.isEmpty(departId) && !"211134".equals(departId)) {
            List<SysDepart> list = sysDepartService.getChildrenListByDeptId(departId);
            list.forEach(e -> depIds.add(e.getId()));
        }
        //二领办
        SysUserRole roleXtwsj = sysRoleService.currentUserRile("role_xtwsj", loginUser.getId());
        //团委
        Boolean isTw = sysDepartService.isTwPersonInCharge(loginUser.getUsername());
        //改为团委和二领办看所有
        if (roleXtwsj != null || isTw) {
            return baseMapper.qryCreditInfoPage(page,userName,depIds);
        }

        //判断是否是学院负责人
        List<String> collgeIds = sysDepartService.getCollegeByUserId(loginUser.getId());
        //获取管理的部门id，取交集
        Set<String> ids = new HashSet<>();
        if (collgeIds.size() > 0) {
            for (String id : collgeIds) {
                List<SysDepart> list = sysDepartService.getChildrenListByDeptId(id);
                list.forEach(e -> ids.add(e.getId()));
            }
        }
        //判断是否是班主任
        List<String> headmaster = sysDepartService.getHeadmasterDepIdsByUserId(loginUser.getId());
        if (headmaster.size() > 0) ids.addAll(headmaster);
        //选择部门和管理部门取交集
        List<String> departIds = ids.stream().filter(depIds::contains).collect(Collectors.toList());
        if (departIds.size() == 0) return page;

        return baseMapper.qryCreditInfoPage(page,userName,depIds);
    }

    /**
     * 根据查询条件查询导出数据列表
     * @param userName
     * @param departId
     * @return
     */
    @Override
    public List<OaCreditApplyInfoEntity> qryCreditInfoList(String userName, String departId) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<OaCreditApplyInfoEntity> oaCreditApplyInfoEntities = new ArrayList<>();
        List<String> depIds = new ArrayList<>();
        if (!StringUtils.isEmpty(departId) && !"211134".equals(departId)) {
            List<SysDepart> list = sysDepartService.getChildrenListByDeptId(departId);
            list.forEach(e -> depIds.add(e.getId()));
        }
        //二领办
        SysUserRole roleXtwsj = sysRoleService.currentUserRile("role_xtwsj", loginUser.getId());
        //团委
        Boolean isTw = sysDepartService.isTwPersonInCharge(loginUser.getUsername());
        //改为团委和二领办看所有
        if (roleXtwsj != null || isTw) {
            return baseMapper.qryCreditInfoList(userName,depIds);
        }

        //判断是否是学院负责人
        List<String> collgeIds = sysDepartService.getCollegeByUserId(loginUser.getId());
        //获取管理的部门id，取交集
        Set<String> ids = new HashSet<>();
        if (collgeIds.size() > 0) {
            for (String id : collgeIds) {
                List<SysDepart> list = sysDepartService.getChildrenListByDeptId(id);
                list.forEach(e -> ids.add(e.getId()));
            }
        }
        //判断是否是班主任
        List<String> headmaster = sysDepartService.getHeadmasterDepIdsByUserId(loginUser.getId());
        if (headmaster.size() > 0) ids.addAll(headmaster);
        //选择部门和管理部门取交集
        List<String> departIds = ids.stream().filter(depIds::contains).collect(Collectors.toList());
        if (departIds.size() == 0) return oaCreditApplyInfoEntities;
        return baseMapper.qryCreditInfoList(userName,depIds);
    }
}
