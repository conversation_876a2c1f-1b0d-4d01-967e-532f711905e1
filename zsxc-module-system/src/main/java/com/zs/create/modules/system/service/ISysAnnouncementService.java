package com.zs.create.modules.system.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.mq.entity.BrokerMessageLogDto;
import com.zs.create.modules.mq.entity.SysMessageDto;
import com.zs.create.modules.system.entity.SysAnnouncement;

/**
 * @Description: 系统通告表
 * @Author: zsxc
 * @Date: 2019-01-02
 * @Version: V1.0
 */
public interface ISysAnnouncementService extends IService<SysAnnouncement> {
    Page<SysAnnouncement> querySysCementPageByUserId(Page<SysAnnouncement> page, String userId, String msgCategory);

    Result<?> saveSysMessage(SysMessageDto sysMessageDto);

    IPage<BrokerMessageLogDto> queryMyMessagePageList(String title, String type, Integer pageNo, Integer pageSize);

    void deleteById(String id);

    Result<?> saveSysMessageForBackbone(SysMessageDto sysMessageDto);
}


