package com.zs.create.modules.backbone.AsyncHandel;

import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.FastJsonConvert;
import com.zs.create.common.util.UUIDGenerator;
import com.zs.create.modules.backbone.entity.BackboneLaunchEntity;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.entity.BrokerMessageLogDto;
import com.zs.create.modules.mq.entity.SysMessageDto;
import com.zs.create.modules.mq.producer.SysMessageSender;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * @description: 学生骨干的异步请求类
 * @author: anshenghui
 * @create: 2021-05-23 13:24
 **/
@Slf4j
@Component
public class AsyncHandel {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Autowired
    private SysMessageSender messageSender;

    @Autowired
    private SysWeixinUserService sysWeixinUserService;

    @Autowired
    private WxMessageSender wxMessageSender;

    @Value("${gzhAppid}")
    private String appid;

    @Async("taskExecutor")
    public void sendMessageLaunch(BackboneLaunchEntity backboneLaunchEntity) {
        LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String uuid = UUIDGenerator.generate();
        SysMessageDto sysMessage = new SysMessageDto();
        sysMessage.setMsgCategory(SysMessageDto.TZGGMSG);
        sysMessage.setTitle("学生骨干申报提醒");
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTime = simpleDateFormat.format(backboneLaunchEntity.getStartTime());
        String endTime = simpleDateFormat.format(backboneLaunchEntity.getEndTime());
        String content = "学生骨干已经发起申报，骨干评审标题为" + backboneLaunchEntity.getTitle() +
                "，申报时间为" + startTime + "至" + endTime + "，符合申报条件的人员可在办公系统—>学生骨干申报菜单进行申报";
        sysMessage.setContent(content);
        sysMessage.setSendType("1");
        sysMessage.setSendStatus(SysMessageDto.SEND_STATUS_PULISH_YES);
        sysMessage.setId(uuid);
        sysMessage.setCreateTime(new Date()).setStick(1);
        sysMessage.setPublicUser(loginUser.getRealname());
        sysMessage.setPublicTime(new Date());
        sysMessage.setSuggestionMsg("0");
        BrokerMessageLogDto messageLog =  new BrokerMessageLogDto();
        messageLog.setId(uuid)
                .setTitle(sysMessage.getTitle())
                .setCreateTime(new Date())
                .setStatus(1)
                .setDelFlag(0)
                .setCreateTime(new Date())
                .setUpdateTime(new Date())
                .setSendUser(loginUser.getRealname()).setShow(1)
                .setMessageObject(FastJsonConvert.convertObjectToJSON(sysMessage))
                .setMsgType("0").setPublicTime(new Date())
                .setType(sysMessage.getMsgCategory())
                .setSendStatus(sysMessage.getSendStatus());
        //保存消息日志
        BrokerMessageLogDto message = mongoTemplate.insert(messageLog);
        String sendMqContent = FastJsonConvert.convertObjectToJSON(message);
        messageSender.allMessageSend(sendMqContent,message.getId());
    }

    @Async("taskExecutor")
    public void sendWxMessage(String userNo, String title, StringBuilder contentBuilder, String remark, String appUrl) {
        WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(userNo, appid);
        if(null == wxMesUserInfo){
            log.info("****************************wxMesUserInfo结果为null");
            return;
        }
        String openId = wxMesUserInfo.getOpenId();
        String theme = wxMesUserInfo.getRealname() + "/"+ wxMesUserInfo.getUsername()+",您有一条新的工作提醒";
        WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                .setUseCommonTemplate(Boolean.TRUE)
                .setTheme(theme)
                .setTitle(title)
                .setUserId(wxMesUserInfo.getUsername())
                .setCreateDate(new Date())
                .setMiniAppUrl(appUrl)
                .setContent(contentBuilder.toString())
                .setOpenId(openId)
                .setRemark(remark);
        wxMessageSender.wxMessageSend(wxCommonMsgInfo);
    }
}
