<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.system.mapper.SysWorkflowMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.system.entity.SysWorkflowEntity" id="sysWorkflowMap">
        <result property="id" column="id"/>
        <result property="itemId" column="item_id"/>
        <result property="assignee" column="assignee"/>
        <result property="candidate" column="candidate"/>
        <result property="taskId" column="task_id"/>
        <result property="processInstanceId" column="process_instance_id"/>
        <result property="processInstanceKey" column="process_instance_key"/>
        <result property="auditNote" column="audit_note"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
    </resultMap>


    <select id="getOneByItemId" resultMap="sysWorkflowMap">
        SELECT * FROM sys_workflow where item_id=#{itemId} ORDER BY create_time DESC LIMIT 1
    </select>

</mapper>