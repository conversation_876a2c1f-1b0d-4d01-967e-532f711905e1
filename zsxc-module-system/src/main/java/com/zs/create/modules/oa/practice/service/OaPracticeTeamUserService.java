package com.zs.create.modules.oa.practice.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.modules.oa.practice.dto.OapracticeGivenHoursDto;
import com.zs.create.modules.oa.practice.entity.OaPracticeTeamEntity;
import com.zs.create.modules.oa.practice.entity.OaPracticeTeamUserEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @Description 团队成员信息Service层
 *
 * <AUTHOR> @email 
 * @date 2023-01-10 15:25:42
 * @Version: V1.0
 */
public interface OaPracticeTeamUserService extends IService<OaPracticeTeamUserEntity> {


    List<OaPracticeTeamUserEntity> selectUserByTeamIdAndStatus(String id, String teamRole);

    List<OaPracticeTeamEntity> checkTimeRepetition(String userCode, Date st, Date et, String teamId, String itemId);

    IPage<OapracticeGivenHoursDto> givenHourList(Page<OapracticeGivenHoursDto> page, OapracticeGivenHoursDto givenHoursDto);

    List<OapracticeGivenHoursDto> givenHourList(OapracticeGivenHoursDto givenHoursDto);

    OaPracticeTeamUserEntity getByTeamAndCode(String teamId, String userCode);

    Boolean resultSure(String itemId);

    /**
     * 学时学分更改消息提醒
     * @param oaPracticeTeamUserEntity
     */
    void sendHoursOrSorceMessage(OaPracticeTeamUserEntity oaPracticeTeamUserEntity);
}

