package com.zs.create.modules.system.asynchandel;

import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.mq.entity.MessageReciveUserDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

/**
 * Created with IntelliJ IDEA.
 *异步处理消息删除
 * @Author: yc
 * @Date: 2022/08/17/10:47
 * @Description:
 */
@Component
public class AnnouncementHand {

    @Autowired
    private MongoTemplate mongoTemplate;

    @Async("taskExecutor")
    public void handelMessageSendUsers(String messageId) {
        Criteria criteria = new Criteria();
        criteria.and("messageId").is(messageId);
        Query query = Query.query(criteria);
        mongoTemplate.remove(query, MessageReciveUserDto.MESSAGE_RECIVE_USER_COLLECTION_NAME);
    }
}
