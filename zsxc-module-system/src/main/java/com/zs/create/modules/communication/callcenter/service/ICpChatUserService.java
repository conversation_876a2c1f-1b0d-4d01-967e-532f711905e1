package com.zs.create.modules.communication.callcenter.service;

import com.zs.create.modules.communication.callcenter.entity.CpChatUserEntity;
import com.zs.create.modules.communication.callcenter.entity.dto.ChatUserDTO;

import java.util.List;

/**
 * @className: ICpChatUserService
 * @description: 聊天对象service
 * @author: hy
 * @date: 2020-12-1
 **/
public interface ICpChatUserService {

    List<CpChatUserEntity> listChatUser(String userId);

    CpChatUserEntity save(CpChatUserEntity cpChatUser);


    List<ChatUserDTO> listChatUserVo(String reciveUserId);

    Boolean deleteChartUser(String chatUserId, String kfUserId);
}
