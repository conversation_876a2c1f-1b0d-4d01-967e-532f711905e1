package com.zs.create.modules.statistic.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="院系统计", description="院系统计")
public class DepartItem implements Serializable {


    @ApiModelProperty(value = "机构名称")
    @Excel(name = "机构名称", width = 20)
    private String depName;
    @ApiModelProperty(value = "机构代码")
    private String depId;

    @ApiModelProperty(value = "总项目数")
    @Excel(name = "总项目数", width = 20,type =4)
    private Integer sumItem;


    @ApiModelProperty(value = "德")
    @Excel(name = "德", width = 20,type =4)
    private Integer virtue;


    @ApiModelProperty(value = "智")
    @Excel(name = "智", width = 20,type =4)
    private Integer wisdom;


    @ApiModelProperty(value = "体")
    @Excel(name = "体", width = 20,type =4)
    private Integer body;


    @ApiModelProperty(value = "美")
    @Excel(name = "美", width = 20,type =4)
    private Integer pretty;


    @ApiModelProperty(value = "劳")
    @Excel(name = "劳", width = 20,type =4)
    private Integer work;
}
