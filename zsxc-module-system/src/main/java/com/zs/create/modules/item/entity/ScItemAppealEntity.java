package com.zs.create.modules.item.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zs.create.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 项目-申诉
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-07-09 10:06:50
 */
@Data
@TableName("sc_item_appeal")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_item_appeal对象", description="项目-申诉")
public class ScItemAppealEntity implements Serializable {
	private static final long serialVersionUID = 1L;


	/**
	 * id
	 */
	@TableId
	private String id;
	/**
	 * 项目id
	 */
	private String itemId;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 用户名
	 */
	private String username;
	/**
	 * 申诉类型
	 */
	@Dict(dicCode = "appeal_type")
	private String appealType;
	/**
	 * 理由
	 */
	private String reason;
	/**
	 * 佐证
	 */
	private String prove;
	/**
	 * 是否处理(-1-未处理,0-驳回,1-通过)
	 */
	private String isHandle;
	/**
	 * 审核意见
	 */
	private String auditNote;
	/**
	 * 创建者
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 修改者
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 删除标志
	 */
	private String delFlag;
	/**
	 * 申请人员
	 */
	@TableField(exist = false)
	private List<Map> personList;
	/**
	 * 处理人员
	 */
	@TableField(exist = false)
	private String  ids;
	/**
	 * 筛选参数(用户学号)
	 */
	@TableField(exist = false)
	private String userCode;
	/**
	 * 筛选参数(学时类型)
	 */
	@TableField(exist = false)
	private String appealTypeName;
	/**
	 * 状态
	 */
	@TableField(exist = false)
	private String  isHandleText;



}
