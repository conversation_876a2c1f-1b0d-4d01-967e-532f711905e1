package com.zs.create.modules.oa.practice.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.entity.ScItemHoursEntity;
import com.zs.create.modules.item.service.ScItemHoursService;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.oa.practice.dto.OapracticeGivenHoursDto;
import com.zs.create.modules.oa.practice.entity.OaPracticeItemEntity;
import com.zs.create.modules.oa.practice.entity.OaPracticeTeamEntity;
import com.zs.create.modules.oa.practice.entity.OaPracticeTeamUserEntity;
import com.zs.create.modules.oa.practice.mapper.OaPracticeTeamUserMapper;
import com.zs.create.modules.oa.practice.service.OaPracticeItemService;
import com.zs.create.modules.oa.practice.service.OaPracticeTeamUserService;
import com.zs.create.modules.paramdesign.entity.ScWeeksEntity;
import com.zs.create.modules.paramdesign.service.ScWeeksService;
import com.zs.create.modules.score.entity.ScDayHoursEntity;
import com.zs.create.modules.score.service.ScDayHoursService;
import com.zs.create.modules.score.service.ScYearScoreService;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.service.ISysUserService;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import com.zs.create.modules.workflow.service.IWorkflowService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * @Description 团队成员信息Service实现层
 *
 * <AUTHOR> @email 
 * @date 2023-01-10 15:25:42
 * @Version: V1.0
 */
@Slf4j
@Service
public class OaPracticeTeamUserServiceImpl extends ServiceImpl<OaPracticeTeamUserMapper, OaPracticeTeamUserEntity> implements OaPracticeTeamUserService {

    @Resource
    private OaPracticeTeamUserMapper oaPracticeTeamUserMapper;

    @Autowired
    private OaPracticeItemService oaPracticeItemService;

    @Autowired
    private ScWeeksService scWeeksService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private ScItemHoursService scItemHoursService;

    @Autowired
    private ScDayHoursService scDayHoursService;

    @Autowired
    private IWorkflowService workflowService;

    @Autowired
    private ScYearScoreService scYearScoreService;

    @Autowired
    @Lazy
    private WxMessageSender wxMessageSender;

    @Autowired
    private SysWeixinUserService sysWeixinUserService;

    @Value("${gzhAppid}")
    private String appid;


    @Override
    public List<OaPracticeTeamEntity> checkTimeRepetition(String userCode, Date st, Date et, String teamId, String itemId) {
        return oaPracticeTeamUserMapper.checkTimeRepetition(userCode,st,et,teamId, itemId);
    }

    @Override
    public IPage<OapracticeGivenHoursDto> givenHourList(Page<OapracticeGivenHoursDto> page, OapracticeGivenHoursDto givenHoursDto) {
        return oaPracticeTeamUserMapper.givenHourList(page,givenHoursDto);
    }

    @Override
    public List<OapracticeGivenHoursDto> givenHourList(OapracticeGivenHoursDto givenHoursDto) {
        return oaPracticeTeamUserMapper.givenHourList(givenHoursDto);
    }

    @Override
    public OaPracticeTeamUserEntity getByTeamAndCode(String teamId, String userCode) {
        return oaPracticeTeamUserMapper.getByTeamAndCode(teamId,userCode);
    }


    @Override
    public List<OaPracticeTeamUserEntity> selectUserByTeamIdAndStatus(String id, String teamRole) {
        return oaPracticeTeamUserMapper.selectUserByTeamIdAndStatus(id,teamRole);
    }

    @Override
    public Boolean resultSure(String itemId) {
        OaPracticeItemEntity oaPracticeItemEntity = oaPracticeItemService.getById(itemId);
        if (oConvertUtils.isEmpty(oaPracticeItemEntity)) throw new ZsxcBootException("未找到对应实践项目");
        List<OaPracticeTeamUserEntity> oaPracticeTeamUserEntities = oaPracticeTeamUserMapper.queryResultSure(itemId);
        if (oaPracticeTeamUserEntities.size()==0) throw new ZsxcBootException("无学时确认人员");

        //逐条转化
        Date now = new Date();
        for (OaPracticeTeamUserEntity entity : oaPracticeTeamUserEntities) {
            dealData(entity,now,oaPracticeItemEntity);
        }
        return Boolean.TRUE;
    }

    @Override
    public void sendHoursOrSorceMessage(OaPracticeTeamUserEntity oaPracticeTeamUserEntity) {
        String userCode = oaPracticeTeamUserEntity.getUserCode();
        String title = "社会实践成绩体现选择";
        String remark = "青春科大智慧团学综合信息平台";
        WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(userCode, appid);
        if (null == wxMesUserInfo) {
            log.error("给团队成员发送学时学分选择消息，未找到接收人：{} 的信息", userCode);
            return;
        }
        String openId = wxMesUserInfo.getOpenId();
        String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的消息提醒";
        StringBuilder contentBuilder = new StringBuilder("您的社会实践成绩体现已修改为");
        if ("1".equals(oaPracticeTeamUserEntity.getSocreType())){
            contentBuilder.append("学分");
        }else {
            contentBuilder.append("学时");
        }
        WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                .setUseCommonTemplate(Boolean.TRUE)
                .setTheme(theme)
                .setTitle(title)
                .setUserId(wxMesUserInfo.getUsername())
                .setCreateDate(new Date())
                .setContent(contentBuilder.toString())
                .setOpenId(openId)
                .setRemark(remark);
        wxMessageSender.wxMessageSend(wxCommonMsgInfo);
    }

    public void dealData(OaPracticeTeamUserEntity entity, Date now, OaPracticeItemEntity oaPracticeItemEntity) {
        String userCode = entity.getUserCode();
        SysUser userByName = sysUserService.getUserByName(userCode);
        if (oConvertUtils.isEmpty(userByName)){
            return;
        }
        if (userByName.getType().equals("T")){
            return;
        }
        ScWeeksEntity weeksEntity = new ScWeeksEntity();
        try {
            weeksEntity = scWeeksService.getWeekMessage(now);
        }catch (Exception e){
            log.error("跳过学年学期配置时间之外的数据");
            return;
        }

        oaPracticeItemEntity.setAddHours("1");
        oaPracticeItemService.updateById(oaPracticeItemEntity);
//        BigDecimal hour = new BigDecimal(10);
        BigDecimal hour = new BigDecimal(32);

        try {
            //保存到学时表
            ScItemHoursEntity scItemHours = new ScItemHoursEntity();
            scItemHours.setItemId(oaPracticeItemEntity.getId()).setUserId(userCode)
                    .setUserCode(userCode)
                    .setUserName(entity.getUserName())
                    .setHours(hour)
                    .setCreateTime(new Date())
                    .setCreateBy("admin")
                    .setUpdateTime(now)
                    .setGiven(1)
                    .setType(1)//线上成绩
                    .setModule("l")
                    .setXn(weeksEntity.getXn())
                    .setXq(weeksEntity.getXq())
                    .setItemHours(Double.parseDouble(String.valueOf(hour)))
                    .setStatus(10)
                    .setDelFlag(0)
                    .setHoursType(8)//设置8为社会实践数据类型
                    .setDelRetain(0)
                    .setItemName(oaPracticeItemEntity.getPracticeItemName());
            scItemHoursService.save(scItemHours);

            //保存到学生每日学时数据,设置7为社会实践
            List<ScDayHoursEntity> result = scDayHoursService.getDayHours(scItemHours.getUpdateTime(),entity.getUserCode(),"6");
            if (result == null || result.size() == 0){
                ScDayHoursEntity scDayHoursEntity = new ScDayHoursEntity();
                scDayHoursEntity.setUserId(userCode)
                        .setUserName(entity.getUserName())
                        .setDay(now)
                        .setSumHours(hour)
                        .setCreateTime(new Date())
                        .setCreateBy("admin");
                scDayHoursEntity.setHours("l", hour);
                scDayHoursEntity.setHoursSocial(hour);
                scDayHoursEntity.setType("7");
                scDayHoursService.save(scDayHoursEntity);
            } else {
                result.get(0).setHoursSocial(hour);
                result.get(0).setHours("l", hour);
                result.get(0).setHours(null, hour);
                scDayHoursService.updateById(result.get(0));
            }

            //更新学年
            scYearScoreService.updateScore(scItemHours,5);

            //更新确认字段
            oaPracticeItemEntity.setAddHours("1");
            oaPracticeItemService.updateById(oaPracticeItemEntity);

        }catch (Exception e){
            oaPracticeItemEntity.setAddHours("0");
            oaPracticeItemService.updateById(oaPracticeItemEntity);
            throw new ZsxcBootException("实践学时确认失败：{}",e);
        }

    }
}

