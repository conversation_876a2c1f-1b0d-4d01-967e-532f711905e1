package com.zs.create.modules.statistic.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="项目统计导出", description="项目统计导出")
public class ItemStatisticVo implements Serializable {
    @ApiModelProperty(value = "项目名称")
    @Excel(name = "项目名称", width = 20)
    private String itemName;

    @Excel(name = "项目模块", width = 20)
    @ApiModelProperty(value = "项目模块")
    private String module;

    @Excel(name = "项目形式", width = 20)
    @ApiModelProperty(value = "项目形式")
    private String form;

    @Excel(name = "项目级别", width = 20)
    @ApiModelProperty(value = "项目级别")
    private String activityLevel;

    @Excel(name = "组织方", width = 20)
    @ApiModelProperty(value = "组织方")
    private String businessDeptName;

    @Excel(name = "联系人", width = 20)
    @ApiModelProperty(value = "联系人")
    private String linkMan;

    @Excel(name = "举办开始时间", width = 20,format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "举办开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date st;

    @Excel(name = "举办结束时间", width = 20,format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "举办结束时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date et;

    @Excel(name = "创建时间", width = 20,format = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date createTime;

    @Excel(name = "参与人数", width = 20,type = 4)
    @ApiModelProperty(value = "参与人数")
    private Integer partakeNum;

    @Excel(name = "总赋予学时", width = 20,type = 4)
    @ApiModelProperty(value = "总赋予学时")
    private BigDecimal totalServiceHour;

    @Excel(name = "状态", width = 20)
    @ApiModelProperty(value = "状态")
    private String examineStatus;
}
