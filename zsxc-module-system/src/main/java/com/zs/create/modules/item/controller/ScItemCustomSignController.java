package com.zs.create.modules.item.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.item.entity.ScItemCustomSignEntity;
import com.zs.create.modules.item.service.ScItemCustomSignService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * @Description 自定义报名范围Controller层
 *
 * <AUTHOR>
 * @email null
 * @date 2020-11-05 10:04:36
 * @Version: V1.0
 */
@Slf4j
@Api(tags="自定义报名范围")
@RestController
@RequestMapping("/item/scItemCustomSign")
public class ScItemCustomSignController {
    @Autowired
    private ScItemCustomSignService scItemCustomSignService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "自定义报名范围-分页列表查询")
    @ApiOperation(value="自定义报名范围-分页列表查询", notes="自定义报名范围-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<ScItemCustomSignEntity>> queryPageList(ScItemCustomSignEntity scItemCustomSign,
                                                        @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                        @RequestParam(name="pageSize", defaultValue="10") Integer pageSize,
                                                        HttpServletRequest req) {
        Result<IPage<ScItemCustomSignEntity>> result = new Result<>();
        QueryWrapper<ScItemCustomSignEntity> queryWrapper =new QueryWrapper<>();
        if (StringUtils.isNotBlank(scItemCustomSign.getUsername()))
            queryWrapper.like("username",scItemCustomSign.getUsername());
        if (StringUtils.isNotBlank(scItemCustomSign.getRealname()))
            queryWrapper.and(i->i.like("realname",scItemCustomSign.getRealname()).or().like("username",scItemCustomSign.getRealname()));

        queryWrapper.eq("uuid_key",scItemCustomSign.getUuidKey());

        queryWrapper.groupBy("username");
        Page<ScItemCustomSignEntity> page = new Page<>(pageNo, pageSize);
        IPage<ScItemCustomSignEntity> pageList = scItemCustomSignService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "自定义报名范围-添加")
    @ApiOperation(value="自定义报名范围-添加", notes="自定义报名范围-添加")
    @PostMapping(value = "/add")
    public Result<ScItemCustomSignEntity> add(@RequestBody ScItemCustomSignEntity scItemCustomSign) {
        Result<ScItemCustomSignEntity> result = new Result<>();
        try {
            scItemCustomSignService.save(scItemCustomSign);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "自定义报名范围-编辑")
    @ApiOperation(value="自定义报名范围-编辑", notes="自定义报名范围-编辑")
    @PutMapping(value = "/edit")
    public Result<ScItemCustomSignEntity> edit(@RequestBody ScItemCustomSignEntity scItemCustomSign) {
        Result<ScItemCustomSignEntity> result = new Result<>();
        ScItemCustomSignEntity scItemCustomSignEntity = scItemCustomSignService.getById(scItemCustomSign.getId());
        if(scItemCustomSignEntity==null) {
            result.error500("未找到对应实体");
        }else {
            boolean ok = scItemCustomSignService.updateById(scItemCustomSign);
            if(ok) {
                result.success("修改成功!");
            }
        }
        return result;
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "自定义报名范围-通过id删除")
    @ApiOperation(value="自定义报名范围-通过id删除", notes="自定义报名范围-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id",required=true) String id) {
        try {
            scItemCustomSignService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败",e.getMessage());
            return Result.error("删除失败!");
        }
		return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "自定义报名范围-批量删除")
    @ApiOperation(value="自定义报名范围-批量删除", notes="自定义报名范围-批量删除")
    @DeleteMapping(value = "/deleteBatch")
    public Result<ScItemCustomSignEntity> deleteBatch(@RequestParam(name="ids",required=true) String ids) {
        Result<ScItemCustomSignEntity> result = new Result<>();
        if(ids==null || "".equals(ids.trim())) {
            result.error500("参数不识别！");
        }else {
            this.scItemCustomSignService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
		return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "自定义报名范围-通过id查询")
    @ApiOperation(value="自定义报名范围-通过id查询", notes="自定义报名范围-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScItemCustomSignEntity> queryById(@RequestParam(name="id",required=true) String id) {
        Result<ScItemCustomSignEntity> result = new Result<>();
        ScItemCustomSignEntity scItemCustomSign = scItemCustomSignService.getById(id);
        if(scItemCustomSign==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(scItemCustomSign);
            result.setSuccess(true);
        }
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        // Step.1 组装查询条件
        QueryWrapper<ScItemCustomSignEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                ScItemCustomSignEntity scItemCustomSign = JSON.parseObject(deString, ScItemCustomSignEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(scItemCustomSign, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<ScItemCustomSignEntity> pageList = scItemCustomSignService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "自定义报名范围列表");
        mv.addObject(NormalExcelConstants.CLASS, ScItemCustomSignEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("自定义报名范围列表数据", "导出人:Jeecg", "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ScItemCustomSignEntity> listScItemCustomSigns = ExcelImportUtil.importExcel(file.getInputStream(), ScItemCustomSignEntity.class, params);
                scItemCustomSignService.saveBatch(listScItemCustomSigns);
                return Result.ok("文件导入成功！数据行数:" + listScItemCustomSigns.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
