package com.zs.create.modules.item.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.DateUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.item.entity.*;
import com.zs.create.modules.item.enums.ItemAuditStatusEnum;
import com.zs.create.modules.item.service.*;
import com.zs.create.modules.item.utils.FileUtil;
import com.zs.create.modules.system.entity.SysUser;
import com.zs.create.modules.system.service.ISysDictService;
import com.zs.create.modules.system.service.ISysUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDateTime;
import java.util.*;


/**
 * @Description 学时Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-07-07 17:08:27
 * @Version: V1.0
 */
@Slf4j
@Api(tags="项目-总结")
@RestController
@RequestMapping("/item/summary")
public class ScItemSummaryController {
    @Autowired
    @Lazy
    ScItemHoursService scItemHoursService;
    @Autowired
    ScItemService scItemService;
    @Autowired
    ISysDictService dictService;
    @Autowired
    ScItemRegistrationService scItemRegistrationService;
    @Autowired
    ScItemSignService scItemSignService;
    @Autowired
    ScItemWorksService scItemWorksService;
    @Autowired
    ISysUserService sysUserService;
    @Autowired
    private CampusAppService campusAppService;

    @AutoLog(value = "报名-报名人员")
    @ApiOperation(value="报名-报名人员", notes="报名-报名人员")
    @GetMapping("/listRegistrationUser")
    public Result<?> listRegistrationUser(RegistrationUserDTO registrationUserDTO,
                                          @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                          @RequestParam(name="pageSize", defaultValue="10") Integer pageSize){
        IPage<ScItemRegistrationEntity> pageList = scItemHoursService.pageRegistrationUser(registrationUserDTO , pageNo , pageSize);
        Result<IPage<ScItemRegistrationEntity>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    @AutoLog(value = "项目-导入人员角色检查")
    @ApiOperation(value="报名-导入人员角色检查", notes="报名-导入人员角色检查")
    @GetMapping("/checkUserRole")
    public Result<Boolean> checkUserRole () {
        Result<Boolean> result = new Result<>();
        Boolean flag = sysUserService.checkUserRole();
        result.setResult(flag);
        result.setSuccess(true);
        return result;
    }

    @AutoLog(value = "项目-学时赋予批量删除权限检查")
    @ApiOperation(value="报名-学时赋予批量删除权限检查", notes="报名-学时赋予批量删除权限检查")
    @GetMapping("/checkUserDelRole")
    public Result<Boolean> checkUserDelRole (@RequestParam(name = "itemId")String itemId) {
        Result<Boolean> result = new Result<>();
        ScItemEntity scItemEntity = scItemService.getById(itemId);
        if (scItemEntity.getExamineStatus().equals(ItemAuditStatusEnum.PUBLISH.getCode())){
            result.setResult(true);
            result.setSuccess(true);
            return result;
        }
        Boolean flag = sysUserService.checkUserRole();
        result.setResult(flag);
        result.setSuccess(true);
        return result;
    }


    @AutoLog(value = "项目-学时")
    @ApiOperation(value="项目-学时列表分页", notes="项目-学时列表分页")
    @GetMapping(value = "/listHours")
    public Result<?> listHoursPage(@Valid ItemHoursDTO itemHoursDTO ,
                                   @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                   @RequestParam(name="pageSize", defaultValue="10") Integer pageSize){
        return scItemHoursService.listHoursPage(itemHoursDTO,pageNo,pageSize);
    }


    @AutoLog(value = "项目-学时赋予导出")
    @ApiOperation(value="项目-学时赋予导出", notes="项目-学时赋予导出")
    @GetMapping(value = "/hoursExportXls")
    public ModelAndView hoursExportXls(@Valid ItemHoursDTO itemHoursDTO) {
        return scItemHoursService.hoursExportXls(itemHoursDTO);
    }

    @AutoLog(value = "项目-学时删除")
    @ApiOperation(value="项目-学时删除[批量删除]", notes="项目-学时删除[批量删除]")
    @DeleteMapping(value = "/hours/batchDel/{itemId}")
    @NoRepeatSubmit(expireSeconds = 2)
    public Result<?> batchDelUserHours(@RequestParam(name="ids",required=true) String ids , @PathVariable String itemId){
        scItemHoursService.batchDelUserHours(ids , itemId);
        return Result.ok("学时删除成功");
    }

    @AutoLog(value = "项目-学时删除")
    @ApiOperation(value="项目-学时删除[批量删除]", notes="项目-学时删除[批量删除]")
    @PostMapping(value = "/hours/batchDelWork")
    @NoRepeatSubmit(expireSeconds = 2)
    public Result<?> batchDelWork(@RequestBody Map map){
        scItemHoursService.batchDelWork(map);
        return Result.ok("学时删除成功");
    }

    @AutoLog(value = "项目-学时确认")
    @ApiOperation(value="项目-学时确认", notes="项目-学时确认")
    @PostMapping("hours/confirm/{itemId}")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> confirmHours(@PathVariable String itemId){
        scItemHoursService.confirmHours(itemId);
        return Result.ok("学时公示确认成功");
    }

    @AutoLog(value = "项目-学时赋予最终提交")
    @ApiOperation(value="项目-学时赋予最终提交", notes="项目-学时赋予最终提交")
    @PostMapping("hours/submit/{itemId}")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> submitScore(@PathVariable String itemId){
        scItemHoursService.submitHours(itemId,null);
        return Result.ok("学时提交审核成功");
    }

    @AutoLog(value = "课程-成绩确认")
    @ApiOperation(value="项课程-成绩确认", notes="项目-成绩确认")
    @PostMapping("hours/submitHoursCourse/{itemId}")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> submitHoursCourse(@PathVariable String itemId){
        scItemHoursService.submitHoursCourse(itemId);
        return Result.ok("成绩确认成功");
    }

    /**
     * 成绩-审核任务列表查询
     * @return
     */
    @GetMapping("taskList")
    @AutoLog(value = "成绩-审核任务列表查询")
    @ApiOperation(value="成绩-审核任务列表查询", notes="项目-审核任务列表查询")
    public Result<?> taskList(ScHoursDirectoryVo entity ,
                              @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                              @RequestParam(name="pageSize", defaultValue="10") Integer pageSize){
        Page<ScHoursDirectoryVo> page = scItemHoursService.taskPage(entity , pageNo ,pageSize);
        return Result.ok(page);
    }


    @PostMapping("auditItem")
    @AutoLog(value = "项目成绩审核")
    @ApiOperation(value="项目成绩审核", notes="项目成绩审核")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> auditItem(@RequestBody @Valid HonorAuditDTO honorAuditDTO){
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Map<String , Object> variables = new HashMap<>();
        variables.put("status" , honorAuditDTO.getStatus());
        String processInstanceId = scItemHoursService.auditHonorProcess(honorAuditDTO.getTaskId(),
                honorAuditDTO.getAuditNote(), sysUser.getId(), variables);
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
        return Result.ok("审核成功");

    }



    @AutoLog(value = "成绩详情分页列表")
    @ApiOperation(value="报名-报名人员", notes="报名-报名人员")
    @GetMapping("/getScorePage")
    public Result<?> getScorePage(RegistrationUserDTO registrationUserDTO,
                                          @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                          @RequestParam(name="pageSize", defaultValue="10") Integer pageSize){
        IPage<ScItemHoursEntity> pageList = scItemHoursService.getScorePage(registrationUserDTO, pageNo , pageSize);
        Result<IPage<ScItemHoursEntity>> result = new Result<>();
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    @AutoLog(value = "项目总结-报名人员赋予")
    @ApiOperation(value="项目总结-报名人员赋予", notes="项目总结-报名人员赋予")
    @PostMapping("/givenRegistrationUserHours")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> givenRegistrationUserHours(@RequestBody  RegistrationUserDTO registrationUserDTO){
        scItemHoursService.givenRegistrationUserHours(registrationUserDTO.getOperateAction() , registrationUserDTO.getItemId() , registrationUserDTO.getCheckUserIds());
        return Result.ok("操作成功");
    }

    @GetMapping("/canGivenHours/{itemId}")
    public Result<?> canGivenHours(@PathVariable String itemId){
        ScItemEntity item = scItemService.getById(itemId);
        if(null == item) throw new ZsxcBootException("项目id参数错误");
        LocalDateTime now  = LocalDateTime.now();
        if(null != item.getEt()){
            LocalDateTime et = DateUtils.dateToLocalDateTime(item.getEt());
            if(now.isAfter(et)) return Result.ok(true);
            return Result.ok(false);
        }else{
            return Result.error("项目数据有误,不可以赋予学时");
        }
    }

    @GetMapping("/countHours/{itemId}")
    public Result<?> countHours(@PathVariable String itemId){
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("item_id" , itemId);
        int count = scItemHoursService.count(queryWrapper);
        return Result.ok(count);
    }






     /*@AutoLog(value = "项目-学时赋予新增")
    @ApiOperation(value="项目-学时赋予新增", notes="项目-学时赋予新增")
    @PostMapping("hours/save")
    public Result<?> save(@RequestBody ScItemHoursEntity scItemHoursEntity){
        scItemHoursService.saveScItemHours(scItemHoursEntity);
        return Result.ok("添加成功");
    }*/



    /*@AutoLog(value = "项目-学时赋予模板下载")
    @ApiOperation(value="项目-学时赋予模板下载", notes="项目-学时赋予模板下载")
    @GetMapping(value = "/exportXls/{itemId}")
    public void exportXls(@PathVariable String itemId , HttpServletResponse response)  {
        ScItemEntity item = scItemService.getById(itemId);
        if(null == item) throw new ZsxcBootException("itemId参数不正确");
        String templateName = item.getItemName() + "学时赋予模板下载";
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        ExportParams params = new ExportParams(templateName, "导出人:" + sysUser.getRealname(), "学时导入模板信息");
        final Workbook workbook = ExcelExportUtil.exportExcel(params,
                ItemHoursDTO.class, null);
        List<DictModel> dict = dictService.queryDictItemsByCode(ItemHoursDTO.DICT_GIVEN_CODE);
        String[] givenArray = dict.stream().map(DictModel::getText).toArray(String[]::new);
        ExcelSelectListUtil.selectList(workbook , 2 , 2 , givenArray);
        OutputStream out = null;
        try {
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            out = response.getOutputStream();
            workbook.write(out);
        } catch (IOException e) {
            e.printStackTrace();
        }finally {
            try {
                if (null != out)
                    out.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    @AutoLog(value = "项目-学时赋予模板导入")
    //@ApiOperation(value="项目-学时赋予模板导入", notes="项目-学时赋予模板导入")
    @PostMapping(value = "/importXls/{itemId}")
    public Result<?> importExcel(HttpServletRequest request ,@PathVariable String itemId) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<ItemHoursDTO> itemHoursDTOS = ExcelImportUtil.importExcel(file.getInputStream(), ItemHoursDTO.class, params);
                scItemHoursService.batchSaveItemHoursDTO(itemHoursDTOS , itemId);
                return Result.ok("文件导入成功！数据行数:" + itemHoursDTOS.size());
            } catch (Exception e) {
                log.error(e.getMessage(),e);
                return Result.error("文件导入失败:"+e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }
*/
     //学时人员模板下载
    @AutoLog(value = "项目-学时人员模板下载")
    @ApiOperation(value="项目-学时人员模板下载", notes="项目-学时人员模板下载")
    @GetMapping(value = "/exportXls")
    public void exportXls(String itemId,String flag,HttpServletResponse response) {
        ScItemEntity item=scItemService.getById(itemId);
        if (item==null) throw new ZsxcBootException("当前项目不存在");
        List<HoursDto> pageList = new ArrayList<HoursDto>();
        List<ScItemRegistrationEntity> list=scItemHoursService.listUser(itemId);

        if (!list.isEmpty()){

            if (flag !=null && flag.equals("qdImport")){
                for (ScItemRegistrationEntity user : list) {
                    HoursDto hoursDto=new HoursDto();
                    hoursDto.setUsername(user.getUsername());
                    hoursDto.setRealname(user.getRealname());
                    hoursDto.setIsno("");
                    pageList.add(hoursDto);
                }
            }else {
                for (ScItemRegistrationEntity user : list) {
                    HoursDto hoursDto = new HoursDto();
                    hoursDto.setUsername(user.getUsername());
                    hoursDto.setRealname(user.getRealname());
                    hoursDto.setIsno("是");
                    pageList.add(hoursDto);
                }
            }
        }
        FileUtil.exportExcel(pageList,item.getItemName()+"-学时导入模板","项目学时人员",HoursDto.class,item.getItemName()+"-学时导入模板.xls",response);
    }


    /**
     * 通过excel导入数据
     */
    @AutoLog(value = "项目-学时人员导入")
    @ApiOperation(value="项目-学时人员导入", notes="项目-学时人员导入")
    @PostMapping(value = "/importExcel")
    @ResponseBody
    public Result<?> importExcel(String itemId,MultipartFile file,String flag){
        Result<?> result = new Result<>();
        String str = file.getOriginalFilename().
                substring(file.getOriginalFilename().lastIndexOf(".") + 1);
        if (!"xls".equals(str)&&!"xlsx".equals(str)) {
            return result.error500("文件的格式不正确,请使用所下载的模板上传数据！！");
        }
//        int successCnt = 0;
        String massge="";
        List<String> users=new ArrayList<>();
        List<String> cancelAddUsers=new ArrayList<>();
        List<String> usernames=new ArrayList<>();
        //错误数据记录集合
        List<String> errors = new LinkedList<>();
        List<String> regisUser = new ArrayList<>();
        List<ScItemRegistrationEntity> entities=scItemHoursService.listUser(itemId);
        if (entities.isEmpty()) throw new ZsxcBootException("当前项目暂无参与人员");
        for (ScItemRegistrationEntity entity:entities){
            usernames.add(entity.getUsername());
        }
        ImportParams params = new ImportParams();
        params.setTitleRows(1);
        params.setHeadRows(1);
        try {
            List<HoursDto> list = ExcelImportUtil.importExcel(file.getInputStream(), HoursDto.class, params);
            if (CollectionUtils.isEmpty(list)) throw new ZsxcBootException("未匹配到任何数据...");
            for (int i=0;i<list.size();i++) {

                if ((list.get(i).getUsername()==null || list.get(i).getUsername().trim().length()==0)
                        &&(list.get(i).getRealname()==null || list.get(i).getRealname().trim().length()==0)){
                    continue;
                }

                SysUser students = sysUserService.getUserByName(list.get(i).getUsername());
                if (null == students){
                    massge=("第" + (i + 3) + "条信息为离校人员，无法导入!");
                    errors.add(massge);
                    continue;
                }
                if ( !list.get(i).getRealname().equals(students.getRealname())) {
                    massge=("第" + (i + 3) + "条数据，学生学工号或姓名不正确 ，请检查!");
                    errors.add(massge);
                    continue;
                }
                String isno = list.get(i).getIsno();
                if (isno==null|| isno.equals("")){
                    massge=("第" + (i + 3) + "条数据，请确认学生是否加入成绩 !");
                    errors.add(massge);
                    continue;
                }
                if (isno.equals("1")){
                    users.add(list.get(i).getUsername());
                }else {
                    cancelAddUsers.add(list.get(i).getUsername());
                }
            }

            if (flag!=null && flag.equals("qdImport")){
//            if (flag==null){
                regisUser = new ArrayList<>(usernames);
                if (!users.isEmpty() || !cancelAddUsers.isEmpty()){
                    usernames.retainAll(users);
                    regisUser.retainAll(cancelAddUsers);
                    if (usernames.isEmpty() && regisUser.isEmpty()) throw new ZsxcBootException("导入人员没有参与项目");
                }else {
                    throw new ZsxcBootException("导入人员错误");
                }


                String giveHours = String.join(",",usernames);
                String noGiven = String.join(",",regisUser);
                if (oConvertUtils.isNotEmpty(giveHours)) {
                    scItemHoursService.qdClassgivenRegistrationHours(1, itemId, giveHours);
                }
                if (oConvertUtils.isNotEmpty(noGiven)) {
                    scItemHoursService.qdClassgivenRegistrationHours(0, itemId, noGiven);
                }

            }else {
                if (!users.isEmpty()) {
                    usernames.retainAll(users);
                    if (usernames.isEmpty()) throw new ZsxcBootException("导入人员没有参与项目");
                } else {
                    throw new ZsxcBootException("导入人员错误或不加入成绩");
                }
                //方案一：使用String.join()函数，给函数传递一个分隔符合一个迭代器，一个StringJoiner对象会帮助我们完成所有的事情
                String string1 = String.join(",", usernames);
                scItemHoursService.givenRegistrationUserHours(1, itemId, string1);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            //返回ExcelImportUtil里自带的错误提示
            if (e.getMessage()==null || e.getMessage().trim().length()==0) {
                return Result.error("导入文件与模板格式不一致，请检查！");
            }
            return Result.error(e.getMessage());
        } finally {
            try {
                file.getInputStream().close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        if (flag!=null && flag.equals("qdImport")){
            int allSize = usernames.size()+regisUser.size();
            result.success("成功导入：" + allSize + "条,导入失败："
                    + errors.size() + "条"
                    + (CollectionUtils.isEmpty(errors) ? "" :
                    ",错误原因：" + String.join(",", errors)));
        }else {
            result.success("成功导入：" + usernames.size() + "条,导入失败："
                    + errors.size() + "条"
                    + (CollectionUtils.isEmpty(errors) ? "" :
                    ",错误原因：" + String.join(",", errors)));
        }
        return result;
    }

}
