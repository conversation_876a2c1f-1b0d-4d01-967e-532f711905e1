<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.ticketManagement.mapper.OaVenueSeatMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.ticketManagement.entity.OaVenueSeatEntity" id="oaVenueSeatMap">
        <result property="id" column="id"/>
        <result property="venueId" column="venue_id"/>
        <result property="codeX" column="code_x"/>
        <result property="codeY" column="code_y"/>
        <result property="isSeat" column="is_seat"/>
        <result property="isUse" column="is_use"/>
        <result property="seatNumber" column="seat_number"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>


</mapper>