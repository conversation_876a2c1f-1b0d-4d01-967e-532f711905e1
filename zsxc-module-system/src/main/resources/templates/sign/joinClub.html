<!DOCTYPE html>
<html lang="en" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no">
    <title>加入社团</title>
    <style>
        body {
            margin: 0;
        }

        .clubCnt {
            width: 100%;
            height: 100%;
            background: url([[${qrUrl} + 'images/clubBg.jpg' ]]) no-repeat;
            background-size: 100% 100%;
        }

        .clubWrap {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            box-sizing: border-box;
        }

        .clubBox {
            text-align: center;
        }

        .clubBox1 {
            padding-top: 6%;
        }

        .clubBox1 img {
            width: 88%;
        }

        .clubBox2 {
            padding-top: 6%;
        }

        .clubBox2 img {
            width: 70%;
        }

        .clubBox3 {
            min-height: 35%;
            padding-top: 5%;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .clubDeptCnt {
            width: 92%;
            margin: 0 auto;
            background: #ffe8bb;
            border: 4px solid #ff9c61;
            border-radius: 10px;
            padding: 9px 9px;
        }

        .deptName {
            background-color: #fff;
            text-align: center;
            padding: 10px 10px;
            width: 95%;
            margin: 0 auto;
            color: #333333;
            font-size: 16px;
            font-weight: bold;
            border-bottom: 1px solid #d47e4a;
        }

        .dptWrap {
            border: 2px dashed #da7d3f;
        }

        .clubBox4 {
            padding-top: 7%;
            padding-bottom: 7%;
        }

        .joinBtn {
            width: 88%;
            background: #ff7e94;
            border: 3px solid #303482;
            margin: 0 auto;
            display: block;
            border-radius: 22px;
            height: 44px;
            color: #fffef1;
            font-weight: bolder;
            font-size: 18px;

        }

        .applyBtn {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .applyBtn button {
            width: 45%;
        }

        .clubinfo {
            padding: 12px;
        }
    </style>
</head>
<body>
<div class="clubCnt">
    <div class="clubWrap">
        <div class="clubBox1 clubBox">
            <img src="./images/clubimg1.png" th:src="${qrUrl} + 'images/clubimg1.png'"/>
        </div>
        <div class="clubBox3">
            <div class="clubDeptCnt">
                <div class="dptWrap" style="background:#fff">
                    <div class="deptName" th:text="${dept.departName}">{{ deptName }}</div>
                    <div class="clubinfo">
                        <div style="word-wrap:break-word" th:text="${dept.memo}">{{ clubJs }}</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="clubBox2 clubBox">
            <img src="./assets/clubimg2.png" th:src="${qrUrl} + 'images/clubimg2.png'"/>
        </div>
        <div class="clubBox4">
            <button class="joinBtn">我要加入</button>
        </div>
    </div>
</div>
<script type="text/javascript">
    
</script>
</body>
</html>
