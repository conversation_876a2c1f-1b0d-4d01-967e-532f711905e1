package com.zs.create.modules.backbone.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zs.create.config.SerializerBigDecimal;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 学生评议申请
 * 
 * <AUTHOR>
 * @email null
 * @date 2021-05-22 12:47:22
 */
@Data
@TableName("backbone_launch_apply_hours")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="backbone_launch_apply_hours对象", description="学生评议申请")
public class BackboneLaunchApplyHoursEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId
	private String id;
	/**
	 * 申请主表id
	 */
	@ApiModelProperty(value = "申请主表id")
	private String applyId;
	/**
	 * 项目时间
	 */
	@ApiModelProperty(value = "项目时间")
	@JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date programmTime;
	/**
	 * 项目名称
	 */
	@ApiModelProperty(value = "项目名称")
	private String prigrammName;
	/**
	 * 履职时长
	 */
	@JsonFormat(shape = JsonFormat.Shape.NUMBER_FLOAT)
	@JsonSerialize(using = SerializerBigDecimal.class)
	@ApiModelProperty(value = "履职时长")
	private BigDecimal hours;
	/**
	 * 任务角色
	 */
	@ApiModelProperty(value = "任务角色")
	private String role;

	@ApiModelProperty(value = "删除标识")
	@TableLogic
	private Integer delFlag;

}
