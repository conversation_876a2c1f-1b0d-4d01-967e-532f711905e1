package com.zs.create.modules.oa.teachers.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.StringUtils;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 校级指导老师库
 * 
 * <AUTHOR>
 * @email null
 * @date 2021-04-14 17:06:56
 */
@Data
@TableName("sys_school_teachers")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sys_school_teachers对象", description="校级指导老师库")
public class SysSchoolTeachersEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	public static final String KEY = "school_teacher_audit";//流程key

	public static final String APPLY = "1";//申请中
	public static final String AUDIT_REJECT = "3";//驳回
	public static final String AUDIT_PASS = "2";//通过

	public static final Integer PROCESS_REJECT = 0;
	public static final Integer PROCESS_PASS = 1;

	public static final String QUERY_TODO = "todo";
	public static final String QUERY_HISTORY = "history";

	@TableField(exist = false)
	@ApiModelProperty(value = "查询类型 ： todo ： 代办   history ：  已办")
	private String queryType;

	@TableField(exist = false)
	private String assignee;

	@TableField(exist = false)
	private String taskId;

	@TableField(exist = false)
	private String taskName;


	/**
	 * 
	 */
	@TableId
	private String id;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 工号
	 */
	private String userCode;
	/**
	 * 姓名
	 */
	private String userName;
	/**
	 * 头像
	 */
	private String avatar;
	/**
	 * 性别（1：男 2：女）
	 */
	private Integer sex;
	/**
	 * 出生年月日
	 */
	@JsonFormat(pattern = "yyyy-MM-dd", locale = "zh", timezone = "Asia/Shanghai")
	@DateTimeFormat(pattern = "yyyy-MM-dd",iso =DateTimeFormat.ISO.DATE )
	private Date birthday;
	/**
	 * 政治面貌
	 */
	private String nation;
	/**
	 * 政治面貌
	 */
	private String politicalOutlook;
	/**
	 * 工作单位
	 */
	private String post;
	/**
	 * 职称/职务
	 */
	private String workUnit;
	/**
	 * 岗位类别
	 */
	private String postType;
	/**
	 * 岗位类别名称
	 */
	@TableField(exist = false)
	private String postTypeName;
	/**
	 * 电话
	 */
	private String phone;
	/**
	 * 邮箱
	 */
	private String email;
	/**
	 * 部门id
	 */
	private String depId;
	/**
	 * 指导单位部门名称
	 */
	@TableField(exist = false)
	private String depName;
	/**
	 * 工作简历
	 */
	private String resume;
	/**
	 * 主要研究方向
	 */
	private String researchDirection;
	/**
	 * 主要学术兼职
	 */
	private String learningJob;
	/**
	 * 0 未申请 1 申请中 2 通过 3 驳回
	 */
	private String status;

	public String getStatus() {
		if (StringUtils.isNotBlank(status)) {
			return status;
		}else {
			return "0";
		}

	}
	/**
	 * 流程实例id
	 */
	private String processInstanceId;
	/**
	 * 删除状态（0，正常，1已删除）
	 */
	private String delFlag;
	/**
	 * 创建人
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 
	 */
	private String updateBy;
	/**
	 * 
	 */
	private Date updateTime;

	//@TableField(exist = false)
	private String departId;

	@TableField(exist = false)
	private String departName;

	@TableField(exist = false)
	private List<String[]> originalList;

	private String originalListToJson;
}
