package com.zs.create.modules.quartz.job;

import com.zs.create.modules.oa.goodsmanage.entity.OaGoodsBorrowEntity;
import com.zs.create.modules.oa.goodsmanage.service.OaGoodsBorrowService;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @className: GoodsManageServiceJob
 * @description: 物资借用失效定时器
 * @author: nihao
 * @date: 2020-1-28
 **/
@Component
public class GoodsManageServiceJob implements Job {
    @Autowired
    private OaGoodsBorrowService oaGoodsBorrowService;
    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        //查询是否有失效的物资借用数据
        List<OaGoodsBorrowEntity> list =oaGoodsBorrowService.getGoodsList();
        //将数据存到scHoursDirectory表,相同数据合并,不同数据保存,然后存到scItemHours表中
        if(list.size()> 0 ){
            oaGoodsBorrowService.dealWithData(list);
        }

    }





}
