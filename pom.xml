<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.zsxcframework.boot</groupId>
    <artifactId>zsxc-parent</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.1.3.RELEASE</version>
        <relativePath/>
    </parent>

    <modules>
        <module>zsxc-base-common</module>
        <module>zsxc-base-redis-monogo</module>
        <!--<module>zsxc-module-workflow</module>-->
        <module>zsxc-dynamic-forms</module>
        <module>zsxc-module-system</module>
    </modules>
    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <tomcat.version>9.0.102</tomcat.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.zsxcframework.boot</groupId>
                <artifactId>zsxc-base-common</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsxcframework.boot</groupId>
                <artifactId>zsxc-base-redis-monogo</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsxcframework.boot</groupId>
                <artifactId>zsxc-dynamic-forms</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.zsxcframework.boot</groupId>
                <artifactId>zsxc-module-workflow</artifactId>
                <version>${version}</version>
            </dependency>
            <!-- commons -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>commons-lang</groupId>
                <artifactId>commons-lang</artifactId>
                <version>2.6</version>
            </dependency>
            <dependency>
                <groupId>org.apache.commons</groupId>
                <artifactId>commons-pool2</artifactId>
                <version>2.6.0</version>
            </dependency>
            <!-- Lombok -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>1.18.6</version>
            </dependency>
            <!-- mybatis-plus -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.1.2</version>
            </dependency>
            <!-- druid -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>1.1.17</version>
            </dependency>

            <!-- 动态数据源 -->
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot-starter</artifactId>
                <version>2.5.4</version>
            </dependency>
            <!-- json -->
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>1.2.83</version>
            </dependency>
            <!--mysql-->
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>5.1.47</version>
                <scope>runtime</scope>
            </dependency>
            <!--  sqlserver-->
            <dependency>
                <groupId>com.microsoft.sqlserver</groupId>
                <artifactId>sqljdbc4</artifactId>
                <version>4.0</version>
                <scope>runtime</scope>
            </dependency>
            <!-- oracle驱动 -->
            <dependency>
                <groupId>com.oracle</groupId>
                <artifactId>ojdbc6</artifactId>
                <version>11.2.0.3</version>
                <scope>runtime</scope>
            </dependency>
            <!--JWT-->
            <dependency>
                <groupId>com.auth0</groupId>
                <artifactId>java-jwt</artifactId>
                <version>3.7.0</version>
            </dependency>
            <!--shiro-->
            <dependency>
                <groupId>org.apache.shiro</groupId>
                <artifactId>shiro-spring</artifactId>
                <version>1.9.1</version>
            </dependency>
            <!-- Swagger API文档 -->
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger2</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-swagger-ui</artifactId>
                <version>2.9.2</version>
            </dependency>
            <dependency>
                <groupId>com.github.xiaoymin</groupId>
                <artifactId>swagger-bootstrap-ui</artifactId>
                <version>1.9.3</version>
            </dependency>
            <dependency>
                <groupId>io.springfox</groupId>
                <artifactId>springfox-bean-validators</artifactId>
                <version>2.9.2</version>
            </dependency>
            <!--1.5.22覆盖1.5.20默认版本处理bug-->
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-annotations</artifactId>
                <version>1.5.22</version>
            </dependency>
            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-models</artifactId>
                <version>1.5.22</version>
            </dependency>
            <!-- AutoPoi Excel工具类-->
            <dependency>
                <groupId>org.jeecgframework</groupId>
                <artifactId>autopoi-web</artifactId>
                <version>1.4.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>commons-codec</groupId>
                        <artifactId>commons-codec</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <!-- easypoi-->
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-base</artifactId>
                <version>4.4.0</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-web</artifactId>
                <version>4.4.0</version>
            </dependency>
            <dependency>
                <groupId>cn.afterturn</groupId>
                <artifactId>easypoi-annotation</artifactId>
                <version>4.4.0</version>
            </dependency>

            <dependency>
                <groupId>com.deepoove</groupId>
                <artifactId>poi-tl</artifactId>
                <version>1.10.3</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml-schemas -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi</artifactId>
                <version>4.1.2</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml-schemas -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>4.1.2</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml-schemas -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml-schemas</artifactId>
                <version>4.1.2</version>
            </dependency>
            <!-- https://mvnrepository.com/artifact/org.apache.poi/poi-scratchpad -->
            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-scratchpad</artifactId>
                <version>4.1.2</version>
            </dependency>

            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>4.1.14</version>
            </dependency>

            <!--加入工作流依赖-->
            <!--activiti 相关依赖 -->
            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-spring-boot-starter-basic</artifactId>
                <version>5.22.0</version>
                <exclusions>
                    <exclusion>
                        <groupId>org.mybatis</groupId>
                        <artifactId>mybatis</artifactId>
                    </exclusion>
                </exclusions>
            </dependency>

            <dependency>
                <groupId>org.activiti</groupId>
                <artifactId>activiti-json-converter</artifactId>
                <version>5.22.0</version>
            </dependency>

            <dependency>
                <groupId>net.oschina.zcx7878</groupId>
                <artifactId>fastdfs-client-java</artifactId>
                <version>1.27.0.0</version>
            </dependency>

            <!-- https://mvnrepository.com/artifact/org.csource/fastdfs-client-java -->
            <!--<dependency>
                <groupId>org.csource</groupId>
                <artifactId>fastdfs-client-java</artifactId>
                <version>1.27-RELEASE</version>
            </dependency>-->

            <!-- xml解析依赖-->
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-codec</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-css</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-svg-dom</artifactId>
                <version>1.7</version>
            </dependency>
            <dependency>
                <groupId>org.apache.xmlgraphics</groupId>
                <artifactId>batik-svggen</artifactId>
                <version>1.7</version>
            </dependency>
            <!--        xml解析依赖-->
            <!-- 阿里云短信 -->
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-dysmsapi</artifactId>
                <version>1.0.0</version>
            </dependency>
            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>3.2.3</version>
            </dependency>
            <dependency>
                <groupId>com.fasterxml.jackson.core</groupId>
                <artifactId>jackson-databind</artifactId>
                <version>2.9.8</version>
            </dependency>
            <dependency>
                <groupId>junit</groupId>
                <artifactId>junit</artifactId>
                <version>4.12</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-freemarker</artifactId>
            </dependency>
            <!-- 整合rabbitmq-->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-amqp</artifactId>
                <version>2.2.3.RELEASE</version>
            </dependency>

            <!-- drools规则引擎 -->
            <!-- drools规则引擎相关jar包-->
            <dependency>
                <groupId>org.kie</groupId>
                <artifactId>kie-api</artifactId>
                <version>7.28.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-core</artifactId>
                <version>7.28.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-verifier-api</artifactId>
                <version>7.28.0.Final</version>
            </dependency>

            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-verifier-core</artifactId>
                <version>7.28.0.Final</version>
            </dependency>

            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-verifier-drl</artifactId>
                <version>7.28.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-compiler</artifactId>
                <version>7.28.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-decisiontables</artifactId>
                <version>7.28.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.drools</groupId>
                <artifactId>drools-templates</artifactId>
                <version>7.28.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.kie</groupId>
                <artifactId>kie-internal</artifactId>
                <version>7.28.0.Final</version>
            </dependency>
            <dependency>
                <groupId>org.kie</groupId>
                <artifactId>kie-ci</artifactId>
                <version>7.28.0.Final</version>
            </dependency>

            <dependency>
                <groupId>org.jsonschema2pojo</groupId>
                <artifactId>jsonschema2pojo-core</artifactId>
                <version>1.0.1</version>
            </dependency>

            <dependency>
                <groupId>com.sun.codemodel</groupId>
                <artifactId>codemodel</artifactId>
                <version>2.6</version>
            </dependency>

            <!--drools 结束 -->

            <!--增强MongoDb设置 -->
            <dependency>
                <groupId>com.spring4all</groupId>
                <artifactId>mongodb-plus-spring-boot-starter</artifactId>
                <version>1.0.0.RELEASE</version>
            </dependency>


            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-websocket</artifactId>
                <version>2.1.3.RELEASE</version>
            </dependency>

            <!-- log4j漏洞修复开始-->
            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-to-slf4j</artifactId>
                <version>2.17.0</version>
            </dependency>

            <dependency>
                <groupId>org.apache.logging.log4j</groupId>
                <artifactId>log4j-api</artifactId>
                <version>2.17.0</version>
            </dependency>
            <!-- log4j漏洞修复结束-->

        </dependencies>
    </dependencyManagement>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <!-- 打包跳过测试 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <skipTests>true</skipTests>
                </configuration>
            </plugin>
            <!-- 避免font文件的二进制文件格式压缩破坏 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>woff</nonFilteredFileExtension>
                        <nonFilteredFileExtension>woff2</nonFilteredFileExtension>
                        <nonFilteredFileExtension>eot</nonFilteredFileExtension>
                        <nonFilteredFileExtension>ttf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>svg</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>
        </plugins>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
                <excludes>
                    <exclude>**/*.java</exclude>
                </excludes>
            </resource>
        </resources>
    </build>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <url>http://192.168.10.151:82/repository/zsxc-release/</url>
        </repository>

        <snapshotRepository>
            <id>snapshots</id>
            <url>http://192.168.10.151:82/repository/zsxc-snapshot/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
