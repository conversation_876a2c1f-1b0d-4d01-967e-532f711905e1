package com.zs.create.modules.oa.ticketManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.DictModel;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.oa.ticketManagement.entity.OaVenueActivityEntity;
import com.zs.create.modules.oa.ticketManagement.entity.OaVenueActivitySeatEntity;
import com.zs.create.modules.oa.ticketManagement.entity.OaVenueEntity;
import com.zs.create.modules.oa.ticketManagement.entity.OaVenueSeatEntity;
import com.zs.create.modules.oa.ticketManagement.mapper.OaVenueMapper;
import com.zs.create.modules.oa.ticketManagement.service.OaVenueActivitySeatService;
import com.zs.create.modules.oa.ticketManagement.service.OaVenueActivityService;
import com.zs.create.modules.oa.ticketManagement.service.OaVenueSeatService;
import com.zs.create.modules.oa.ticketManagement.service.OaVenueService;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import com.zs.create.modules.system.util.DictUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description 票券管理—场馆Service实现层
 *
 * <AUTHOR> @email 
 * @date 2023-03-31 13:05:35
 * @Version: V1.0
 */
@Service
public class OaVenueServiceImpl extends ServiceImpl<OaVenueMapper, OaVenueEntity> implements OaVenueService {
    @Autowired
    private OaVenueSeatService oaVenueSeatService;
    @Resource
    private SysDepartMapper sysDepartMapper;
    @Autowired
    private OaVenueActivityService oaVenueActivityService;
    @Autowired
    @Lazy
    private OaVenueActivitySeatService oaVenueActivitySeatService;

    @Override
    public Result<OaVenueEntity> addVenue(OaVenueEntity oaVenue) {
        List<DictModel> ticket_col_row_max = DictUtils.getKeyAndValueByCode("ticket_col_row_max", "50");
        String value="0";
        for (DictModel ticketColRowMax : ticket_col_row_max) {
            value = ticketColRowMax.getValue();
        }
        if (Integer.parseInt(value) < oaVenue.getColumnNum() || Integer.parseInt(value) < oaVenue.getLineNum()){
            throw new ZsxcBootException("行数，列数不能超过最大值");
        }
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getUsername());
        if (!twPersonInCharge) throw new ZsxcBootException("非团委负责人不可新增");
        Result<OaVenueEntity> result = new Result<OaVenueEntity>();
        if (CollectionUtils.isEmpty(oaVenue.getSeatInfoList())) throw new ZsxcBootException("参数有误，未找到座位信息");

        List<List<OaVenueSeatEntity>> seatInfoList = oaVenueSeatService.setSeatNumber(oaVenue.getSeatInfoList());

        List<OaVenueSeatEntity> seatList = new ArrayList<>();
        seatInfoList.forEach(seatList::addAll);
        //统计可用座位数
        long count = seatList.stream().filter(t -> t.getIsSeat().equals(0)).count();
        oaVenue.setCanUseSeatNum((int)count);
        this.save(oaVenue);
//        保存座位信息,设置id
        seatList.forEach(t -> {
            t.setVenueId(oaVenue.getId());
//            if (oConvertUtils.isNotEmpty(t.getSeatNumber())){
//                t.setXNumbering(t.getCodeX());
//            }
        });
        oaVenueSeatService.saveBatch(seatList);
        result.success("添加成功！");
        return result;
    }

    @Override
    public Result<OaVenueEntity> queryById(String id) {
        Result<OaVenueEntity> result = new Result<OaVenueEntity>();

        OaVenueEntity oaVenue = this.getById(id);
        if (oConvertUtils.isEmpty(oaVenue)) throw new ZsxcBootException("未找到对应场馆信息");
        //封装座位信息
        LambdaQueryWrapper<OaVenueSeatEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaVenueSeatEntity::getVenueId,id);
        List<OaVenueSeatEntity> seatEntityList = oaVenueSeatService.list(wrapper);

        List<List<OaVenueSeatEntity>> seatInfoList = oaVenueSeatService.packSeat(seatEntityList);
        oaVenue.setSeatInfoList(seatInfoList);

        List<OaVenueActivityEntity> futureActList = oaVenueActivityService.getFutureActByVenueId(id);
        if (futureActList.size()>0){
            oaVenue.setEditSeatFlag(false);
        }else {
            oaVenue.setEditSeatFlag(true);
        }

        result.setResult(oaVenue);
        result.setSuccess(true);
        return result;
    }

    @Override
    @Transactional
    public boolean updateVenue(OaVenueEntity oaVenue) {
        //判断权限
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getUsername());
        if (!twPersonInCharge) throw new ZsxcBootException("非团委负责人不可编辑");

        //当此场馆有活动进行中/未开始，则禁止修改  ps.或者不允许修改座位信息
//        List<OaVenueActivityEntity> futureActList = oaVenueActivityService.getFutureActByVenueId(oaVenue.getId());
        //前端做禁用，后端不好校验
//        if (!CollectionUtils.isEmpty(futureActList)) throw new ZsxcBootException("此场馆有待举办活动，暂不可修改");

        LambdaQueryWrapper<OaVenueSeatEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaVenueSeatEntity::getVenueId,oaVenue.getId());
        List<OaVenueSeatEntity> seatEntityList = oaVenueSeatService.list(wrapper);
        //删除之前座位，再新建
        List<String> deleteIds = seatEntityList.stream().map(OaVenueSeatEntity::getId).collect(Collectors.toList());
        Boolean remove = oaVenueSeatService.removeByIds(deleteIds);
        //设置座位编号
        List<List<OaVenueSeatEntity>> seatInfoList = oaVenueSeatService.setSeatNumber(oaVenue.getSeatInfoList());

        List<OaVenueSeatEntity> seatList = new ArrayList<>();
        seatInfoList.forEach(seatList::addAll);
        //统计可用座位数
        long count = seatList.stream().filter(t -> t.getIsSeat().equals(0)).count();
        oaVenue.setCanUseSeatNum((int)count);
        Boolean update = this.updateById(oaVenue);

        seatList.forEach(t -> {
            t.setVenueId(oaVenue.getId());
//            if (oConvertUtils.isNotEmpty(t.getSeatNumber())){
//                t.setXNumbering(t.getCodeX());
//            }
        });
        Boolean save = oaVenueSeatService.saveBatch(seatList);
        if (remove && update && save){
            return Boolean.TRUE;
        }else {
            return Boolean.FALSE;
        }

    }

    @Override
    public Result<List<Map<String, Object>>> getAllVenue() {
        Result<List<Map<String, Object>>> result = new Result<>();
        LambdaQueryWrapper<OaVenueEntity> wrappers = new LambdaQueryWrapper<>();
        wrappers.orderByDesc(OaVenueEntity::getCreateTime);
        List<OaVenueEntity> oaVenueEntities = this.list(wrappers);
        List<Map<String, Object>> resultList = new ArrayList<>();
        if (oaVenueEntities.size()>0) {
            oaVenueEntities.forEach( t ->{
                //封装座位信息
                LambdaQueryWrapper<OaVenueSeatEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(OaVenueSeatEntity::getVenueId,t.getId());
                List<OaVenueSeatEntity> seatEntityList = oaVenueSeatService.list(wrapper);
                List<OaVenueActivitySeatEntity> actSeatList = new ArrayList<>();

                for (OaVenueSeatEntity param : seatEntityList) {
                    OaVenueActivitySeatEntity actSeat = new OaVenueActivitySeatEntity();
                    BeanUtils.copyProperties(param, actSeat);
                    actSeatList.add(actSeat);
                }

                List<List<OaVenueActivitySeatEntity>> seatInfoList = oaVenueActivitySeatService.packSeat(actSeatList);
                Map<String , Object> map = new HashMap<>();
                map.put("venueId", t.getId());
                map.put("venueName", t.getVenueName());
                map.put("seatInfo", seatInfoList);
                resultList.add(map);
            });
        }
        result.setResult(resultList);
        result.setSuccess(Boolean.TRUE);
        return result;
    }

    @Override
    public void delete(String id) {
        //查询当前场馆是否有历史活动
        List<OaVenueActivityEntity> activityEntities = oaVenueActivityService.list(Wrappers.<OaVenueActivityEntity>lambdaQuery()
                .eq(OaVenueActivityEntity::getVenueId, id));
        if (activityEntities.size() > 0){
            //有数据，不给删除，给出提示
            throw new ZsxcBootException("本场馆存在历史数据，不可删除");
        }else {
            //没有，删除场馆信息，删除座位信息
            oaVenueSeatService.remove(Wrappers.<OaVenueSeatEntity>lambdaQuery()
                    .eq(OaVenueSeatEntity::getVenueId,id));
            this.removeById(id);
        }
    }
}
