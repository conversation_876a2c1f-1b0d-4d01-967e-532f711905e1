<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.weixin.mapper.SysWeixinMessageMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.weixin.entity.SysWeixinMessageEntity" id="sysWeixinMessageMap">
        <result property="id" column="id"/>
        <result property="focusReply" column="focus_reply"/>
        <result property="autoReply" column="auto_reply"/>
        <result property="enableStatus" column="enable_status"/>
        <result property="updateBy" column="update_by"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <select id="getNewByCt" resultType="com.zs.create.modules.weixin.entity.SysWeixinMessageEntity">
        select focus_reply,auto_reply from sys_weixin_message
         where del_flag = '0' and enable_status = '0'
         order by create_time desc limit 1
    </select>

</mapper>