<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.society.mapper.SysSchoolTeachersSocietyItemMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.society.entity.SysSchoolTeachersSocietyItemEntity" id="sysSchoolTeachersSocietyItemMap">
        <result property="id" column="id"/>
        <result property="socirtyId" column="socirty_id"/>
        <result property="name" column="name"/>
        <result property="departId" column="depart_id"/>
        <result property="createBy" column="create_by"/>
        <result property="createByName" column="create_by_name"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateDate" column="update_date"/>
        <result property="delFlag" column="del_flag"/>
    </resultMap>


</mapper>