package com.zs.create.modules.mobile.sc;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.base.enums.ExperienceEnum;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.personal.entity.ScExperienceEntity;
import com.zs.create.modules.personal.service.ScExperienceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * @createUser hy
 * @createTime 2020-7-27
 * @description 经历
 */
@Slf4j
@Api(tags="手机端-经历api")
@RestController
@RequestMapping("/mobile/experience")
public class ExperienceApiController {
    @Autowired
    private ScExperienceService scExperienceService;
    /**
     * 手机端-个人中心-任职经历
     */
    @ApiOperation(value="任职经历", notes="任职经历")
    @GetMapping(value = "/involvedPage")
    public Result<IPage<ScExperienceEntity>> involvedPage(
            ScExperienceEntity scExperienceEntity,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        Result<IPage<ScExperienceEntity>> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        QueryWrapper<ScExperienceEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", DelFlagEnum.NO_DEL.getCode());
        queryWrapper.eq("type", ExperienceEnum.RZ.getCode());
        queryWrapper.eq("user_id",sysUser.getId());
        if(StringUtils.isNotEmpty(scExperienceEntity.getJob())){
            queryWrapper.like("job", scExperienceEntity.getJob());
        }
        Page<ScExperienceEntity> page = new Page<>(pageNo, pageSize);
        IPage<ScExperienceEntity> pageList = scExperienceService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 手机端-个人中心-奖惩经历
     */
    @ApiOperation(value="奖惩经历", notes="奖惩经历")
    @GetMapping(value = "/rewardPage")
    public Result<IPage<ScExperienceEntity>> rewardPage(
            ScExperienceEntity scExperienceEntity,
            @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
            @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        Result<IPage<ScExperienceEntity>> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        String userId=sysUser.getId();
        QueryWrapper<ScExperienceEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", DelFlagEnum.NO_DEL.getCode());
        queryWrapper.eq("user_id",userId);
        queryWrapper.eq("type", ExperienceEnum.JC.getCode());
        if(StringUtils.isNotEmpty(scExperienceEntity.getBonusPenaltyName())){
            queryWrapper.like("bonus_penalty_name", scExperienceEntity.getBonusPenaltyName());
        }
        Page<ScExperienceEntity> page = new Page<>(pageNo, pageSize);
        IPage<ScExperienceEntity> pageList = scExperienceService.page(page,queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 添加
     */
    @NoRepeatSubmit(expireSeconds = 3)
    @ApiOperation(value="个人经历表-添加", notes="个人经历表-添加")
    @PostMapping(value = "/add")
    public Result<ScExperienceEntity> add(@RequestBody ScExperienceEntity scExperience) {
        Result<ScExperienceEntity> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        try {
            scExperience.setUserId(sysUser.getId());
            scExperienceService.save(scExperience);
            result.success("添加成功！");
        } catch (Exception e) {
            log.error(e.getMessage(),e);
            result.error500("操作失败");
        }
        return result;
    }

    /**
     * 通过id查询
     */
    @ApiOperation(value="个人经历表-通过id查询", notes="个人经历表-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<ScExperienceEntity> queryById(@RequestParam(name="id") String id) {
        Result<ScExperienceEntity> result = new Result<>();
        ScExperienceEntity scExperience = scExperienceService.getById(id);
        if(scExperience==null) {
            result.error500("未找到对应实体");
        }else {
            result.setResult(scExperience);
            result.setSuccess(true);
        }
        return result;
    }

    /**
     * 编辑
     */
    @ApiOperation(value="个人经历表-编辑", notes="个人经历表-编辑")
    @PutMapping(value = "/edit")
    public Result<ScExperienceEntity> edit(@RequestBody ScExperienceEntity scExperience) {
        Result<ScExperienceEntity> result = new Result<>();
        ScExperienceEntity scExperienceEntity = scExperienceService.getById(scExperience.getId());
        if(scExperienceEntity==null) {
            result.error500("未找到对应实体");
        }else {
            boolean ok = scExperienceService.updateById(scExperience);
            if(ok) {
                result.success("修改成功!");
            }
        }
        return result;
    }

    /**
     * 通过id删除
     */
    @ApiOperation(value="个人经历表-通过id删除", notes="个人经历表-通过id删除")
    @DeleteMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id") String id) {
        try {
            scExperienceService.removeById(id);
        } catch (Exception e) {
            log.error("删除失败",e.getMessage());
            return Result.error("删除失败!");
        }
        return Result.ok("删除成功!");
    }
}
