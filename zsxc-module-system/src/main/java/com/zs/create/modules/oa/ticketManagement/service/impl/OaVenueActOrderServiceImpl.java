package com.zs.create.modules.oa.ticketManagement.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.base.util.RedissonLock;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.common.util.StringKit;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.config.RabbitmqConfig;
import com.zs.create.modules.oa.ticketManagement.entity.OaVenueActOrderEntity;
import com.zs.create.modules.oa.ticketManagement.entity.OaVenueActivityEntity;
import com.zs.create.modules.oa.ticketManagement.entity.OaVenueActivitySeatEntity;
import com.zs.create.modules.oa.ticketManagement.entity.actOrderVo;
import com.zs.create.modules.oa.ticketManagement.mapper.OaVenueActOrderMapper;
import com.zs.create.modules.oa.ticketManagement.mapper.OaVenueActivityMapper;
import com.zs.create.modules.oa.ticketManagement.service.OaVenueActOrderService;
import com.zs.create.modules.oa.ticketManagement.service.OaVenueActivitySeatService;
import com.zs.create.modules.oa.ticketManagement.utils.barCodeUtil;
import com.zs.create.modules.system.service.ISysDepartService;
import org.apache.shiro.SecurityUtils;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR> @Description 票券管理—订单记录Service实现层
 * @email
 * @date 2023-04-10 13:45:16
 * @Version: V1.0
 */
@Service
public class OaVenueActOrderServiceImpl extends ServiceImpl<OaVenueActOrderMapper, OaVenueActOrderEntity> implements OaVenueActOrderService {

    @Resource
    private OaVenueActivityMapper oaVenueActivityMapper;
    @Autowired
    private OaVenueActivitySeatService oaVenueActivitySeatService;
    @Autowired
    private RedissonLock redissonLock;
    @Autowired
    private RabbitTemplate rabbitTemplate;
    @Autowired
    private ISysDepartService sysDepartService;

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public Result<?> purchaseTicket(OaVenueActOrderEntity oaVenueActOrder) {

        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<String> seatIdList = oaVenueActOrder.getSeatIdList();
        if (oConvertUtils.isEmpty(seatIdList) || seatIdList.size() == 0) {
            throw new ZsxcBootException("请先选择座位");
        }
        seatIdList.forEach(t -> {
            // 当前座位是否有订单，订单是否为登陆人创建
            LambdaQueryWrapper<OaVenueActOrderEntity> orderWrapper = new LambdaQueryWrapper<>();
            orderWrapper.eq(OaVenueActOrderEntity::getActivityId, oaVenueActOrder.getActivityId());
            orderWrapper.ne(OaVenueActOrderEntity::getUserId, sysUser.getUsername());
            orderWrapper.eq(OaVenueActOrderEntity::getActSeatId, t);
            OaVenueActOrderEntity one = this.getOne(orderWrapper);
            if (oConvertUtils.isNotEmpty(one)) throw new ZsxcBootException("当前座位已被选择，请刷新活动列表重试");
        });

        Result<List<String>> result = new Result<>();
        OaVenueActivityEntity activityEntity = oaVenueActivityMapper.selectById(oaVenueActOrder.getActivityId());
        if (oConvertUtils.isEmpty(activityEntity)) throw new ZsxcBootException("未找到对应活动");
        Date now = new Date();
        if (now.compareTo(activityEntity.getGetTicketEt()) >= 0) throw new ZsxcBootException("购票时间已过");

        // 校验购票是否超过最大限制

        // 统计已确认+本次是否超过上限，因之前预订会被清除，所以用本次的统计
        LambdaQueryWrapper<OaVenueActOrderEntity> commitOrderWrapper = new LambdaQueryWrapper<>();
        commitOrderWrapper.eq(OaVenueActOrderEntity::getActivityId, activityEntity.getId());
        commitOrderWrapper.eq(OaVenueActOrderEntity::getUserId, sysUser.getUsername());
        commitOrderWrapper.eq(OaVenueActOrderEntity::getStatus, 1);

        List<OaVenueActOrderEntity> commitOrderList = this.list(commitOrderWrapper);
        if ((seatIdList.size() + commitOrderList.size()) > Integer.parseInt(activityEntity.getTicketLimit()))
            throw new ZsxcBootException("不可超过最大限制票数，最大票数限制为：" + activityEntity.getTicketLimit());

        // 查询是否有以往预订订单，有就删除老订单，创建新订单
        LambdaQueryWrapper<OaVenueActOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaVenueActOrderEntity::getActivityId, activityEntity.getId());
        wrapper.eq(OaVenueActOrderEntity::getUserId, sysUser.getUsername());
        wrapper.eq(OaVenueActOrderEntity::getStatus, 0);
        List<OaVenueActOrderEntity> list = this.list(wrapper);
        if (list.size() > 0) {
            this.remove(wrapper);
            List<String> seatId = list.stream().map(OaVenueActOrderEntity::getActSeatId).collect(Collectors.toList());
            LambdaUpdateWrapper<OaVenueActivitySeatEntity> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.in(OaVenueActivitySeatEntity::getId, seatId);
            updateWrapper.set(OaVenueActivitySeatEntity::getIsUse, "0");
            oaVenueActivitySeatService.update(updateWrapper);
        }

        List<String> orderIdList = new ArrayList<>();
        List<OaVenueActOrderEntity> orderSaveList = new ArrayList<>();
        List<OaVenueActivitySeatEntity> seatUpList = new ArrayList<>();
        // 加redis 分布式锁
        for (String seatId : seatIdList) {
            String lockName = oaVenueActOrder.getActivityId() + seatId;
            boolean lock = redissonLock.lock(lockName, 5);
            if (!lock) {
                return result.error500("当前座位已被选择，请刷新活动列表重试");
            } else {
                OaVenueActOrderEntity orderEntity = new OaVenueActOrderEntity();
                BeanUtils.copyProperties(oaVenueActOrder, orderEntity);
                String id = Long.toString(SnowIdUtils.uniqueLong());
                orderEntity.setId(id);
                orderEntity.setActSeatId(seatId);
                OaVenueActivitySeatEntity actSeat = oaVenueActivitySeatService.getById(seatId);
                orderEntity.setSeatNumber(actSeat.getSeatNumber());
                orderEntity.setStatus(0);// 设置订单状态为预订
                actSeat.setIsUse(2);// 设置座位状态为预订
                orderSaveList.add(orderEntity);
                orderIdList.add(orderEntity.getId());
                seatUpList.add(actSeat);
            }
        }
        boolean save = this.saveBatch(orderSaveList);
        boolean up = oaVenueActivitySeatService.updateBatchById(seatUpList);
        if (save && up) {
            result.setResult(orderIdList);
            // 存mq
            for (OaVenueActOrderEntity order : orderSaveList) {
                // 发送消息到订单队列
                rabbitTemplate.convertAndSend(RabbitmqConfig.ACTIVITY_ORDER_EXCHANGE,
                        RabbitmqConfig.ACTIVITY_ORDER_KEY, order.getId());
            }
            return result.success("购票成功");
        } else {
            return result.error500("购票失败");
        }
    }

    @Override
    public IPage<actOrderVo> queryPage(Page<actOrderVo> page, actOrderVo oaVenueActOrder) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Boolean twPersonInCharge = sysDepartService.isTwPersonInCharge(sysUser.getUsername());
        if (twPersonInCharge) {
            IPage<actOrderVo> voIPage = this.baseMapper.queryPage(page, oaVenueActOrder);
            List<actOrderVo> records = voIPage.getRecords();
            if (records.size() > 0) {
                for (int i = 0; i < records.size(); i++) {
                    records.get(i).setSort(String.valueOf(i + 1));
                }
                voIPage.setRecords(records);
            }
            return voIPage;
        } else {
            return new Page<>();
        }
    }

    @Override
    public List<actOrderVo> queryPage(actOrderVo oaVenueActOrder) {
        return this.baseMapper.queryPage(oaVenueActOrder);
    }

    @Override
    public IPage<actOrderVo> myOrder(Page<actOrderVo> page, String activityName, Integer type) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Date now = new Date();
        IPage<actOrderVo> voIPage = this.baseMapper.getMyOrder(page, activityName, type, sysUser.getUsername());
        List<actOrderVo> records = voIPage.getRecords();
        if (records.size() > 0) {
            records.forEach(t -> {
                // 设置状态字段
                if (t.getStatus() == 0) {
                    t.setStatusName("待确认");
                }
                if (t.getStatus() == 1 && t.getIsJoin() == 1) {
                    t.setStatusName("已完成");
                }
                if (t.getStatus() == 1 && now.compareTo(t.getEt()) <= 0 && t.getIsJoin() == 0) {
                    t.setStatusName("待使用");
                }
                if (now.compareTo(t.getEt()) > 0 && t.getIsJoin() == 0) {
                    t.setStatusName("已过期");
                }

//                List<String> idList = Arrays.stream(t.getId().split(",")).collect(Collectors.toList());
//                LambdaQueryWrapper<OaVenueActOrderEntity> wrapper = new LambdaQueryWrapper<>();
//                wrapper.in(OaVenueActOrderEntity::getId, idList);
//                wrapper.select(OaVenueActOrderEntity::getBarCode);
//                List<OaVenueActOrderEntity> barCodeList = this.list(wrapper);
//                if (!CollectionUtils.isEmpty(barCodeList)) {
//                    String barCode = barCodeList.stream().map(OaVenueActOrderEntity::getBarCode).collect(Collectors.joining(","));
//                    t.setBarCode(barCode);
//                }
            });
            voIPage.setRecords(records);
        }
        return voIPage;
    }

    @Override
    public Result<?> unCommitOrder(String actId) {
        Result<actOrderVo> result = new Result<>();
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        LambdaQueryWrapper<OaVenueActOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(OaVenueActOrderEntity::getActivityId, actId);
        wrapper.eq(OaVenueActOrderEntity::getUserId, sysUser.getUsername());
        wrapper.eq(OaVenueActOrderEntity::getStatus, 0);
        List<OaVenueActOrderEntity> unCommitOrderList = this.list(wrapper);

        if (unCommitOrderList.size() == 0) throw new ZsxcBootException("当前未确认订单已被确认或删除，请返回列表页刷新");

        // orderId为所有订单id拼接
//        List<String> idList = Arrays.stream(orderId.split(",")).collect(Collectors.toList());
        List<String> idList = unCommitOrderList.stream().map(OaVenueActOrderEntity::getId).collect(Collectors.toList());
        List<actOrderVo> orderList = this.baseMapper.unCommitOrderDetail(idList);
        if (orderList.size() == 0) throw new ZsxcBootException("未找到对应订单");
        String idStr = orderList.stream().map(actOrderVo::getId).collect(Collectors.joining(","));
        String seatNumberStr = orderList.stream().map(actOrderVo::getSeatNumber).collect(Collectors.joining(","));

        actOrderVo reOrder = new actOrderVo();
        reOrder = orderList.get(0);
        reOrder.setId(idStr).setSeatNumber(seatNumberStr);
        result.setResult(reOrder);
        return result;
    }

    @Override
    public Result<actOrderVo> getDetailOrder(String ids) {
        Result<actOrderVo> result = new Result<>();
        List<String> orderIds = new ArrayList<>(Arrays.asList(ids.split(",")));
        actOrderVo actOrderVo = this.baseMapper.getDetailOrder(orderIds);
        if (StringKit.isEmpty(actOrderVo)) {
            return result.error500("暂无数据");
        }
        List<String> idList = Arrays.stream(actOrderVo.getId().split(",")).collect(Collectors.toList());
        LambdaQueryWrapper<OaVenueActOrderEntity> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(OaVenueActOrderEntity::getId, idList);
        wrapper.select(OaVenueActOrderEntity::getBarCode);
        List<OaVenueActOrderEntity> barCodeList = this.list(wrapper);
        List<String> seatNumberList = Arrays.stream(actOrderVo.getSeatNumber().split(",")).collect(Collectors.toList());
        String bar = barCodeList.stream().map(OaVenueActOrderEntity::getBarCode).collect(Collectors.joining(","));
        actOrderVo.setBarCode(bar);
//        List<String> barCodeList = Arrays.stream(actOrderVo.getBarCode().split(",")).collect(Collectors.toList());
        List<Map<String, Object>> dataMapList = new ArrayList<>();
        for (int i = 0; i < idList.size(); i++) {
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("id", idList.get(i));
            dataMap.put("seatNumber", seatNumberList.get(i));
            dataMap.put("barCode", barCodeList.get(i).getBarCode());
            dataMapList.add(dataMap);
        }
        actOrderVo.setDataMapList(dataMapList);
        result.setResult(actOrderVo);
        result.setSuccess(true);
        return result;
    }

    @Override
    public Boolean checkTicket(String actId, String idInCode) {
        Date now = new Date();
        OaVenueActOrderEntity order = this.getById(idInCode);
        if (oConvertUtils.isEmpty(order)) throw new ZsxcBootException("未找到对应订单");
        OaVenueActivityEntity activityEntity = oaVenueActivityMapper.selectById(actId);
        if (oConvertUtils.isEmpty(activityEntity)) throw new ZsxcBootException("未找到对应活动");
        if (!order.getActivityId().equals(actId)) throw new ZsxcBootException("非本活动门票信息，无法识别");
        if (order.getIsJoin().equals(1)) throw new ZsxcBootException("此票已核验");
        // 增加过期票不可再验
        if (now.compareTo(activityEntity.getEt()) > 0) throw new ZsxcBootException("门票已过期，无法核验");
        order.setIsJoin(1);
        boolean update = this.updateById(order);

        return update;
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void ticketConfirm(String orderIds) {
        if (oConvertUtils.isEmpty(orderIds)) throw new ZsxcBootException("订单id为空");
        List<String> ids = new ArrayList<>(Arrays.asList(orderIds.split(",")));
        List<OaVenueActOrderEntity> orderList = new ArrayList<>();
        ids.forEach(t -> {
            OaVenueActOrderEntity order = this.getById(t);
            if (oConvertUtils.isEmpty(order)) throw new ZsxcBootException("订单已失效或不存在，请刷新列表");
            if (order.getStatus().equals(1)) throw new ZsxcBootException("订单已被确认，请返回列表页刷新");
            orderList.add(order);
        });
        if (orderList.size() > 0) {
            // 更新订单状态
            orderList.forEach(t -> {
                // 生成条形码并转为Base64位编码保存
                String barCode = barCodeUtil.generateBarCode128(t.getId());
                t.setBarCode(barCode);
                t.setStatus(1);
            });
            this.updateBatchById(orderList);

            // 更新座位数据，置为已选座位
            List<String> seatIdList = orderList.stream().map(OaVenueActOrderEntity::getActSeatId).collect(Collectors.toList());
            LambdaUpdateWrapper<OaVenueActivitySeatEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(OaVenueActivitySeatEntity::getId, seatIdList);
            wrapper.set(OaVenueActivitySeatEntity::getIsUse, 3);
            oaVenueActivitySeatService.update(wrapper);
        }
    }

    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public void cancelTicket(String orderIds) {
        if (oConvertUtils.isEmpty(orderIds)) throw new ZsxcBootException("订单id为空");
        List<String> ids = new ArrayList<>(Arrays.asList(orderIds.split(",")));
        List<OaVenueActOrderEntity> orderList = new ArrayList<>();
        Date now = new Date();
        ids.forEach(t -> {
            OaVenueActOrderEntity order = this.getById(t);
            if (oConvertUtils.isEmpty(order)) throw new ZsxcBootException("订单已失效或不存在，请刷新列表");
            Date st = oaVenueActivityMapper.selectById(order.getActivityId()).getSt();
            if (now.compareTo(st) >= 0) throw new ZsxcBootException("活动已开始，不可退票");
            orderList.add(order);
        });

        if (orderList.size() > 0) {
            orderList.forEach(t -> {
                if (t.getIsJoin().equals(1)) throw new ZsxcBootException("存在已核验票据，无法退票");
            });

            // 删除订单
            this.removeByIds(orderList.stream().map(OaVenueActOrderEntity::getId).collect(Collectors.toList()));
            // 更新座位信息，把座位放出来
            List<String> seatIdList = orderList.stream().map(OaVenueActOrderEntity::getActSeatId).collect(Collectors.toList());
            LambdaUpdateWrapper<OaVenueActivitySeatEntity> wrapper = new LambdaUpdateWrapper<>();
            wrapper.in(OaVenueActivitySeatEntity::getId, seatIdList);
            wrapper.set(OaVenueActivitySeatEntity::getIsUse, 0);
            oaVenueActivitySeatService.update(wrapper);
        }

    }

    @Override
    public void saveRemoveLog(String mess, String type) {
        baseMapper.saveRemoveLog(mess, type);
    }
}
