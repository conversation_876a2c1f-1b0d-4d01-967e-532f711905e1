package com.zs.create.modules.oa.credit.controller;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.modules.oa.credit.entity.OaConfigureCourseEntity;
import com.zs.create.modules.oa.credit.entity.OaCreditApplyEntity;
import com.zs.create.modules.oa.credit.enums.ModuleDeptEnum;
import com.zs.create.modules.oa.credit.service.OaConfigureCourseService;
import com.zs.create.modules.oa.credit.service.OaCreditApplyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;


/**
 * @Description 学分申请Controller层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-18 09:40:16
 * @Version: V1.0
 */
@Slf4j
@Api(tags="课程配置")
@RestController
@RequestMapping("/credit/oaConfigureCourse")
public class OaConfigureCourseController {
    @Autowired
    private OaConfigureCourseService oaConfigureCourseService;

    @Autowired
    private OaCreditApplyService oaCreditApplyService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "课程配置-分页列表查询")
    @ApiOperation(value="课程配置-分页列表查询", notes="课程配置-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<OaConfigureCourseEntity>> queryPageList(OaConfigureCourseEntity oaConfigureCourse,
                                                                @RequestParam(name="pageNo", defaultValue="1") Integer pageNo,
                                                                @RequestParam(name="pageSize", defaultValue="10") Integer pageSize) {
        Result<IPage<OaConfigureCourseEntity>> result = new Result<>();
        QueryWrapper<OaConfigureCourseEntity> queryWrapper = new QueryWrapper<>();
        if(StringUtils.isNotEmpty(oaConfigureCourse.getId())){
            queryWrapper.eq("id",oaConfigureCourse.getId());
        }
        if(StringUtils.isNotEmpty(oaConfigureCourse.getModule())){
            queryWrapper.eq("module",oaConfigureCourse.getModule());
        }
        queryWrapper.eq("del_flag", DelFlagEnum.NO_DEL.getCode());
        queryWrapper.orderByDesc("create_time");
        Page<OaConfigureCourseEntity> page = new Page<>(pageNo, pageSize);
        IPage<OaConfigureCourseEntity> pageList = oaConfigureCourseService.page(page, queryWrapper);
        pageList.getRecords().forEach(oaConfigureCourseEntity ->{
            //根据当前系统时间判断课程状态
            if (oaConfigureCourseEntity.getStartTime().getTime() > System.currentTimeMillis()) {
                oaConfigureCourseEntity.setStatus("1");
            } else if (oaConfigureCourseEntity.getEndTime().getTime() < System.currentTimeMillis()) {
                oaConfigureCourseEntity.setStatus("3");
            } else {
                oaConfigureCourseEntity.setStatus("2");
            }
            oaConfigureCourseEntity.setModule(ModuleDeptEnum.qryByCode(oaConfigureCourseEntity.getModule()));
            oaConfigureCourseEntity.setHoursForCredit(oaConfigureCourseEntity.getHours()+"学时申请"+oaConfigureCourseEntity.getCredit()+"学分");
        });
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "课程配置-添加")
    @ApiOperation(value="课程配置-添加", notes="课程配置-添加")
    @NoRepeatSubmit(expireSeconds = 5)
    @PostMapping(value = "/add")
    public Result<?> add(@RequestBody @Valid OaConfigureCourseEntity oaConfigureCourse) {
        oaConfigureCourseService.saveCourse(oaConfigureCourse);
        return Result.ok("添加成功！");
    }

    /**
      * 编辑
      */
    @AutoLog(value = "课程配置-编辑")
    @ApiOperation(value="课程配置-编辑", notes="课程配置-编辑")
    @PostMapping(value = "/edit")
    public Result<OaConfigureCourseEntity> edit(@RequestBody OaConfigureCourseEntity oaConfigureCourse) {
        Result<OaConfigureCourseEntity> result = new Result<>();
        OaConfigureCourseEntity oaConfigureManageEntity = oaConfigureCourseService.getById(oaConfigureCourse.getId());
        if(oaConfigureManageEntity==null) {
           return result.error500("未找到课程配置");
        }else {
            boolean ok = oaConfigureCourseService.updateCourse(oaConfigureCourse);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "课程配置-通过id删除")
    @ApiOperation(value="课程配置-通过id删除", notes="课程配置-通过id删除")
    @GetMapping(value = "/delete")
    public Result<?> delete(@RequestParam(name="id") String id) {
        List<OaCreditApplyEntity> oaCreditApplyEntities = oaCreditApplyService.list(new QueryWrapper<OaCreditApplyEntity>().eq("course_id", id));
        if(oaCreditApplyEntities.size()>0){
            return new Result<>().error500("存在已申请数据，无法删除!");
        }
        oaConfigureCourseService.removeById(id);
        return Result.ok("删除成功!");
    }


    /**
      * 通过id查询
     */
    @AutoLog(value = "课程配置-通过id查询")
    @ApiOperation(value="课程配置-通过id查询", notes="课程配置-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OaConfigureCourseEntity> queryById(@RequestParam(name="id") String id) {
        Result<OaConfigureCourseEntity> result = new Result<>();
        OaConfigureCourseEntity oaConfigureManage = oaConfigureCourseService.queryById(id);
        result.setResult(oaConfigureManage);
        result.setSuccess(true);
        return result;
    }

    /**
     * 查询课程配置名称列表
     */
    @AutoLog(value = "课程配置-查询课程配置名称列表")
    @ApiOperation(value="课程配置-查询课程配置名称列表", notes="课程配置-查询课程配置名称列表")
    @GetMapping(value = "/queryCourseName")
    public Result<List<OaConfigureCourseEntity>> queryCourseName() {
        Result<List<OaConfigureCourseEntity>> result = new Result<>();
        QueryWrapper<OaConfigureCourseEntity> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("del_flag", DelFlagEnum.NO_DEL.getCode());
        queryWrapper.orderByDesc("create_time");
        List<OaConfigureCourseEntity> list = oaConfigureCourseService.list(queryWrapper);
        result.setSuccess(true);
        result.setResult(list);
        return result;
    }
}
