package com.zs.create.modules.oa.society.controller;

import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.oa.society.dto.SysSchoolTeachersSocietyAndItemDto;
import com.zs.create.modules.oa.society.service.SysSchoolTeachersSocietyService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.Api;

import java.util.List;


/**
 * @Description 社团拟知道单位Controller层
 *
 * <AUTHOR>
 * @email ''
 * @date 2021-05-12 15:45:05
 * @Version: V1.0
 */
@Slf4j
@Api(tags="社团拟知道单位")
@RestController
@RequestMapping("/oa/society/sysSchoolTeachersSociety")
public class SysSchoolTeachersSocietyController {

    @Autowired
    private SysSchoolTeachersSocietyService sysSchoolTeachersSocietyService;

    /**
     * 查询按照前端格式进行查询出所有的拟指导社团返回给前端
     */
    @GetMapping(value = "/getSysSchoolTeachersSocietysAll")
    public Result<List<SysSchoolTeachersSocietyAndItemDto>> getSysSchoolTeachersSocietysAll() {
        Result<List<SysSchoolTeachersSocietyAndItemDto>> result = new Result<>();
        try {
            List<SysSchoolTeachersSocietyAndItemDto> list = sysSchoolTeachersSocietyService.getSysSchoolTeachersSocietysAll();
            result.setResult(list);
            result.setCode(200);
        } catch (Exception e) {
            result.error500("查询出现错误，请联系管理员！");
        }
        return result;
    }

}
