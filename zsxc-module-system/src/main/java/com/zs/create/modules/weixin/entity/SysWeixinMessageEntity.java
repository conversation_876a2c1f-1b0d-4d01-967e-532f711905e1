package com.zs.create.modules.weixin.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.zs.create.common.aspect.annotation.Dict;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * 微信管理
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-04-22 11:45:26
 */
@Data
@TableName("sys_weixin_message")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value = "sys_weixin_message对象", description = "消息回复配置")
public class SysWeixinMessageEntity implements Serializable {
    private static final long serialVersionUID = 1L;

    @TableId
    private String id;

    @ApiModelProperty("关注回复内容")
    private String focusReply;

    @ApiModelProperty("自动回复内容")
    private String autoReply;

    @ApiModelProperty("状态")
    @Dict(dicCode = "enable_status")
    private Integer enableStatus;

    @TableLogic
    private Integer delFlag;
    /**
     *
     */
    private String updateBy;
    /**
     *
     */
    private String createBy;
    /**
     *
     */
    private Date updateTime;
    /**
     *
     */
    private Date createTime;
}
