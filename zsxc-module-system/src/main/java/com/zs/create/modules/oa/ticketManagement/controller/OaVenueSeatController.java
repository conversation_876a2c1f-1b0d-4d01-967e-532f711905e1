package com.zs.create.modules.oa.ticketManagement.controller;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.oa.ticketManagement. entity.OaVenueSeatEntity;
import com.zs.create.modules.oa.ticketManagement. service.OaVenueSeatService;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;

import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import com.zs.create.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
/**
 * @Description 票券管理—座位信息Controller层
 *
 * <AUTHOR> @email 
 * @date 2023-03-31 13:48:59
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "票券管理—座位信息")
@RestController
@RequestMapping("/oa/oaVenueSeat")
public class OaVenueSeatController {
    @Autowired
    private OaVenueSeatService oaVenueSeatService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "票券管理—座位信息-分页列表查询")
    @ApiOperation(value = "票券管理—座位信息-分页列表查询", notes = "票券管理—座位信息-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<OaVenueSeatEntity>> queryPageList(OaVenueSeatEntity oaVenueSeat,
                                                                       @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                                       @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                                       HttpServletRequest req) {
        Result<IPage<OaVenueSeatEntity>> result = new Result<IPage<OaVenueSeatEntity>>();
        LambdaQueryWrapper<OaVenueSeatEntity> queryWrapper = new LambdaQueryWrapper<>();
        //自行构造查询参数
        Page<OaVenueSeatEntity> page = new Page<OaVenueSeatEntity>(pageNo, pageSize);
        IPage<OaVenueSeatEntity> pageList = oaVenueSeatService.page(page, queryWrapper);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "票券管理—座位信息-添加")
    @ApiOperation(value = "票券管理—座位信息-添加", notes = "票券管理—座位信息-添加")
    @PostMapping(value = "/add")
    public Result<OaVenueSeatEntity> add(@RequestBody OaVenueSeatEntity oaVenueSeat) {
        Result<OaVenueSeatEntity> result = new Result<OaVenueSeatEntity>();
            oaVenueSeatService.save(oaVenueSeat);
        result.success("添加成功！");
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "票券管理—座位信息-编辑")
    @ApiOperation(value = "票券管理—座位信息-编辑", notes = "票券管理—座位信息-编辑")
    @PutMapping(value = "/edit")
    public Result<OaVenueSeatEntity> edit(@RequestBody OaVenueSeatEntity oaVenueSeat) {
        Result<OaVenueSeatEntity> result = new Result<OaVenueSeatEntity>();
            OaVenueSeatEntity oaVenueSeatEntity = oaVenueSeatService.getById(oaVenueSeat.getId());
        if (oaVenueSeatEntity == null) {
            return result.error500("未找到对应实体");
        } else {
            boolean ok = oaVenueSeatService.updateById(oaVenueSeat);
            return ok ? result.success("修改成功!") : result.error500("修改失败!");
        }
    }

    /**
     * 通过id删除
     */
    @AutoLog(value = "票券管理—座位信息-通过id删除")
    @ApiOperation(value = "票券管理—座位信息-通过id删除", notes = "票券管理—座位信息-通过id删除")
    @PostMapping(value = "/delete/{id}")
    public Result<?> delete(@PathVariable(name = "id") String id) {
        if (StringUtils.isEmpty(id)) {
            return Result.error("请选择要删除的数据!");
        }
            oaVenueSeatService.removeById(id);
        return Result.ok("删除成功!");
    }

    /**
      *  批量删除
     */
    @AutoLog(value = "票券管理—座位信息-批量删除")
    @ApiOperation(value = "票券管理—座位信息-批量删除", notes = "票券管理—座位信息-批量删除")
    @PostMapping(value = "/deleteBatch/{ids}")
    public Result<OaVenueSeatEntity> deleteBatch(@PathVariable(name = "ids") String ids) {
        Result<OaVenueSeatEntity> result = new Result<OaVenueSeatEntity>();
        if (ids == null || StringUtils.isEmpty(ids)) {
            result.error500("参数不识别！");
        } else {
            this.oaVenueSeatService.removeByIds(Arrays.asList(ids.split(",")));
            result.success("删除成功!");
        }
        return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "票券管理—座位信息-通过id查询")
    @ApiOperation(value = "票券管理—座位信息-通过id查询", notes = "票券管理—座位信息-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<OaVenueSeatEntity> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<OaVenueSeatEntity> result = new Result<OaVenueSeatEntity>();
            OaVenueSeatEntity oaVenueSeat = oaVenueSeatService.getById(id);
        if (oaVenueSeat==null){
            result.error500("未找到对应实体");
        }else{
            result.setResult(oaVenueSeat);
            result.setSuccess(true);
        }
        return result;
    }

    /**
      * 导出excel
     */
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(HttpServletRequest request, HttpServletResponse response) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        // Step.1 组装查询条件
        QueryWrapper<OaVenueSeatEntity> queryWrapper = null;
        try {
            String paramsStr = request.getParameter("paramsStr");
            if (oConvertUtils.isNotEmpty(paramsStr)) {
                String deString = URLDecoder.decode(paramsStr, "UTF-8");
                    OaVenueSeatEntity oaVenueSeat = JSON.parseObject(deString, OaVenueSeatEntity.class);
                queryWrapper = QueryGenerator.initQueryWrapper(oaVenueSeat, request.getParameterMap());
            }
        } catch (UnsupportedEncodingException e) {
            log.error(e.getMessage(), e);
        }

        //Step.2 AutoPoi 导出Excel
        ModelAndView mv = new ModelAndView(new JeecgEntityExcelView());
        List<OaVenueSeatEntity> pageList = oaVenueSeatService.list(queryWrapper);
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "票券管理—座位信息列表");
        mv.addObject(NormalExcelConstants.CLASS, OaVenueSeatEntity.class);
        mv.addObject(NormalExcelConstants.PARAMS, new ExportParams("票券管理—座位信息列表数据", "导出人:"+ sysUser.getRealname(), "导出信息"));
        mv.addObject(NormalExcelConstants.DATA_LIST, pageList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<OaVenueSeatEntity> listOaVenueSeats = ExcelImportUtil.importExcel(file.getInputStream(), OaVenueSeatEntity.class, params);
                    oaVenueSeatService.saveBatch(listOaVenueSeats);
                return Result.ok("文件导入成功！数据行数:" + listOaVenueSeats.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
