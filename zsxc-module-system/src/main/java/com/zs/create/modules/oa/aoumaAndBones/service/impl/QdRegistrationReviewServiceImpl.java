package com.zs.create.modules.oa.aoumaAndBones.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.base.enums.QdStatusEnum;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.SnowIdUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.campusapp.entity.PushTaskDto;
import com.zs.create.modules.campusapp.service.CampusAppService;
import com.zs.create.modules.item.entity.ItemAuditDTO;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.item.entity.ScItemRegistrationEntity;
import com.zs.create.modules.item.entity.ScParticipateItemEntity;
import com.zs.create.modules.item.enums.ItemFlowPdKeyEnum;
import com.zs.create.modules.item.mapper.ScItemMapper;
import com.zs.create.modules.item.service.ScItemRegistrationService;
import com.zs.create.modules.message.entity.WxCommonMsgInfo;
import com.zs.create.modules.mq.producer.WxMessageSender;
import com.zs.create.modules.oa.aoumaAndBones.dto.ApplyVerifyReqDto;
import com.zs.create.modules.oa.aoumaAndBones.dto.QdClassRspDto;
import com.zs.create.modules.oa.aoumaAndBones.dto.RegistrationReviewReqDto;
import com.zs.create.modules.oa.aoumaAndBones.dto.RegistrationReviewRspDto;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdParticipateEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdPublicEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdScoreEntity;
import com.zs.create.modules.oa.aoumaAndBones.mapper.QdClassMapper;
import com.zs.create.modules.oa.aoumaAndBones.mapper.QdParticipateMapper;
import com.zs.create.modules.oa.aoumaAndBones.mapper.QdPublicMapper;
import com.zs.create.modules.oa.aoumaAndBones.mapper.QdScoreMapper;
import com.zs.create.modules.oa.aoumaAndBones.service.QdRegistrationReviewService;
import com.zs.create.modules.personal.entity.ScCalendarEntity;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.entity.SysWorkflowEntity;
import com.zs.create.modules.system.mapper.SysDepartMapper;
import com.zs.create.modules.system.mapper.SysUserDepartMapper;
import com.zs.create.modules.system.service.ISysUserService;
import com.zs.create.modules.system.service.SysWorkflowService;
import com.zs.create.modules.weixin.entity.WxUserDTO;
import com.zs.create.modules.weixin.service.SysWeixinUserService;
import com.zs.create.modules.workflow.instance.service.impl.ProcessInstanceService;
import com.zs.create.modules.workflow.service.IWorkflowService;
import com.zs.create.modules.workflow.util.TaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.activiti.engine.*;
import org.activiti.engine.impl.RepositoryServiceImpl;
import org.activiti.engine.impl.persistence.entity.ProcessDefinitionEntity;
import org.activiti.engine.impl.pvm.PvmActivity;
import org.activiti.engine.impl.pvm.PvmTransition;
import org.activiti.engine.impl.pvm.process.ActivityImpl;
import org.activiti.engine.runtime.ProcessInstance;
import org.activiti.engine.task.Task;
import org.apache.shiro.SecurityUtils;
import org.apache.shiro.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

/**
 * @ClassName suzheng
 * @Description TODO
 * @date 2022-11-08  22:33
 * @Version 1.0
 */
@Service
@Slf4j
public class QdRegistrationReviewServiceImpl implements QdRegistrationReviewService {

    private static final String TW_CODE = "1";

    @Autowired
    private IdentityService identityService;

    @Autowired
    private RuntimeService runtimeService;

    @Autowired
    private TaskService taskService;

    @Autowired
    ProcessInstanceService processInstanceService;

    @Resource
    private QdParticipateMapper qdParticipateMapper;

    @Resource
    private SysWorkflowService sysWorkflowService;

    @Resource
    protected SysWeixinUserService sysWeixinUserService;

    @Resource
    private ISysUserService sysUserService;

    @Resource
    private QdClassMapper qdClassMapper;

    @Resource
    private WxMessageSender wxMessageSender;

    @Resource
    private IWorkflowService workflowService;

    @Resource
    private SysUserDepartMapper sysUserDepartMapper;

    @Resource
    private SysDepartMapper sysDepartMapper;

    @Resource
    private ScItemMapper scItemMapper;

    @Resource
    private QdPublicMapper qdPublicMapper;

    @Resource
    private QdScoreMapper qdScoreMapper;

    @Autowired
    private ScItemRegistrationService scItemRegistrationService;
    @Autowired
    private CampusAppService campusAppService;
    @Value("${gzhAppid}")
    private String appid;


    /**
     * 启动流程实例
     *
     * @param qdParticipate
     * @param currentUserId
     */
    @Override
    public ProcessInstance startApplyProcess(QdParticipateEntity qdParticipate, String currentUserId) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        //创建流程变量
        Map<String, Object> variables = new HashMap<>(2);
        SysDepart classInfoDepart = sysUserDepartMapper.qryByUserId(sysUser.getId());
        if (Objects.isNull(classInfoDepart)) {
            throw new ZsxcBootException("当前用户无所在班级，不能报名，请联系管理员！");
        }

        variables.put("deptId", classInfoDepart.getId());
        variables.put("businessDeptId", qdParticipate.getTzb());
        //启动流程实例
        identityService.setAuthenticatedUserId(currentUserId);

        //运行流程实例并获取流程数据
        ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(ItemFlowPdKeyEnum.QMDGBM_SQ.getPdKey(), qdParticipate.getId(), variables);

        //自动审核第一个申请任务
        List<Task> tasks = taskService.createTaskQuery().active()
                .processInstanceId(processInstance.getId())
                .orderByTaskCreateTime().asc().list();//通过流程实例获取正在执行的任务
        if (!CollectionUtils.isEmpty(tasks)) {
            for (Task task : tasks) {
                ItemAuditDTO itemAuditDTO = new ItemAuditDTO();
                itemAuditDTO.setTaskId(task.getId()).setAuditNote("申请");
                registrationReview(itemAuditDTO, currentUserId, null, Boolean.TRUE);
            }
        }
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstance.getId()));
        return processInstance;
    }

    /**
     * 第一步审核人未审核时撤回报名流程
     *
     * @param applyId
     * @return
     */
    @Override
    public Result<?> recallAuditReview(String applyId) {
        Result<?> result = new Result<>();
        QdParticipateEntity qdParticipateEntity = qdParticipateMapper.selectById(applyId);
        if (oConvertUtils.isEmpty(qdParticipateEntity)) throw new ZsxcBootException("报名人员已被删除，请刷新列表");
        if (!QdStatusEnum.wsh_status.getCode().equals(qdParticipateEntity.getApplyStatus())) {
            throw new ZsxcBootException("报名流程已被审核，无法撤回");
        }
        //结束流程
        String processInstanceId = qdParticipateEntity.getProcessInstanceId();
        ProcessEngine processEngine = ProcessEngines.getDefaultProcessEngine();
        processEngine.getRuntimeService().deleteProcessInstance(processInstanceId, "删除当前流程");
        //重置审核状态
        qdParticipateEntity.setApplyStatus(QdStatusEnum.zc_status.getCode());
        qdParticipateMapper.updById(qdParticipateEntity);
        // 推送消息
        campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
        result.setSuccess(true);
        result.success("撤回成功");
        return result;
    }

    /**
     * 报名审核任务列表导出表格
     *
     * @param registrationReviewReqDto
     * @return
     */
    @Override
    public List<RegistrationReviewRspDto> registrationReviewAll(RegistrationReviewReqDto registrationReviewReqDto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        registrationReviewReqDto.setAssignee(sysUser.getId());
        //自行构造查询参数
        List<RegistrationReviewRspDto> reviewRspDtoIPage = new ArrayList<>();
        if (RegistrationReviewReqDto.QUERY_TODO.equals(registrationReviewReqDto.getQueryType())) {
            //查询待审核列表
            reviewRspDtoIPage = qdParticipateMapper.todoReviewPage(registrationReviewReqDto);
        }
        if (RegistrationReviewReqDto.QUERY_HISTORY.equals(registrationReviewReqDto.getQueryType())) {
            Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getId());
            //已审核列表
            if (twPersonInCharge) {
                reviewRspDtoIPage = qdParticipateMapper.historyReviewPage(registrationReviewReqDto, TW_CODE);
            } else {
                reviewRspDtoIPage = qdParticipateMapper.historyReviewPage(registrationReviewReqDto, null);
            }

        }
        reviewRspDtoIPage.forEach(item -> item.setApplyStatusName(QdStatusEnum.codeOf(item.getApplyStatus()).getDesc()));

        return reviewRspDtoIPage;
    }

    /**
     * 报名结果确认
     *
     * @param classId
     * @return
     */
    @Override
    @Transactional
    public Result<?> applyVerify(String classId) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        Result<Object> result = new Result<>();
        //根据班级id查询已公示结束的报名人员信息
        List<QdParticipateEntity> qdParticipateEntities = qdParticipateMapper.qryByClassId(classId);
        if (CollectionUtils.isEmpty(qdParticipateEntities)) {
            throw new ZsxcBootException("当前班级没有已公示的报名学生");
        }
        //相关表插入数据
        insert3Table(classId, qdParticipateEntities, sysUser.getId());
        //批量修改学生报名状态为已报名
        qdParticipateMapper.updateByIds(qdParticipateEntities);

        result.setSuccess(true);
        result.setMessage("确认成功");
        return result;
    }


    /**
     * 把报名公示过的人员数据插入到相关的3张表里，并把状态改为已报名（人员导入也调用）
     */
    @Override
    public void insert3Table(String classId, List<QdParticipateEntity> qdParticipateEntities, String assignee) {
        //查询班级所有的课程
        List<ScItemEntity> scItemEntities = scItemMapper.selectByClassId(classId);

        qdParticipateEntities.forEach(qdParticipateEntity -> {
            if (!CollectionUtils.isEmpty(scItemEntities)) {
                scItemEntities.forEach(item -> {
                    //添加报名表数据
                    ScItemRegistrationEntity scItemRegistrationEntity = new ScItemRegistrationEntity();
                    scItemRegistrationEntity.setId(SnowIdUtils.uniqueLongHex())
                            .setRealname(qdParticipateEntity.getUserName())
                            .setApplyWay(0).setItemId(item.getId())
                            .setUserId(qdParticipateEntity.getUserId())
                            .setStatus("1")
                            .setUsername(qdParticipateEntity.getUserId())
                            .setDelFlag(DelFlagEnum.NO_DEL.getCode())
                            .setItemHours(item.getDuration());

                    //添加sc_participate_item表数据
                    ScParticipateItemEntity scParticipateItemEntity = new ScParticipateItemEntity();
                    BeanUtil.copyProperties(item, scParticipateItemEntity, true,
                            CopyOptions.create().setIgnoreNullValue(true).setIgnoreProperties("id").setIgnoreError(true));//非空赋值
                    scParticipateItemEntity.setUserId(qdParticipateEntity.getUserId()).setUserName(qdParticipateEntity.getUserId())
                            .setUserCode(qdParticipateEntity.getUserId()).setItemId(item.getId()).setDelFlag(DelFlagEnum.NO_DEL.getCode());

                    //添加日程表数据
                    ScCalendarEntity scCalendarEntity = new ScCalendarEntity();
                    scCalendarEntity.setId(SnowIdUtils.uniqueLongHex()).setTitle(item.getItemName())
                            .setPlace(item.getPlaceInfo()).setContent(item.getBaseContent())
                            .setStartTime(item.getSt()).setEndTime(item.getEt()).setItemId(item.getId())
                            .setUserId(qdParticipateEntity.getUserId()).setDelFlag(DelFlagEnum.NO_DEL.getCode());

                    //存表
                    scItemRegistrationService.insertEnter(scItemRegistrationEntity, scCalendarEntity, scParticipateItemEntity);
                });
            }
            //往成绩表添加数据
            QdScoreEntity qdScoreEntity = new QdScoreEntity();
            qdScoreEntity.setClassId(classId)
                    .setCreateBy(assignee)
                    .setCreateTime(new Date())
                    .setUserId(qdParticipateEntity.getUserId())
                    .setUserName(qdParticipateEntity.getUserName());
            qdScoreMapper.insert(qdScoreEntity);
            //修改学生报名状态为已报名
            qdParticipateEntity.setApplyStatus(QdStatusEnum.ybm_status.getCode());
            qdParticipateEntity.setUpdateBy(assignee);
            qdParticipateEntity.setUpdateTime(new Date());
        });
    }


    /**
     * 报名审核任务列表查询
     *
     * @param registrationReviewReqDto
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public IPage<RegistrationReviewRspDto> registrationReviewList(RegistrationReviewReqDto registrationReviewReqDto, Integer pageNo, Integer pageSize) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        registrationReviewReqDto.setAssignee(sysUser.getId());
        //自行构造查询参数
        Page<RegistrationReviewRspDto> page = new Page<RegistrationReviewRspDto>(pageNo, pageSize);
        IPage<RegistrationReviewRspDto> reviewRspDtoIPage = null;
        if (RegistrationReviewReqDto.QUERY_TODO.equals(registrationReviewReqDto.getQueryType())) {
            //查询待审核列表
            reviewRspDtoIPage = this.todoReviewPage(page, registrationReviewReqDto);
        }
        if (RegistrationReviewReqDto.QUERY_HISTORY.equals(registrationReviewReqDto.getQueryType())) {
            Boolean twPersonInCharge = sysDepartMapper.isTwPersonInCharge(sysUser.getId());
            if (twPersonInCharge) {
                //已审核列表
                reviewRspDtoIPage = this.historyReviewPage(page, registrationReviewReqDto, TW_CODE);
            } else {
                //已审核列表
                reviewRspDtoIPage = this.historyReviewPage(page, registrationReviewReqDto);
            }
        }
        if (!Objects.isNull(reviewRspDtoIPage)) {
            reviewRspDtoIPage.getRecords().forEach(item -> item.setApplyStatusName(QdStatusEnum.codeOf(item.getApplyStatus()).getDesc()));
        }

        return reviewRspDtoIPage;
    }

    /**
     * 报名流程审核
     *
     * @param itemAuditDTO
     */
    @Override
    @Transactional(rollbackFor = RuntimeException.class)
    public String registrationReview(ItemAuditDTO itemAuditDTO, String assignee, Map<String, Object> variables, Boolean autoAuditFirst) {

        String taskId = itemAuditDTO.getTaskId();
        String auditNote = itemAuditDTO.getAuditNote();
        Task task = taskService.createTaskQuery().taskId(taskId).active().singleResult();
        log.info("审核日志信息：{}", task);
        if (null == task) {
            throw new ZsxcBootException("当前任务不存在或已经被审核");
        }
        String businessKey = getTaskBusinessKey(task);
        log.info("业务流程id:{}", businessKey);

        //保存工作流
        saveWorkflow(taskId, businessKey, assignee, task.getProcessInstanceId(), auditNote);
        taskService.claim(taskId, assignee);
        taskService.addComment(taskId, task.getProcessInstanceId(), auditNote);
        //modify by hy
        taskService.setVariablesLocal(taskId, variables);
        taskService.complete(taskId, variables);
        //流程结束修改状态
        modifyStatus4ProcessEndAndSendWxMes(itemAuditDTO, task.getProcessInstanceId(), assignee, businessKey, variables, auditNote);
        return task.getProcessInstanceId();
    }

    /**
     * 查看所有班级
     *
     * @return
     */
    @Override
    public List<QdClassRspDto> qdClassRspDtoList() {
        List<QdClassRspDto> qdClassRspDtoList = qdClassMapper.selectAll();
        return qdClassRspDtoList;
    }

    /**
     * 分页查看班级（小程序端）
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    @Override
    public IPage<QdClassRspDto> qdClassRspDtoListForPage(Integer pageNo, Integer pageSize) {
        Page<QdClassRspDto> page = new Page<QdClassRspDto>(pageNo, pageSize);
        return qdClassMapper.selectAllByPage(page);
    }

    /**
     * 流程结束修改状态
     *
     * @param itemAuditDTO
     * @param processInstanceId
     * @param assignee
     * @param businessKey
     * @param variables
     * @param auditNote
     */
    private void modifyStatus4ProcessEndAndSendWxMes(ItemAuditDTO itemAuditDTO, String processInstanceId, String assignee, String businessKey, Map<String, Object> variables, String auditNote) {
        QdParticipateEntity qdParticipateEntity = qdParticipateMapper.selectById(businessKey);
        if (Objects.isNull(qdParticipateEntity)) {
            throw new ZsxcBootException("报名信息不存在");
        }
        boolean aBoolean = processInstanceService.judgeProcessIsEnd(processInstanceId);
        if (aBoolean) {
            if (null != variables && ItemAuditDTO.PROCESS_REJECT.equals(variables.get("status"))) {
                //驳回结束
                Integer rejectStatus = itemAuditDTO.getRejectstatus();
                if (rejectStatus.equals(DelFlagEnum.NO_DEL.getCode())) {
                    //驳回不可修改
                    qdParticipateEntity.setApplyStatus(QdStatusEnum.btjbm_status.getCode());
                    qdParticipateMapper.updateById(qdParticipateEntity);
                }
                if (rejectStatus.equals(DelFlagEnum.DEL.getCode())) {
                    //修改后再提交
                    qdParticipateEntity.setApplyStatus(QdStatusEnum.xghztj_status.getCode());
                    qdParticipateMapper.updateById(qdParticipateEntity);
                }
                //给报名用户发送消息提醒
                sendAuditResult2RegistratuinUser(qdParticipateEntity, ItemAuditDTO.PROCESS_REJECT, assignee, null, auditNote);
            } else if (null != variables && ItemAuditDTO.PROCESS_PASS.equals(variables.get("status"))) {
                //审核通过
                qdParticipateEntity.setApplyStatus(QdStatusEnum.ysh_status.getCode());
                qdParticipateMapper.updateById(qdParticipateEntity);
                //给报名用户发送消息提醒
                sendAuditResult2RegistratuinUser(qdParticipateEntity, ItemAuditDTO.PROCESS_PASS, assignee, null, auditNote);
            }
        } else {
            //流程未结束
            processNotEndMes(processInstanceId, qdParticipateEntity);
        }
    }

    /**
     * 流程未结束消息提醒
     *
     * @param processInstanceId
     * @param qdParticipateEntity
     */
    private void processNotEndMes(String processInstanceId, QdParticipateEntity qdParticipateEntity) {
        Task currentTask = taskService.createTaskQuery()
                .processInstanceId(processInstanceId)
                .active().singleResult();
        qdParticipateEntity.setApplyStatus(QdStatusEnum.shz_status.getCode());
        qdParticipateMapper.updateById(qdParticipateEntity);

        List<String> taskCandidates = workflowService.getTaskUser(currentTask.getId());
        if (!CollectionUtils.isEmpty(taskCandidates)) {
            for (String reciveUserId : taskCandidates) {
                String title = "报名审核提醒";
                String remark = "点击可查看详情";
                WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(reciveUserId, appid);
                if (null == wxMesUserInfo) {
                    log.error("给代办人发送待办任务消息，未找到接收人：{} 的信息", reciveUserId);
                    continue;
                }
                String openId = wxMesUserInfo.getOpenId();
                String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的工作提醒";
                String contentBuilder = "有一条新的：“" +
                        qdParticipateEntity.getClassName() +
                        "”报名信息，等待您的审核，请及时处理。";
                WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                        .setUseCommonTemplate(Boolean.TRUE)
                        .setTheme(theme)
                        .setTitle(title)
                        .setUserId(wxMesUserInfo.getUsername())
                        .setCreateDate(new Date())
                        .setContent(contentBuilder)
                        .setOpenId(openId)
                        .setMiniAppUrl("/my/class/classReviewList")
                        .setRemark(remark);
                wxMessageSender.wxMessageSend(wxCommonMsgInfo);
            }
        }
    }

    /**
     * 给报名用户发送消息提醒
     *
     * @param qdParticipateEntity
     * @param processReject
     * @param assignee
     * @param placeApply
     * @param auditNote
     */
    private void sendAuditResult2RegistratuinUser(QdParticipateEntity qdParticipateEntity, Integer processReject, String assignee, String placeApply, String auditNote) {
        if (!Objects.isNull(qdParticipateEntity)) {
            String createUserId = qdParticipateEntity.getCreateBy();
            String title = "报名审核提醒";
            StringBuilder contentBuilder = new StringBuilder();
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(createUserId, appid);
            if (null == wxMesUserInfo) {
                log.error("给报名用户发送审核结果消息，未找到接收人：{} 的信息", createUserId);
                return;
            }
            String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的审核提醒";
            String openId = wxMesUserInfo.getOpenId();
            if (ItemAuditDTO.PROCESS_PASS.equals(processReject)) {
                contentBuilder.append("您报名的班级：“").append(qdParticipateEntity.getClassName()).append("”已经审核通过。");
            } else {
                contentBuilder.append("“")
                        .append(qdParticipateEntity.getClassName())
                        .append("”")
                        .append("报名被驳回，驳回原因：")
                        .append(auditNote)
                        .append(",请及时处理");
            }
            String remark = "点击可查看详情";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(qdParticipateEntity.getCreateBy())
                    .setCreateDate(new Date())
                    .setMiniAppUrl("my/class/classEnrollDeclare?classId=" + qdParticipateEntity.getClassId()
                            + "&applyStatus=" + qdParticipateEntity.getApplyStatus()
                            + "&processInstanceId=" + qdParticipateEntity.getProcessInstanceId() + "&type=1")
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }

    /**
     * 已审核列表
     *
     * @param page
     * @param registrationReviewReqDto
     * @return
     */
    private IPage<RegistrationReviewRspDto> historyReviewPage(Page<RegistrationReviewRspDto> page, RegistrationReviewReqDto registrationReviewReqDto) {
        return qdParticipateMapper.historyReviewPage(page, registrationReviewReqDto, null);
    }

    /**
     * 已审核列表(校团委)
     *
     * @param page
     * @param registrationReviewReqDto
     * @return
     */
    private IPage<RegistrationReviewRspDto> historyReviewPage(Page<RegistrationReviewRspDto> page, RegistrationReviewReqDto registrationReviewReqDto, String code) {
        return qdParticipateMapper.historyReviewPage(page, registrationReviewReqDto, code);
    }

    /**
     * 查询待审核列表
     *
     * @param page
     * @param registrationReviewReqDto
     * @return
     */
    private IPage<RegistrationReviewRspDto> todoReviewPage(Page<RegistrationReviewRspDto> page, RegistrationReviewReqDto registrationReviewReqDto) {
        IPage<RegistrationReviewRspDto> qdParticipateEntityIPage = qdParticipateMapper.todoReviewPage(page, registrationReviewReqDto);
        return qdParticipateEntityIPage;
    }

    /**
     * 根据task 获取业务id
     *
     * @param task
     * @return
     */
    private String getTaskBusinessKey(Task task) {
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery()
                .processInstanceId(task.getProcessInstanceId()).active().singleResult();
        log.info("===>>>流程实例信息:{}", processInstance);
        return processInstance.getBusinessKey();
    }

    /**
     * 保存工作流
     *
     * @param taskId
     * @param businessKey
     * @param assignee
     * @param processInstanceId
     * @param auditNote
     */
    public void saveWorkflow(String taskId, String businessKey, String assignee, String processInstanceId, String auditNote) {
        // 获得审核人列表
        String candidate = String.join(",", TaskUtil.getCandidateUserIdList(taskId));
        SysWorkflowEntity work = new SysWorkflowEntity();
        work.setItemId(businessKey)
                .setAssignee(assignee)
                .setCandidate(candidate)
                .setTaskId(taskId)
                .setProcessInstanceId(processInstanceId)
//                    .setProcessInstanceKey()
                .setAuditNote(auditNote);
        sysWorkflowService.save(work);
    }

    /**
     * 判断是否是最后一步审核
     *
     * @param taskId
     * @return
     */
    public Boolean nextTaskIsEnd(String taskId) {
        //参数校验
        // 查询当前任务对象
        Task task = ProcessEngines.getDefaultProcessEngine().getTaskService()
                .createTaskQuery().taskId(taskId).singleResult();
        //根据taskId获取流程实例Id
        String processInstanceId = task.getProcessInstanceId();
        String definitionId = ProcessEngines.getDefaultProcessEngine()
                .getRuntimeService().createProcessInstanceQuery()
                .processInstanceId(processInstanceId).singleResult()
                .getProcessDefinitionId();
        ProcessDefinitionEntity processDefinitionEntity =
                (ProcessDefinitionEntity) ((RepositoryServiceImpl)
                        ProcessEngines.getDefaultProcessEngine().getRepositoryService())
                        .getDeployedProcessDefinition(definitionId);
        ActivityImpl activityImpl = processDefinitionEntity.
                findActivity(task.getTaskDefinitionKey());
        // 获取节点所有流向线路信息
        List<PvmTransition> outTransitions = activityImpl.getOutgoingTransitions();
        for (PvmTransition tr : outTransitions) {
            // 获取线路的终点节点
            PvmActivity ac = tr.getDestination();
            //当其中一条流线的中点不是结束节点时，直接返回 false（不是结束节点）
            if (!"endEvent".equals(ac.getProperty("type"))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 批量审核报名结果
     *
     * @param applyVerifyReqDto
     * @return
     */
    @Override
    public Result<?> batchReview(ApplyVerifyReqDto applyVerifyReqDto) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        if (ApplyVerifyReqDto.BATCH_TYPE.equals(applyVerifyReqDto.getType()) && CollectionUtils.isEmpty(applyVerifyReqDto.getIds())) {
            throw new ZsxcBootException("批量审核请先选择数据！");
        }
        try {
            //先判断是否被删除
            applyVerifyReqDto.getIds().forEach(t -> {
                LambdaQueryWrapper<QdParticipateEntity> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(QdParticipateEntity::getClassId, applyVerifyReqDto.getClassId());
                wrapper.eq(QdParticipateEntity::getId, t);
                QdParticipateEntity entity = qdParticipateMapper.selectOne(wrapper);
                if (oConvertUtils.isEmpty(entity)) throw new ZsxcBootException("包含被删除报名人员，请先刷新列表");
            });

            List<String> taskIds = qdParticipateMapper.qryWaitReview(applyVerifyReqDto.getClassId(), applyVerifyReqDto.getIds(), sysUser.getId());
            if (CollectionUtils.isEmpty(taskIds)) throw new ZsxcBootException("未找到待审核人员，请先刷新列表");
            ItemAuditDTO itemAuditDTO = new ItemAuditDTO();
            itemAuditDTO.setAuditNote(applyVerifyReqDto.getAuditNote())
                    .setRejectstatus(applyVerifyReqDto.getRejectstatus())
                    .setStatus(applyVerifyReqDto.getStatus());
            taskIds.forEach(taskId -> {
                itemAuditDTO.setTaskId(taskId);
                Map<String, Object> variables = new HashMap<>();
                variables.put("status", itemAuditDTO.getStatus());
                String processInstanceId = this.registrationReview(itemAuditDTO, sysUser.getId(), variables, Boolean.FALSE);
                // 推送消息
                campusAppService.pushTaskByProcess(new PushTaskDto().setProcessInstanceId(processInstanceId));
            });
            return Result.ok("审核完成");
        } catch (Exception e) {
            log.error("批量审核错误信息()", e);
            return Result.error(e.getMessage());
        }

    }

    /**
     * 一键不推荐报名
     *
     * @param id
     * @return
     */
    @Override
    public Result<?> notRecommendedRegistration(String id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        QdParticipateEntity qdParticipateEntity = qdParticipateMapper.selectById(id);
        if (Objects.isNull(qdParticipateEntity)) {
            throw new ZsxcBootException("报名信息不存在");
        }
        qdParticipateEntity.setApplyStatus(QdStatusEnum.btjbm_status.getCode());
        qdParticipateEntity.setUpdateBy(sysUser.getId());
        qdParticipateEntity.setCreateTime(new Date());
        qdParticipateMapper.updateById(qdParticipateEntity);
        this.sendUserMessage(qdParticipateEntity);
        return Result.ok("修改成功！");
    }

    /**
     * 给报名用户发送消息提醒
     *
     * @param qdParticipateEntity
     */
    private void sendUserMessage(QdParticipateEntity qdParticipateEntity) {
        if (!Objects.isNull(qdParticipateEntity)) {
            String createUserId = qdParticipateEntity.getUserId();
            String title = "报名公示处理结果";
            StringBuilder contentBuilder = new StringBuilder();
            WxUserDTO wxMesUserInfo = sysWeixinUserService.findWxMesUserInfo(createUserId, appid);
            if (null == wxMesUserInfo) {
                log.error("给报名用户发送报名公示处理结果消息，未找到接收人：{} 的信息", createUserId);
                return;
            }
            String theme = wxMesUserInfo.getRealname() + "/" + wxMesUserInfo.getUsername() + ",您有一条新的公示提醒";
            String openId = wxMesUserInfo.getOpenId();
            if (QdStatusEnum.btjbm_status.getCode().equals(qdParticipateEntity.getApplyStatus())) {
                contentBuilder.append(qdParticipateEntity.getUserName()).append("同学,您被”").append(qdParticipateEntity.getClassName()).append("“取消报名。");
            } else {
                contentBuilder.append(qdParticipateEntity.getUserName()).append("同学,您被”").append(qdParticipateEntity.getClassName()).append("“重新添加报名。");
            }
            String remark = "请及时查看";
            WxCommonMsgInfo wxCommonMsgInfo = new WxCommonMsgInfo()
                    .setUseCommonTemplate(Boolean.TRUE)
                    .setTheme(theme)
                    .setTitle(title)
                    .setUserId(qdParticipateEntity.getUserId())
                    .setCreateDate(new Date())
                    .setContent(contentBuilder.toString())
                    .setOpenId(openId)
                    .setRemark(remark);
            wxMessageSender.wxMessageSend(wxCommonMsgInfo);
        }
    }


    /**
     * 一键加入报名
     *
     * @param id
     * @return
     */
    @Override
    @Transactional
    public Result<?> addRegistration(String id) {
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        Assert.notNull(sysUser);
        QdParticipateEntity qdParticipateEntity = qdParticipateMapper.selectById(id);
        if (Objects.isNull(qdParticipateEntity)) {
            throw new ZsxcBootException("报名信息不存在");
        }
        QdPublicEntity qdPublicEntity = qdPublicMapper.qryByClassId(qdParticipateEntity.getClassId());
        if (Objects.isNull(qdPublicEntity)) {
            throw new ZsxcBootException("还未进行公示");
        }

        if (QdPublicEntity.GSZ_STATUS.equals(qdPublicEntity.getPublicStatus())) {
            qdParticipateEntity.setApplyStatus(QdStatusEnum.gsz_status.getCode());
        } else if (QdPublicEntity.GSYJS_STATUS.equals(qdPublicEntity.getPublicStatus())) {
            qdParticipateEntity.setApplyStatus(QdStatusEnum.gsyjs_status.getCode());
        }
        qdParticipateEntity.setUpdateBy(sysUser.getId());
        qdParticipateEntity.setUpdateTime(new Date());
        qdParticipateMapper.updateById(qdParticipateEntity);
        this.sendUserMessage(qdParticipateEntity);
        return Result.ok("修改成功！");
    }

}
