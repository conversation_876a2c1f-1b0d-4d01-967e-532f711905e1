package com.zs.create.modules.personal.entity;

import java.io.Serializable;
import java.util.Date;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import org.jeecgframework.poi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 个人经历表
 * 
 * <AUTHOR>
 * @email null
 * @date 2020-07-06 09:43:48
 */
@Data
@TableName("sc_experience")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="sc_experience对象", description="个人经历表")
public class ScExperienceEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	/**
	 * 主键
	 */
	@TableId
	private String id;
	/**
	 * 项目名称
	 */
	private String itemName;
	/**
	 * 结束时间
	 */
	@JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy-MM-dd")
	private Date endTime;
	/**
	 * 开始时间
	 */
	@JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy/MM/dd")
	private Date startTime;
	/**
	 * 职务
	 */
	private String job;
	/**
	 * 状态0 -奖励 1-惩处
	 */
	private String state;
	/**
	 * 任期
	 */
	private String term;
	/**
	 * 奖惩名称
	 */
	private String bonusPenaltyName;
	/**
	 * 创建人id
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	@JsonFormat(pattern = "yyyy/MM/dd", timezone = "GMT+8")
	@DateTimeFormat(pattern = "yyyy/MM/dd")
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 删除标记
	 */
	private Integer delFlag;

	private String userId;

	/*
	  0-项目,1-任职,2-奖惩
	 */
	private String type;

	/*
	  0-项目,1-任职,2-奖惩
	 */
	private Integer isShow;

	@TableField(exist = false)
	private String name;
}
