package com.zs.create.modules.oa.aoumaAndBones.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.item.entity.ScItemEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdClassEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdCourseEntityDTO;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdParticipateEntity;

import java.util.List;

/**
 * @Description 青马/大骨班课程管理Service层
 *
 * <AUTHOR> @email 
 * @date 2022-11-03 08:41:11
 * @Version: V1.0
 */
public interface QdCourseService extends IService<ScItemEntity> {

    /**
     * 管理人人员查看课程列表
     * @param course
     * @param pageNo
     * @param pageSize
     * @return
     */
    IPage<ScItemEntity> pageQdCourse(ScItemEntity course, Integer pageNo, Integer pageSize);

    /**
     * 课程变更时给所有报名人员发送消息提醒
     * @param itemId
     */
    void sendChangeCourseMesg(String itemId,Integer type);


    IPage<QdCourseEntityDTO> courseForStudent(ScItemEntity course,String drill,String userId, Page<QdCourseEntityDTO> page,Integer pageNo,Integer pageSize);

    /**
     * 返回当前人员通过报名审核的所有班级
     * @param userId
     * @return
     */
    List<QdParticipateEntity> getClassByUserId(String userId);

    /**
     * 返回当前人员所有课程
     * @param userId
     * @return
     */
    IPage<ScItemEntity> queryAllCourse(String userId,Integer pageNo, Integer pageSize);

    Boolean saveCourse(ScItemEntity courseItem, String id);

    Result<?> getButton(LoginUser sysUser, String itemId);

    Boolean delById(String id);
}

