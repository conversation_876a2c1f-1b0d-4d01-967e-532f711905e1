package com.zs.create.modules.communication.vote.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.modules.communication.vote.entity.CpVoteInfoEntity;
import com.zs.create.modules.communication.vote.entity.dto.CpVoteQueryDTO;
import com.zs.create.modules.communication.vote.entity.vo.CpVoteInfoVo;

import java.util.Set;
import java.util.concurrent.ExecutionException;

/**
 * @Description 投票Service层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-17 15:11:04
 * @Version: V1.0
 */
public interface CpVoteInfoService extends IService<CpVoteInfoEntity> {

     Boolean saveCpVote(CpVoteInfoEntity cpVoteInfoEntity);

     Boolean delelteCpVote(Long id);

    CpVoteInfoEntity getOne(Long id);

    CpVoteInfoVo getVoteInfoVo(Long id);

    void addVisitNum(Long id);

    IPage<CpVoteInfoEntity> pageList(Page<CpVoteInfoEntity> page, CpVoteQueryDTO cpVoteQueryDTO , LoginUser user);

    Result<?> vote(Long voteId, Set<Long> optionIds , LoginUser user) throws ExecutionException, InterruptedException;
}

