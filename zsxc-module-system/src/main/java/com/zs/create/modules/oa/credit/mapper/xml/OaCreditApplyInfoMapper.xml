<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.oa.credit.mapper.OaCreditApplyInfoMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.oa.credit.entity.OaCreditApplyInfoEntity" id="oaCreditApplyInfoMap">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="college" column="college"/>
        <result property="classes" column="classes"/>
        <result property="delFlag" column="del_flag"/>
        <result property="dModuleCredit" column="d_module_credit"/>
        <result property="dModuleTime" column="d_module_time"/>
        <result property="zModuleCredit" column="z_module_credit"/>
        <result property="zModuleTime" column="z_module_time"/>
        <result property="tModuleCredit" column="t_module_credit"/>
        <result property="tModuleTime" column="t_module_time"/>
        <result property="mModuleCredit" column="m_module_credit"/>
        <result property="mModuleTime" column="m_module_time"/>
        <result property="lModuleCredit" column="l_module_credit"/>
        <result property="lModuleTime" column="l_module_time"/>
    </resultMap>
    <select id="qryCreditInfoPage" resultType="com.zs.create.modules.oa.credit.entity.OaCreditApplyInfoEntity">
        select cp.id
        ,cp.user_id
        , cp.user_name
        , cp.college
        , cp.classes
        , cp.del_flag
        , cp.d_module_credit dModuleCredit
        , cp.d_module_time dModuleTime
        , cp.z_module_credit zModuleCredit
        , cp.z_module_time zModuleTime
        , cp.t_module_credit tModuleCredit
        , cp.t_module_time tModuleTime
        , cp.m_module_credit mModuleCredit
        , cp.m_module_time mModuleTime
        , cp.l_module_credit lModuleCredit
        , cp.l_module_time lModuleTime
        from oa_credit_apply_info cp
        left join sys_user_depart ud on cp.user_id = ud.user_id
        where cp.del_flag = 0
        <if test="userName != null and userName != ''">
            and (cp.user_id like concat('%',#{userName},'%') or cp.user_name like concat('%',#{userName},'%'))
        </if>
        <if test="depIds != null and depIds.size > 0 ">
            and  ud.dep_id in
            <foreach collection="depIds" item="depId" index="index" open="(" separator="," close=")">
                #{depId}
            </foreach>
        </if>
        group by cp.user_id
        order by cp.college , cp.classes
    </select>
    <select id="qryCreditInfoList" resultType="com.zs.create.modules.oa.credit.entity.OaCreditApplyInfoEntity">
        select cp.id
        ,cp.user_id
        , cp.user_name
        , cp.college
        , cp.classes
        , cp.del_flag
        , cp.d_module_credit dModuleCredit
        , cp.d_module_time dModuleTime
        , cp.z_module_credit zModuleCredit
        , cp.z_module_time zModuleTime
        , cp.t_module_credit tModuleCredit
        , cp.t_module_time tModuleTime
        , cp.m_module_credit mModuleCredit
        , cp.m_module_time mModuleTime
        , cp.l_module_credit lModuleCredit
        , cp.l_module_time lModuleTime
        from oa_credit_apply_info cp
        left join sys_user_depart ud on cp.user_id = ud.user_id
        where cp.del_flag = 0
        <if test="userName != null and userName != ''">
            and (cp.user_id like concat('%',#{userName},'%') or cp.user_name like concat('%',#{userName},'%'))
        </if>
        <if test="depIds != null and depIds.size > 0 ">
            and  ud.dep_id in
            <foreach collection="depIds" item="depId" index="index" open="(" separator="," close=")">
                #{depId}
            </foreach>
        </if>
        group by cp.user_id
        order by cp.college , cp.classes
    </select>

</mapper>