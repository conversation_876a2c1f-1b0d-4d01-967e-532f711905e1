package com.zs.create.modules.score.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: yc
 * @Date: 2022/04/11/10:39
 * @Description:
 */
@Data
public class ScDayHoursDto implements Serializable{

    private static final long serialVersionUID = 1L;


    /**
     * 用户id
     */
    @ApiModelProperty(value = "用户id")
    private String userId;
    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名")
    private String userName;

    /**
     * 获得学时的日期
     */
    @ApiModelProperty(value = "获得学时的日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date day;

    /**
     * 模块
     */
    @ApiModelProperty(value = "模块")
    private String module;

    /**
     * 学时
     */
    @ApiModelProperty(value = "学时")
    private BigDecimal hours;



}
