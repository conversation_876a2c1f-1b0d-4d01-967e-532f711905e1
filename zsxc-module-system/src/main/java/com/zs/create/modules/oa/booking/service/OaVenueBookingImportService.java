package com.zs.create.modules.oa.booking.service;

import com.zs.create.common.api.vo.Result;
import com.zs.create.modules.oa.booking.entity.OaVenueBookingImportEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * @Description 场馆预约人员导入Service层
 *
 * <AUTHOR> @email 
 * @date 2023-05-05 11:14:16
 * @Version: V1.0
 */
public interface OaVenueBookingImportService extends IService<OaVenueBookingImportEntity> {

    OutputStream exportXls(HttpServletResponse response);

    Result<String> importExcel(InputStream in);
}

