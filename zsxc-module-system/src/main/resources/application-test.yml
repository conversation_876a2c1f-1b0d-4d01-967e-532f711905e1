server:
  port: 1202
  servlet:
    context-path: /sc-report-product
    compression:
      enabled: true
      mime-types: application/javascript,application/json,application/xml,text/html,text/xml,text/plain,text/css,image/*

spring:
  datasource:
    druid:
      stat-view-servlet:
        enabled: false
        loginUsername: j8RqTqRqMn
        loginPassword: tH2C&xhcK2eysvHD
      web-stat-filter:
        enabled: false
    dynamic:
      druid: # 全局druid参数，绝大部分值和默认保持一致。(现已支持的参数如下,不清楚含义不要乱设置)
        # 连接池的配置信息
        # 初始化大小，最小，最大
        initial-size: 5
        min-idle: 5
        maxActive: 20
        # 配置获取连接等待超时的时间
        maxWait: 100000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 100000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        # 打开PSCache，并且指定每个连接上PSCache的大小
        poolPreparedStatements: true
        maxPoolPreparedStatementPerConnectionSize: 20
        # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
        filters: stat,wall,slf4j
        wall:
          multi-statement-allow: true
          none-base-statement-allow: true
        # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
        # connectionProperties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      datasource:
        master:
          url: ******************************************************************************************************************************
          username: root
          password: '[o+)d_p/J$@VAo8'
          driver-class-name: com.mysql.jdbc.Driver
          # 多数据源配置
          #multi-datasource1:
          #url: **********************************************************************************************************************************************************
          #username: root
          #password: 123456
          #driver-class-name: com.mysql.jdbc.Driver
  #activiti 配置
  activiti:
    check-process-definitions: false #项目启动不校验部署流程
    async-executor-activate: false # asyncExecutorActivate是指activiti在流程引擎启动就激活AsyncExecutor,异步：true-开启（默认）、false-关闭
  #rabbitmq配置
  rabbitmq:
    host: **************
    port: 5672
    username: root
    password: rabbitmq@ceshi
    virtualHost: /
    publisher-confirms: true
  #redis 配置
  redis:
    database: 0
    host: **************
    lettuce:
      pool:
        max-active: 8   #最大连接数据库连接数,设 0 为没有限制
        max-idle: 8     #最大等待连接中的数量,设 0 为没有限制
        max-wait: -1  #最大建立连接等待时间。如果超过此时间将接到异常。设为-1表示无限制。-1ms
        min-idle: 0     #最小等待连接中的数量,设 0 为没有限制
      shutdown-timeout: 100ms
    password: ''
    port: 6379
    timeout: 100000
  servlet:
    multipart:
      max-file-size: 500MB
      max-request-size: 500MB
  mail:
    host: smtp.qq.com
    username: <EMAIL>
    password: ryujzffanunyjjie
    properties:
      mail:
        smtp:
          auth: true
          starttls:
            enable: true
            required: true
  ## quartz定时任务,采用数据库方式
  quartz:
    job-store-type: jdbc
    properties:
      org:
        quartz:
          scheduler:
            instanceId: AUTO #默认主机名和时间戳生成实例ID,可以是任何字符串，但对于所有调度程序来说，必须是唯一的 对应qrtz_scheduler_state INSTANCE_NAME字段
            #instanceName: clusteredScheduler #quartzScheduler
          jobStore:
            class: org.quartz.impl.jdbcjobstore.JobStoreTX #持久化配置
            driverDelegateClass: org.quartz.impl.jdbcjobstore.StdJDBCDelegate #我们仅为数据库制作了特定于数据库的代理
            useProperties: true #以指示JDBCJobStore将JobDataMaps中的所有值都作为字符串，因此可以作为名称 - 值对存储而不是在BLOB列中以其序列化形式存储更多复杂的对象。从长远来看，这是更安全的，因为您避免了将非String类序列化为BLOB的类版本问题。
            misfireThreshold: 60000 #在被认为“失火”之前，调度程序将“容忍”一个Triggers将其下一个启动时间通过的毫秒数。默认值（如果您在配置中未输入此属性）为60000（60秒）。
            clusterCheckinInterval: 5000 #设置此实例“检入”*与群集的其他实例的频率（以毫秒为单位）。影响检测失败实例的速度。
            isClustered: true #打开群集功能
          threadPool: #连接池
            class: org.quartz.simpl.SimpleThreadPool
            threadCount: 10
            threadPriority: 5
            threadsInheritContextClassLoaderOfInitializingThread: true
  #json 时间戳统一转换
  jackson:
    date-format:   yyyy-MM-dd HH:mm:ss
    time-zone:   GMT+8
  aop:
    proxy-target-class: true
  # 设置静态文件路径，js,css等
  mvc:
    static-path-pattern: /**
  resource:
    static-locations: classpath:/static/,classpath:/public/
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  data:
    mongodb:
      database: ustc_sc_dev
      host: **************
      port: 27017
      username: root
      password: <EMAIL>
      option:
        min-connection-per-host: 0
        max-connection-per-host: 100
        threads-allowed-to-block-for-connection-multiplier: 5
        server-selection-timeout: 30000
        max-wait-time: 120000
        max-connection-idle-time: 0
        max-connection-life-time: 0
        connect-timeout: 10000
        socket-timeout: 0
        socket-keep-alive: false
        ssl-enabled: false
        ssl-invalid-host-name-allowed: false
        always-use-m-beans: false
        heartbeat-socket-timeout: 20000
        heartbeat-connect-timeout: 20000
        min-heartbeat-frequency: 500
        heartbeat-frequency: 10000
        local-threshold: 15
      #uri:  *********************************************
      #uri:  *********************************************
  freemarker:
    cache: false
    suffix: .ftl
    template-loader-path: classpath:/templates/


redisson:
  lock:
    server:
      address: **************:6379
      type: standalone
#mybatis plus 设置
mybatis-plus:
  #配置mybatis的entity对象的mapper.xml所在的目录
  mapper-locations: classpath*:com/zs/create/modules/**/xml/*Mapper.xml
  #配置mybatis的通用枚举
  type-enums-package: com.zs.create.**.enums
  global-config:
    # 关闭MP3.0自带的banner
    banner: false
    db-config:
      #主键类型  0:"数据库ID自增",1:"该类型为未设置主键类型", 2:"用户输入ID",3:"全局唯一ID (数字类型唯一ID)", 4:"全局唯一ID UUID",5:"字符串全局唯一ID (idWorker 的字符串表示)";
      id-type: uuid
      # 默认数据库表下划线命名
      table-underline: true
  configuration:
    # 这个配置会将执行的sql打印出来，在开发或测试的时候可以用
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
##jeecg专用配置
#zsxc :
#  path :
#    #文件上传根目录 设置
#    upload: /opt/zsxc/upload
#    #webapp文件路径
#    webapp: /opt/zsxc/webapp


  thymeleaf:
    prefix: classpath:/templates/
    mode: HTML
    cache: false
    encoding: UTF-8
    #     新版本不支持content-type: text/html，故新写法
    servlet:
      content-type: text/html
    #图片上传地址
photoUrl: /opt/zsxc/photo
filePath: /opt/zsxc/photo
#gzhAppid: wxf0519092dbe6d1d7
#gzhSecret: e9a0a949522a40d31fd5ea22e2fd6908
template_id: wrA0-f6a7Gntsr3NRY0XqeMfi6mHMGMZun3FjZwuTc4
gzhAppid: wx6db09087e83d19e7
gzhSecret: 78a23eaceff2be0e942bc3123209ea69
zyfwAppid: 04c36aa5d668e52e373ee4d349873b31
zyfwSecret: 0eae39a5d01a03877016c04f547d844d
xcxAppid: wxf0bdb1dcd71daf4b
qrUrl: https://yw.zs-si.com/sc-report-product/ # 当前服务所在的域名
fileUrl: https://yw.zs-si.com/
qdUrl: https://yw.zs-si.com/sc-report/joinclub
qdEmailUrl: https://yw.zs-si.com/sc-report


CHUNCK_TEMP_DIR: /opt/share
wx:
  miniapp:
    configs:
      - appid: wxf0bdb1dcd71daf4b
        secret: 96c77414f86f4d5608aa4517c5a4099f
        token: #微信小程序消息服务器配置的token
        aesKey: #微信小程序消息服务器配置的EncodingAESKey
        msgDataFormat: JSON
second-classroom:
  # 文件服务器配置
  fastdfs:
    connect_timeout_in_seconds: 6000
    network_timeout_in_seconds: 6000
    charset: UTF-8
    tracker_servers: **************:22122 #多个 trackerServer中间以逗号分隔

# 微信开放平台配置
wechat:
  open:
    appid: wxf6d29870d9c0fdd0
    secret: e0ec534823ccff105a0d331418f08683
    token:
    aeskey:

# cas 配置
cas:
  prefixUrl: https://ucas.ustc.edu.cn
#  prefixUrl: https://passport.ustc.edu.cn/dev

logging:
  level:
    root: info
    com.zs.create: info

#分布式任务调度
xxl:
  job:
    accessToken: ''
    admin:
      addresses: http://**************:8081/sc-job/
    executor:
      address: ''
      appname: job-executor-ustc
      ip: ''
      logpath: /apps/dekt/xxl-job/jobhandler
      logretentiondays: 30
      port: 9988

#志愿汇接口
zyhApi:
  #测试：http://*************:8080/VMSAPI/api/zkd/volunteerHours.do  正式服： http://third.api.zyh365.com/api/zkd/volunteerHours.do
  url: http://*************:8080/VMSAPI/api/zkd/volunteerHours.do
  #测试：AccessKeyId: c8c2d572f7594056ba7eca61b0313f39  正式服：AccessKeyId:60cd5393842a4bea83813e6fb3488b9a
  AccessKeyId: c8c2d572f7594056ba7eca61b0313f39
  #测试：accessKeySecret: 503387097bda47b5aa6b9305b5d4cceb  正式服：accessKeySecret:0efc7ae3b91f44788f547a7a81b8944a
  accessKeySecret: 503387097bda47b5aa6b9305b5d4cceb

#青年大学习向校团委推送数据链接
youthLearning2TWUrl: https://tuanxiao.ustc.edu.cn/api/portal/open/collectStudyData

agentid : 1000425
corpsecret : VcUrOxJdbaX-V-zlHfIl0qjXVfXeA8rr8nlJ_sljfmw
#redirect_uri : http://***********:8081/login/ustc-h5-product/
redirect_uri : https://yw.zs-si.com/ustc-h5-test/pages/h5logtransit/h5logtransit

#agentid : 1000424
#corpsecret : 9ws1y6Z3xhQs5GdYlQ7QyWFYoPwmvUaBJuypnwiV21c
#redirect_uri : https://yw.zs-si.com/ustc-h5/pages/h5logtransit/h5logtransit

#科大app统一通讯服务平台url
ustc_app_enabled: false
ustc_app_url : https://comm.ustc.edu.cn
ustc_appId : A6AE24E6A345D37CC98E16818F6E3743
ustc_appSecret : WvsjMoY5rlotfZn0W0ozBmQ7QAirYoNtKb8pagwu1hKd0X4ItFij5XsFSVUWEEhVKmfErD9cHkAaZY4DYBtvlOFvU0F28VPvf9ClRFaSbKjtiMyFexQmId2P0MkzvgaXfKccgdjJXDh2pA9TYbZC5NzUxb2iCUonFyDHhKXP4ilASbCpu0vrq1m6roBeudzJMSSM5hFRKz5ewplbIEqGHQ1TCumdYSqahqW2WQHzl2ckgQNdf6Mh3UDBrzGKHyXIzZkaOuHylvrfS0GIBicckU5cveszqCZMAGbMJv65ydyriiClnSDNTuX1nMNbWv1eUQ9MJomnO7tSKUjNyiMgH7F2gxIOji4ymOURnJrNrcG1FUtne8zcQUssYIzXVTlfbwehatvLOJTVv0s5MJWpOTph5HfFTHp5cYwlS7V51V20iJRSty8LmWW0FkvSDdsCKtAWrv0sFZVnOWbgD0wtmPqzJlGlPTjgKZ6zk5FBmqRH5uuTJGyG27AFo4Ll2BcG06zSMALZ7A093quHTMdKdyUeV2iWmlbKnkvD4OJBYPUnNJbEru9Vwe2dI8ggIzmk3PIrBaQ1divxOFZGkFEeetYSVYyC76OHvZGpVWzjUZxWLImuJQhPQNfqmlDIZu6GdRjY6RSvvalfv3q46d7nPA5YFHBjVV0v9PX6kkMy4HDVaMTiGCvocBWTDv7g5qtSkR9GjJYGxf0hejX4OB50f6XavcTjbGJhK1CAvoDKgJG847oBunLjlMqLPDxjsTItxjC28PpufpGCHf68D4Qiyaewb0FcVC6SWEYJpj5lup3tWhi8pLgLnbHRf5K7e67oD8SIC2jOHay4Bs2oRVt6fGWhMS5p3OecqPHnc3bW6GeipG1eHgOto5C1BYj9HquHEEQhXLtmkBsEA8dtj8rNazkcAPDrZrDTdGnJKK2O80aVtCzUPrpXcAoNy9GYR0bEJ6DlEYkmKZVvIEQ5uu7F5Q16DfjBcemU0P9M4anIlBDXkLUBhDwZsA2Vuc1NmuU4pikpROKYp3TEPS30S01HGR5iw01xY4zZxUTYT2gUjjT5vR6yhK8B7YRWXIa9AY4dHODD8xqj1cuhANBihcTSQcwgM13Fly5izlKneW18LMOCRIMD7dncOxVmPt2oSJqQNDKxdB4jSYvylDra2ahT9P8JEDd7fv8BNE8XWlvOZViKlYE2D0SCoeBxbClPDM9idPaskX2kmg6mx0GKc6w8Qou5di1FTZp14SPiHLB82VKzg1SgLs09OmSTjbhzTGxw8CmbbE8MI6PkSM0naPAFaSjrrrzByECRvuq1vJ5JPCnD8Utoj8kHhB48g1NoHUEqSWXLcCTj3kNVBBckL2SeVyOoqdKIU3QUB1SHGowWsG6RDESebVbxGbvw1DIeoQOXJ7hAOX7aTW9nj87loj3YgB0NL2wW5gGJsy8DK5uEA1tk4R4cI5Rz5FfIbI1EE5AjQ6muq8TpIw4Lkp4fJmaUq9mvtTrU

#青春科大app免密Oauth登录url
ustc_app_oauth_url : https://portal.ustc.edu.cn/demo
ustc_oauth_appId : 20241106093742798
ustc_oauth_appSecret : lh9WIKHvcZ9CMAxwoFIDi8AvqaKzOO4M

#消息跳转url前缀
message_url_prefix : https://yw.zs-si.com/ustc-h5-test/

#中国科大app
campusapp:
  enabled: false
  url: https://task.ustc.edu.cn
  appId: 200250317094250348
  appSecret: weir824cbqipbvy3v54dqhi26t53kqy3
  pcUrl: https://yw.zs-si.com/sc-report/
  h5Url: https://yw.zs-si.com/ustc-h5-test/
