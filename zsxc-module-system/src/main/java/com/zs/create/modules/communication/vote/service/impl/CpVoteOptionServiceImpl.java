package com.zs.create.modules.communication.vote.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zs.create.base.enums.DelFlagEnum;
import com.zs.create.common.exception.ZsxcBootException;
import com.zs.create.modules.communication.vote.constants.VoteConstants;
import com.zs.create.modules.communication.vote.entity.CpVoteConfigEntity;
import com.zs.create.modules.communication.vote.entity.CpVoteInfoEntity;
import com.zs.create.modules.communication.vote.entity.CpVoteOptionEntity;
import com.zs.create.modules.communication.vote.mapper.CpVoteOptionMapper;
import com.zs.create.modules.communication.vote.mapper.CpVoteUserMapper;
import com.zs.create.modules.communication.vote.service.CpVoteConfigService;
import com.zs.create.modules.communication.vote.service.CpVoteInfoService;
import com.zs.create.modules.communication.vote.service.CpVoteOptionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @Description 投票Service实现层
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2020-11-17 15:11:04
 * @Version: V1.0
 */
@Service
public class CpVoteOptionServiceImpl extends ServiceImpl<CpVoteOptionMapper, CpVoteOptionEntity> implements CpVoteOptionService {

    @Autowired
    RedisTemplate redisTemplate;
    @Autowired
    @Lazy
    CpVoteInfoService cpVoteInfoService;
    @Autowired
    CpVoteUserMapper cpVoteUserMapper;
    @Autowired
    @Lazy
    CpVoteConfigService cpVoteConfigService;
    @Override
    public Boolean saveOpt(CpVoteOptionEntity cpVoteOption) {
        boolean res = this.saveOrUpdate(cpVoteOption);
        CpVoteInfoEntity voteInfo = cpVoteInfoService.getById(cpVoteOption.getVoteId());
        if(null == voteInfo) throw new ZsxcBootException("vote base info not exist");
        String voteOptionKey = VoteConstants.VOTE_OPTION_KEY_PREFIX + cpVoteOption.getId() ;
        String voteOptIdsKey = VoteConstants.VOTE_OPTIDS_KEY_PREFIX + cpVoteOption.getVoteId();
        if(res){
            Boolean delVoteOption = redisTemplate.delete(voteOptionKey);
            if(delVoteOption){
                //维护 redis 中的 投票选项集合
                redisTemplate.delete(voteOptIdsKey);
                return Boolean.TRUE ;
            }
        }
        return Boolean.FALSE;
    }

    @Override
    public Boolean delById(Long id) {
        CpVoteOptionEntity one = this.getById(id);
        if(one == null) return Boolean.FALSE;
        one.setDelFlag(DelFlagEnum.DEL.getCode());
        boolean res =  this.saveOrUpdate(one);
        String key = VoteConstants.VOTE_OPTION_KEY_PREFIX + id ;
        if(res){
            Boolean delVoteOption = redisTemplate.delete(key);
            if(delVoteOption){
                String voteOptIdsKey = VoteConstants.VOTE_OPTIDS_KEY_PREFIX + one.getVoteId();
                redisTemplate.delete(voteOptIdsKey);
                return Boolean.TRUE ;
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 根据选项id 获取选项基本信息
     * @param id
     * @return
     */
    @Override
    public CpVoteOptionEntity getOne(Long id) {
        String key = VoteConstants.VOTE_OPTION_KEY_PREFIX + id ;
        Map map = redisTemplate.opsForHash().entries(key);
        CpVoteOptionEntity one = BeanUtil.mapToBean(map ,
                CpVoteOptionEntity.class ,true);
        if(CollectionUtils.isEmpty(map)){
            CpVoteOptionEntity option = this.getById(id);
            if(null != option) {
                Long voteId = option.getVoteId();
                CpVoteInfoEntity cpVoteInfoEntity = cpVoteInfoService.getOne(voteId);
                if(null == cpVoteInfoEntity) throw new ZsxcBootException("投票基本信息不存在");
                redisTemplate.opsForHash().putAll(key , BeanUtil.beanToMap(option));
                //保证当前时间-投票结束时间 都是有缓存的
                redisTemplate.expire(key , VoteConstants.getVoteCacheExpire(cpVoteInfoEntity.getVoteEt()),
                        TimeUnit.MILLISECONDS);
            }
            return option;
        }else{
            return one;
        }
    }



    /**
     * 获取 optionIds
     * @param voteId
     * @return
     */
    public List<Long> getOptionIdsByVoteId(Long voteId){
        String voteOptIdsKey = VoteConstants.VOTE_OPTIDS_KEY_PREFIX + voteId;
        String optIds = (String)redisTemplate.opsForValue().get(voteOptIdsKey);
        if(StringUtils.isNotBlank(optIds)){
            return  Arrays.stream(optIds.split(","))
                    .map(s -> Long.parseLong(s.trim()))
                    .collect(Collectors.toList());
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("vote_id" , voteId);
        queryWrapper.eq("del_flag" , DelFlagEnum.NO_DEL.getCode());
        List<CpVoteOptionEntity> optionsList = this.list(queryWrapper);
        if(CollectionUtils.isEmpty(optionsList)) return null;
        List<Long> optIdList = optionsList.parallelStream()
                .map(CpVoteOptionEntity::getId).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(optIdList)){
            CpVoteInfoEntity cpVoteInfoEntity = cpVoteInfoService.getOne(voteId);
            if(null == cpVoteInfoEntity) throw new ZsxcBootException("投票基本信息不存在");
            redisTemplate.opsForValue().setIfAbsent(voteOptIdsKey , optIds);
            redisTemplate.expire(voteOptIdsKey , VoteConstants.getVoteCacheExpire(cpVoteInfoEntity.getVoteEt())
                    , TimeUnit.MILLISECONDS);
        }

        return optIdList;
    }


    /**
     * 根据投票id获取选项list
     * @param voteId
     * @return
     */
    public List<CpVoteOptionEntity> getOptsByVoteId(Long voteId){
        List<CpVoteOptionEntity> list = new ArrayList<>();
        List<Long> optionIdsByVoteId = this.getOptionIdsByVoteId(voteId);
        if(CollectionUtils.isEmpty(optionIdsByVoteId)) return null;
        optionIdsByVoteId.forEach(optId->{
            CpVoteOptionEntity one = this.getOne(optId);
            if(null != one) list.add(one);
        });
        return list;
    }

    @Override
    public List<CpVoteOptionEntity> getAllOptsByVoteId(Long voteId) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("vote_id" , voteId);
        //queryWrapper.eq("del_flag" , DelFlagEnum.NO_DEL.getCode());
        List<CpVoteOptionEntity> optionsList = this.list(queryWrapper);
        return optionsList;
    }


    public List<CpVoteOptionEntity> listOpts(Long voteId , String optContent){


        List<CpVoteOptionEntity> opts = this.getOptsByVoteId(voteId);
        if(null == opts) opts = new ArrayList<>(1);
        if(null!=optContent){
           return opts.stream().filter(opt->opt.getOptContent().contains(optContent))
                    .collect(Collectors.toList());
        }else{
            return opts;
        }
    }


    /**
     * 根据配置是否展示票数 返回
     * @param voteId
     * @param optContent
     * @return
     */
    public List<CpVoteOptionEntity> accordingConfig2ListOpts(Long voteId , String optContent){
        List<CpVoteOptionEntity> cpVoteOptionEntities = this.listOpts(voteId, optContent);
        CpVoteConfigEntity config = cpVoteConfigService.getByVoteId(voteId);
        if(null != config && config.getShowVoteNum().equals(0)){
            cpVoteOptionEntities.forEach(cpVoteOptionEntity -> cpVoteOptionEntity.setVotedNum(null));
        }
        return cpVoteOptionEntities;
    }



}
