package com.zs.create.modules.oa.credit.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zs.create.common.aspect.annotation.Dict;
import com.zs.create.common.util.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 学分申请
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2021-01-19 10:43:42
 */
@Data
@TableName("oa_credit_apply")
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
@ApiModel(value="oa_credit_apply对象", description="学分申请")
public class OaCreditApplyEntity implements Serializable {
	private static final long serialVersionUID = 1L;
	public static final String processDefinitionKey ="credit_audit_sq";
	public static final Integer PROCESS_REJECT = 0;
	public static final Integer PROCESS_PASS = 1;
	public static final String QUERY_TODO = "todo";
	public static final String QUERY_HISTORY = "history";
	/**
	 * 主键
	 */
	@TableId
	private String id;
	/**
	 * 课程id
	 */
	private String courseId;
	/**
	 * 课程名称
	 */
	private String topicTitle;
	/**
	 *模块
	 */
	private String module;
	/**
	 * 用户id
	 */
	private String userId;
	/**
	 * 用户姓名
	 */
	private String userName;
	/**
	 * 转换学时
	 */
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	private BigDecimal hours;
	/**
	 * 转换学分
	 */
	@JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
	private BigDecimal credit;
	/**
	 * 审核状态  0 审核中 1 审核通过 2 审核驳回
	 */
	@Dict(dicCode = "creditapply_status")
	@Excel(name = "审核状态", width = 15,readConverterExp = "0=审核中, 1=审核通过, 2=审核驳回")
	private Integer examineStatus;
	/**
	 * 学分转换制度
	 */
	@TableField(exist = false)
	@ApiModelProperty(value = "学分转换制度")
	private String hoursForCredit;
	/**
	 * 流程实例id
	 */
	private String processInstanceId;
	/**
	 * 删除标记
	 */
	private Integer delFlag;
	/**
	 * 创建人id
	 */
	private String createBy;
	/**
	 * 创建时间
	 */
	private Date createTime;
	/**
	 * 更新人id
	 */
	private String updateBy;
	/**
	 * 修改时间
	 */
	private Date updateTime;

	@TableField(exist = false)
	@ApiModelProperty(value = "查询类型 ： todo ： 代办   history ：  已办")
	private String queryType;

	@TableField(exist = false)
	@ApiModelProperty(value = "任务处理人")
	private String assignee;

	@TableField(exist = false)
	@ApiModelProperty(value = "任务id")
	private String taskId;
}
