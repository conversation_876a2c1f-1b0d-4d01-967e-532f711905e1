package com.zs.create.modules.system.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zs.create.common.api.vo.Result;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.system.vo.LoginUser;
import com.zs.create.common.util.DateUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.system.entity.SysScientificqiValueVO;
import com.zs.create.modules.system.service.SysScientificqiValueService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.shiro.SecurityUtils;
import org.jeecgframework.poi.excel.def.MapExcelConstants;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgMapExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2023/11/9
 */
@Slf4j
@Api(tags = "科气值")
@RestController
@RequestMapping("/sys/scientificqiValue")
public class SysScientificqiValueController {

    @Autowired
    private SysScientificqiValueService sysScientificqiValueService;

    /**
     * 分页列表查询
     * 此接口返回operator为操作人学号，realName为操作人姓名，userName为操作对象学号
     */
    @AutoLog(value = "科气值-分页列表查询")
    @ApiOperation(value = "科气值-分页列表查询", notes = "科气值-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<SysScientificqiValueVO>> queryPageList(@RequestParam(name = "userName", required = false) String userName,
                                                               @RequestParam(name = "type", required = false) String type,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "5") Integer pageSize
                                                               ) {
        Result<IPage<SysScientificqiValueVO>> result = new Result<IPage<SysScientificqiValueVO>>();
        //自行构造查询参数
        Page<SysScientificqiValueVO> page = new Page<SysScientificqiValueVO>(pageNo, pageSize);
        IPage<SysScientificqiValueVO> pageList = sysScientificqiValueService.queryPage(page,userName,type);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }



    /**
     * 日志查询
     */
    @AutoLog(value = "科气值-操作日志查询")
    @ApiOperation(value = "科气值-操作日志查询", notes = "科气值-操作日志查询")
    @GetMapping(value = "/operationLog")
    public Result<IPage<SysScientificqiValueVO>> operationLog(@RequestParam(name = "userName", required = true) String userName,
                                                               @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                               @RequestParam(name = "pageSize", defaultValue = "5") Integer pageSize,
                                                               HttpServletRequest req) {
        Result<IPage<SysScientificqiValueVO>> result = new Result<IPage<SysScientificqiValueVO>>();
        //自行构造查询参数
        Page<SysScientificqiValueVO> page = new Page<SysScientificqiValueVO>(pageNo, pageSize);
        IPage<SysScientificqiValueVO> pageList = sysScientificqiValueService.operationLog(page,userName);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }


    /**
     * 科气值-编辑
     */
    @AutoLog(value = "科气值-编辑")
    @ApiOperation(value = "科气值-编辑", notes = "科气值-编辑")
    @PostMapping(value = "/edit")
    public Result<?> edit(@RequestBody SysScientificqiValueVO sysScientificqiValueVO) {
        boolean ok = sysScientificqiValueService.editScientificqiValue(sysScientificqiValueVO);
        return ok? Result.ok("修改成功") : Result.error("修改失败");
    }


    /**
     * 科气值列表导出excel
     * 此接口返回operator为操作人学号，realName为操作人姓名，userName为操作对象学号
     */
    @AutoLog(value = "科气值-操作日志导出列表")
    @ApiOperation(value = "科气值-操作日志导出列表", notes = "科气值-操作日志导出列表")
    @RequestMapping(value = "/operationLogExportXls")
    public ModelAndView exportXls(@RequestParam(name = "userName") String userName) {
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        //当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ExcelExportEntity> entityList = new ArrayList<>();
        ExcelExportEntity entity = new ExcelExportEntity("操作人", "realName");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("学/工号", "operator");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("时间", "createTime");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("数值", "updateValue");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("事由", "content");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("操作对象", "userName");
        entity.setWidth(30);
        entityList.add(entity);
        //查询条件;
        List<SysScientificqiValueVO> list = sysScientificqiValueService.operationLogList(userName);

        List<Map<String, Object>> dataList = new ArrayList<>();
        for (SysScientificqiValueVO valueVO : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("realName", oConvertUtils.null2String(valueVO.getRealName()));
            map.put("operator", oConvertUtils.null2String(valueVO.getOperator()));
            map.put("createTime", oConvertUtils.null2String(DateUtils.formatDate(valueVO.getCreateTime(), "yyyy-MM-dd HH:mm:ss")));
            map.put("updateValue", oConvertUtils.null2String(valueVO.getUpdateValue()));
            map.put("content", oConvertUtils.null2String(valueVO.getContent()));
            map.put("userName", oConvertUtils.null2String(valueVO.getUserName()));
            dataList.add(map);
        }

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("科气值操作日志导出");
        exportParams.setSecondTitle("导出人：" + sysUser.getRealname() + "，导出时间：" + DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss") + "【 导出说明： 当导出数据量过大时系统将会把数据分成多个Sheet（工作表）导出,默认Sheet从0开始依次递增 】");
        exportParams.setSheetName("Sheet0");
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "科气值操作日志-导出列表");
        mv.addObject(MapExcelConstants.ENTITY_LIST, entityList);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        return mv;
    }




    /**
     * 科气值列表导出excel
     */
    @AutoLog(value = "科气值-导出列表")
    @ApiOperation(value = "科气值-导出列表", notes = "科气值-导出列表 ")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(@RequestParam(name = "userName", required = false) String userName,
                                  @RequestParam(name = "type", required = false) String type) {
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        //当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ExcelExportEntity> entityList = new ArrayList<>();
        ExcelExportEntity entity = new ExcelExportEntity("姓名", "realName");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("学/工号", "userName");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("科气值", "scientificqiValue");
        entity.setWidth(30);
        entityList.add(entity);
        //查询条件;
        List<SysScientificqiValueVO> list = sysScientificqiValueService.queryList(userName,type);

        List<Map<String, Object>> dataList = new ArrayList<>();
        for (SysScientificqiValueVO valueVO : list) {
            Map<String, Object> map = new HashMap<>();
            map.put("realName", oConvertUtils.null2String(valueVO.getRealName()));
            map.put("userName", oConvertUtils.null2String(valueVO.getUserName()));
            map.put("scientificqiValue", oConvertUtils.null2String(valueVO.getScientificqiValue()));
            dataList.add(map);
        }

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("科气值数据导出");
        exportParams.setSecondTitle("导出人：" + sysUser.getRealname() + "，导出时间：" + DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss") + "【 导出说明： 当导出数据量过大时系统将会把数据分成多个Sheet（工作表）导出,默认Sheet从0开始依次递增 】");
        exportParams.setSheetName("Sheet0");
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "科气值-导出列表");
        mv.addObject(MapExcelConstants.ENTITY_LIST, entityList);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        return mv;
    }


}
