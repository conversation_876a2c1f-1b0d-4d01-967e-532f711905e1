package com.zs.create.modules.system.service;

import com.zs.create.modules.system.entity.SysMoveMenuEntity;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;


/**
 * @Description 移动端权限Service层
 *
 * <AUTHOR>
 * @email null
 * @date 2021-03-17 16:44:39
 * @Version: V1.0
 */
public interface SysMoveMenuService extends IService<SysMoveMenuEntity> {


    List<SysMoveMenuEntity> getNodeTree();

    List<SysMoveMenuEntity> getUserRoleTree(String userId);

    List<String> getUserRoleIds(String userId);
}

