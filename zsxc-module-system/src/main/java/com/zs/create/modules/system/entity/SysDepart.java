package com.zs.create.modules.system.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.zs.create.common.aspect.annotation.Dict;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.jeecgframework.poi.excel.annotation.Excel;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.Collection;
import java.util.Date;
import java.util.Objects;

/**
 * <p>
 * 部门表
 * <p>
 *
 * <AUTHOR>
 * @Since 2019-01-22
 */
@Data
@TableName("sys_depart")
public class SysDepart implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 负责人
     */
    public static final String FZR = "fzr";

    //指导老师
    public static final String ZDLS = "zdls";

    //班主任
    public static final String BZR = "bzr";

    //会长团
    public static final String HZT = "hzt";

    //主席团
    public static final String ZXT = "zxt";

    public static final String DEPART_ROLE_USER = "depart_role_user";


    /**
     * ID
     */
    @TableId(type = IdType.UUID)
    private String id;
    /**
     * 父机构ID
     */
    private String parentId;
    /**
     * 机构/部门名称
     */
    @Excel(name = "机构/部门名称", width = 15)
    @NotBlank(message = "机构/部门名称")
    @Length(max = 50 , message = "最大长度不能超过50")
    private String departName;
    /**
     * 英文名
     */
    //@Excel(name = "英文名", width = 15)
    @Length(max = 100 , message = "最大长度不能超过100")
    private String departNameEn;
    /**
     * 缩写
     */
    private String departNameAbbr;
    /**
     * 排序
     */
    @Excel(name = "排序", width = 15)
    private Integer departOrder;
    /**
     * 描述
     */
    //@Excel(name = "描述", width = 15)
    private Object description;
    /**
     * 机构类型
     */
    @Excel(name = "机构类型", width = 15 ,  dicCode = "org_type")
    @Dict(dicCode = "org_type")
    private String orgType;


    /**
     * 机构等级
     */
    private String level;
    /**
     * 机构编码
     */
    @Excel(name = "机构编码", width = 15)
    private String orgCode;
    /**
     * 手机号
     */
    @Excel(name = "手机号", width = 15)
    private String mobile;
    /**
     * 传真
     */
    @Excel(name = "传真", width = 15)
    @Length(max=32, message = "传真字段过长")
    private String fax;
    /**
     * 地址
     */
    @Excel(name = "地址", width = 15)
    private String address;
    /**
     * 备注
     */
    @Excel(name = "备注", width = 15)
    private String memo;
    /**
     * 状态（1启用，0不启用）
     */
    //@Excel(name = "状态", width = 15 , dicCode = "depart_status")
    @Dict(dicCode = "depart_status")
    private String status;
    /**
     * 删除状态（0，正常，1已删除）
     */
    @Excel(name = "删除状态", width = 15 , dicCode = "del_flag")
    @Dict(dicCode = "del_flag")
    private String delFlag;
    /**
     * 创建人
     */
    private String createBy;
    /**
     * 创建日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    /**
     * 更新人
     */
    private String updateBy;
    /**
     * 更新日期
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 父级id集合
     */
    private String pids;

    /**
     * 指导老师
     */
    private String guideTeachers="";

    /**
     * 指导部门
     */
    private String guideDeptId="";

    /**
     * 主席团
     */
    private String bureau="";

    /**
     * 会长团
     */
    private String presidium="";

    /**
     * 班主任
     */
    private String headmaster="";
    /**
     * 负责人
     */
    private String personInCharge="";
    /**
     * 秘书
     */
    private String secretaries="";

    private String star;


    //指导老师姓名
    @TableField(exist = false)
    private String guideTeachersName;

    @TableField(exist = false)
    private Collection<SysUser> guideTeachersList;
    //指导部门姓名
    @TableField(exist = false)
    private String guideDeptName;

    /*@TableField(exist = false)
    private Collection<SysDepart> guideDepts;*/
    //主席团姓名
    @TableField(exist = false)
    private String bureauName;
    //会长团姓名
    @TableField(exist = false)
    private String presidiumName;
    //班主任姓名
    @TableField(exist = false)
    private String headmasterName;
    //负责人姓名
    @TableField(exist = false)
    private String personInChargeName;
    @TableField(exist = false)
    private Collection<SysUser> personInChargeList;
    //上级名称
    @TableField(exist = false)
    private String parentName;
    @TableField(exist = false)
    private String orgTypeName;

    //当前组织院部负责人
    @TableField(exist = false)
    private Boolean collegeType;

    /**
     * 我的部门（添加用户按钮 0：不展示，1：展示）
     */
    @TableField(exist = false)
    private String addUserType;

    /**
     * 团总支名称
     */
    @TableField(exist = false)
    private String departmentName;

    /**
     * 班级团支部标识（0：否，1：是）
     */
    @TableField(exist = false)
    private String tzbType;


    /**
     * 重写equals方法
     */
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        SysDepart depart = (SysDepart) o;
        return Objects.equals(id, depart.id)
                && Objects.equals(parentId, depart.parentId)
                && Objects.equals(departName, depart.departName)
                && Objects.equals(departNameEn, depart.departNameEn)
                && Objects.equals(departNameAbbr, depart.departNameAbbr)
                && Objects.equals(departOrder, depart.departOrder)
                && Objects.equals(description, depart.description)
                && Objects.equals(orgType, depart.orgType)
                && Objects.equals(orgCode, depart.orgCode)
                && Objects.equals(mobile, depart.mobile)
                && Objects.equals(fax, depart.fax)
                && Objects.equals(address, depart.address)
                && Objects.equals(memo, depart.memo)
                && Objects.equals(status, depart.status)
                && Objects.equals(delFlag, depart.delFlag)
                && Objects.equals(createBy, depart.createBy)
                && Objects.equals(createTime, depart.createTime)
                && Objects.equals(updateBy, depart.updateBy)
                && Objects.equals(updateTime, depart.updateTime)
                && Objects.equals(guideTeachers, depart.guideTeachers)
                && Objects.equals(guideDeptId, depart.guideDeptId)
                && Objects.equals(secretaries, depart.secretaries)
                && Objects.equals(headmaster, depart.headmaster)
                && Objects.equals(presidium, depart.presidium)
                && Objects.equals(bureau, depart.bureau)
                && Objects.equals(personInCharge, depart.personInCharge)
                && Objects.equals(parentName,depart.getParentName());
    }

    /**
     * 重写hashCode方法
     */
    @Override
    public int hashCode() {

        return Objects.hash(super.hashCode(), id, parentId, departName,
                departNameEn, departNameAbbr, departOrder, description,
                orgType, orgCode, mobile, fax, address, memo, status,
                delFlag, createBy, createTime, updateBy, updateTime,
                guideTeachers,guideDeptId,secretaries,bureau,presidium,headmaster,personInCharge,parentName);
    }

    public SysDepart() {

    }

    public SysDepart(String id, @NotBlank(message = "机构/部门名称") @Length(max = 50, message = "最大长度不能超过50") String departName) {
        this.id = id;
        this.departName = departName;
    }
}
