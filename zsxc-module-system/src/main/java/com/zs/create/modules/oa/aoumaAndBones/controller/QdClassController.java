package com.zs.create.modules.oa.aoumaAndBones.controller;

import java.util.*;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.zs.create.common.api.vo.Result;
import com.zs.create.base.query.QueryGenerator;
import com.zs.create.common.aspect.annotation.AutoLog;
import com.zs.create.common.aspect.annotation.NoRepeatSubmit;
import com.zs.create.common.util.DateUtils;
import com.zs.create.common.util.oConvertUtils;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdClassEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdParticipateEntity;
import com.zs.create.modules.oa.aoumaAndBones.entity.QdPublicEntity;
import com.zs.create.modules.oa.aoumaAndBones.service.QdClassService;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.zs.create.modules.oa.aoumaAndBones.service.QdParticipateService;
import com.zs.create.modules.oa.aoumaAndBones.service.QdPublicService;
import com.zs.create.modules.system.entity.SysDepart;
import com.zs.create.modules.system.service.ISysDepartService;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.jeecgframework.poi.excel.ExcelImportUtil;
import org.jeecgframework.poi.excel.def.MapExcelConstants;
import org.jeecgframework.poi.excel.def.NormalExcelConstants;
import org.jeecgframework.poi.excel.entity.ExportParams;
import org.jeecgframework.poi.excel.entity.ImportParams;
import org.jeecgframework.poi.excel.entity.params.ExcelExportEntity;
import org.jeecgframework.poi.excel.view.JeecgEntityExcelView;

import org.jeecgframework.poi.excel.view.JeecgMapExcelView;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.web.multipart.MultipartHttpServletRequest;
import org.springframework.web.servlet.ModelAndView;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import com.zs.create.common.system.vo.LoginUser;
import org.apache.shiro.SecurityUtils;
/**
 * @Description 青马/大骨班班级管理Controller层
 *
 * <AUTHOR> @email 
 * @date 2022-11-03 08:41:11
 * @Version: V1.0
 */
@Slf4j
@Api(tags = "青马/大骨班班级管理")
@RestController
@RequestMapping("/qdClass")
public class QdClassController {
    @Autowired
    private QdClassService qdClassService;
    @Autowired
    private ISysDepartService sysDepartService;
    @Autowired
    private QdParticipateService qdParticipateService;
    @Autowired
    private QdPublicService qdPublicService;

    /**
     * 分页列表查询
     */
    @AutoLog(value = "青马/大骨班班级管理-分页列表查询")
    @ApiOperation(value = "青马/大骨班班级管理-分页列表查询", notes = "青马/大骨班班级管理-分页列表查询")
    @GetMapping(value = "/list")
    public Result<IPage<QdClassEntity>> queryPageList(QdClassEntity qdClass,
                                                      @RequestParam(name = "pageNo", defaultValue = "1") Integer pageNo,
                                                      @RequestParam(name = "pageSize", defaultValue = "10") Integer pageSize,
                                                      HttpServletRequest req) {
        Result<IPage<QdClassEntity>> result = new Result<IPage<QdClassEntity>>();
        IPage<QdClassEntity> pageList = qdClassService.pageQdClass(qdClass, pageNo,pageSize);
        result.setSuccess(true);
        result.setResult(pageList);
        return result;
    }

    /**
     * 添加
     */
    @AutoLog(value = "青马/大骨班班级管理-添加")
    @ApiOperation(value = "青马/大骨班班级管理-添加", notes = "青马/大骨班班级管理-添加")
    @PostMapping(value = "/add")
    @NoRepeatSubmit(expireSeconds = 3)
    public Result<?> add(@RequestBody @Valid QdClassEntity qdClass) {
        Result result = qdClassService.saveqdClass(qdClass);
        return result;
    }

    /**
      * 编辑
      */
    @AutoLog(value = "青马/大骨班班级管理-编辑")
    @ApiOperation(value = "青马/大骨班班级管理-编辑", notes = "青马/大骨班班级管理-编辑")
    @PutMapping(value = "/edit")
    @NoRepeatSubmit(expireSeconds = 3)
    @Transactional
    public Result<QdClassEntity> edit(@RequestBody @Valid QdClassEntity qdClass) {
        Result<QdClassEntity> result = new Result<QdClassEntity>();
            QdClassEntity qdClassEntity = qdClassService.getById(qdClass.getId());
        if (qdClassEntity == null) {
            return result.error500("未找到对应班级");
        } else {
            Date applyStarttime = qdClassEntity.getApplyStarttime();
            Date applyEndtime = qdClassEntity.getApplyEndtime();
            Date now = new Date();
            if (now.after(applyEndtime)){
                if (qdClass.getApplyEndtime().compareTo(applyEndtime)!=0 && qdClass.getApplyStarttime().compareTo(applyStarttime)!=0){
                    return result.error500("报名时间已过，不可修改时间");
                }
            }
            if (now.before(applyEndtime) && now.after(applyStarttime)){
                if (qdClass.getApplyStarttime().compareTo(applyStarttime)!=0){
                    return result.error500("报名已开始，不可修改开始时间");
                }
            }
            //修改班级名时同步修改报名表，公示表class_name字段
            List<QdParticipateEntity> upParList = new ArrayList<>();
            List<QdPublicEntity> upPuList = new ArrayList<>();

            LambdaQueryWrapper<QdParticipateEntity> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(QdParticipateEntity::getClassId,qdClass.getId());
            List<QdParticipateEntity> parList = qdParticipateService.list(queryWrapper);
            if (parList.size()>0){
                for (QdParticipateEntity qdParticipateEntity : parList) {
                    qdParticipateEntity.setClassName(qdClass.getClassName());
                    upParList.add(qdParticipateEntity);
                }
            }

            LambdaQueryWrapper<QdPublicEntity> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(QdPublicEntity::getClassId,qdClass.getId());
            List<QdPublicEntity> puList = qdPublicService.list(wrapper);
            if (puList.size()>0){
                for (QdPublicEntity qdPublicEntity : puList) {
                    qdPublicEntity.setClassName(qdClass.getClassName());
                    upPuList.add(qdPublicEntity);
                }
            }
            if (upParList.size()>0) {
                qdParticipateService.updateBatchById(upParList);
            }
            if (upPuList.size()>0) {
                qdPublicService.updateBatchById(upPuList);
            }
            boolean ok = qdClassService.updateqdClassById(qdClass);
            if (ok){
                return result.success("修改成功");
            }
        }
        return result;
    }

    /**
      * 通过id查询
     */
    @AutoLog(value = "青马/大骨班班级管理-通过id查询")
    @ApiOperation(value = "青马/大骨班班级管理-通过id查询", notes = "青马/大骨班班级管理-通过id查询")
    @GetMapping(value = "/queryById")
    public Result<QdClassEntity> queryById(@RequestParam(name = "id", required = true) String id) {
        Result<QdClassEntity> result = new Result<QdClassEntity>();
            QdClassEntity qdClass = qdClassService.getById(id);
        if (qdClass==null){
            result.error500("未找到对应班级");
        }else{
            if (StringUtils.isNotBlank(qdClass.getDeptId())) {
                Collection<SysDepart> sysDeparts = sysDepartService.listByIds(Arrays.asList(qdClass.getDeptId().split(",")));
                String deptBuilder = "";
                if (!CollectionUtils.isEmpty(sysDeparts)) {
                    //拼接部门名称
                    StringBuilder stringBuilder = new StringBuilder();
                    sysDeparts.stream().forEach(sysDepart -> {
                        stringBuilder.append(sysDepart.getDepartName() + ",");
                    });
                    deptBuilder = stringBuilder.toString();
                    deptBuilder = deptBuilder.substring(0, deptBuilder.length() - 1);
                }
                qdClass.setDeptName(deptBuilder);
            }else {
                qdClass.setDeptId("").setDeptName("");
            }
            if (StringUtils.isNotBlank(qdClass.getNj())){
                String njName="";
                List<String>list=Arrays.asList(qdClass.getNj().split(","));
                for (String s:list){
                    if (s.equals("1")){
                        njName += "大一" + ",";
                    }else if (s.equals("2")){
                        njName += "大二" + ",";
                    }else if (s.equals("3")){
                        njName += "大三" + ",";
                    }else if (s.equals("4")){
                        njName += "大四" + ",";
                    }else if (s.equals("5")){
                        njName += "研究生" + ",";
                    }
                }
                qdClass.setNjName(njName.substring(0, njName.length() - 1));
            }else {
                qdClass.setNj("").setNjName("");
            }
            result.setResult(qdClass);
            result.setSuccess(true);
        }
        return result;
    }
    /**
      * 导出excel
     */
    @AutoLog(value = "青马/大骨班-班级导出列表")
    @ApiOperation(value = "青马/大骨班-班级导出列表", notes = "青马/大骨班-班级导出列表")
    @RequestMapping(value = "/exportXls")
    public ModelAndView exportXls(QdClassEntity qdClassEntity) {
        ModelAndView mv = new ModelAndView(new JeecgMapExcelView());
        //当前登录用户
        LoginUser sysUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        List<ExcelExportEntity> entityList = new ArrayList<>();
        ExcelExportEntity entity = new ExcelExportEntity("项目名称", "className");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("报名开始时间", "applyStarttime");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("报名结束时间", "applyEndtime");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("创建时间", "createTime");
        entity.setWidth(30);
        entityList.add(entity);
        entity = new ExcelExportEntity("状态", "status");
        entity.setWidth(30);
        entityList.add(entity);

        //查询条件
        QueryWrapper<QdClassEntity> queryWrapper = new QueryWrapper<>();
        if (StringUtils.isNotBlank(qdClassEntity.getClassName())) queryWrapper.like("class_name",qdClassEntity.getClassName());
        queryWrapper.orderByDesc("create_time");
        List<QdClassEntity> qdClassList = qdClassService.list(queryWrapper);

        //status转化为对应字
        Map<Integer,String> statusMap=new HashMap<>();
        statusMap.put(0,"未开始");
        statusMap.put(1,"进行中");
        statusMap.put(2,"已结束");
        List<Map<String, Object>> dataList = new ArrayList<>();
        for (QdClassEntity qdClass : qdClassList) {
            Map<String, Object> map = new HashMap<>();
            map.put("className", oConvertUtils.null2String(qdClass.getClassName()));
            map.put("applyStarttime", oConvertUtils.null2String(DateUtils.formatDate(qdClass.getApplyStarttime(), "yyyy-MM-dd HH:mm")));
            map.put("applyEndtime", oConvertUtils.null2String(DateUtils.formatDate(qdClass.getApplyEndtime(), "yyyy-MM-dd HH:mm")));
            map.put("createTime", oConvertUtils.null2String(DateUtils.formatDate(qdClass.getCreateTime(), "yyyy-MM-dd HH:mm:ss")));
            map.put("status", oConvertUtils.null2String(statusMap.get(qdClass.getStatus())));
            dataList.add(map);
        }

        ExportParams exportParams = new ExportParams();
        exportParams.setTitle("青马/大骨班-班级导出数据");
        exportParams.setSecondTitle("导出人：" + sysUser.getRealname() + "，导出时间：" + DateUtils.formatDate(DateUtils.getDate(), "yyyy-MM-dd HH:mm:ss") + "【 导出说明： 当导出数据量过大时系统将会把数据分成多个Sheet（工作表）导出,默认Sheet从0开始依次递增 】");
        exportParams.setSheetName("Sheet0");
        //导出文件名称
        mv.addObject(NormalExcelConstants.FILE_NAME, "青马/大骨班-班级导出列表");
        mv.addObject(MapExcelConstants.ENTITY_LIST, entityList);
        mv.addObject(NormalExcelConstants.PARAMS, exportParams);
        mv.addObject(NormalExcelConstants.MAP_LIST, dataList);
        return mv;
    }

    /**
      * 通过excel导入数据
     */
    @RequestMapping(value = "/importExcel", method = RequestMethod.POST)
    public Result<?> importExcel(HttpServletRequest request, HttpServletResponse response) {
        MultipartHttpServletRequest multipartRequest = (MultipartHttpServletRequest) request;
        Map<String, MultipartFile> fileMap = multipartRequest.getFileMap();
        for (Map.Entry<String, MultipartFile> entity : fileMap.entrySet()) {
            MultipartFile file = entity.getValue();// 获取上传文件对象
            ImportParams params = new ImportParams();
            params.setTitleRows(2);
            params.setHeadRows(1);
            params.setNeedSave(true);
            try {
                List<QdClassEntity> listQdClasss = ExcelImportUtil.importExcel(file.getInputStream(), QdClassEntity.class, params);
                    qdClassService.saveBatch(listQdClasss);
                return Result.ok("文件导入成功！数据行数:" + listQdClasss.size());
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Result.error("文件导入失败:" + e.getMessage());
            } finally {
                try {
                    file.getInputStream().close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
        return Result.ok("文件导入失败！");
    }


}
