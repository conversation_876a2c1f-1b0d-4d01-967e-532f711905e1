package com.zs.create.modules.oa.aoumaAndBones.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @ClassName suzheng
 * @Description TODO
 * @date 2022-11-14  10:35
 * @Version 1.0
 */
@Data
public class QdClassRspDto implements Serializable {
    private static final long serialVersionUID = -8200784237050375520L;

    /**
     * 班级Id
     */
    @ApiModelProperty(value = "班级Id")
    private String classId;
    /**
     * 班级名称
     */
    @ApiModelProperty(value = "班级名称")
    private String className;
    /**
     * 班级类型：0为大骨班，1为青马班
     */
    @ApiModelProperty(value = "班级类型：0为大骨班，1为青马班")
    private Integer type;
    /**
     * 班级报名开始时间
     */
    @ApiModelProperty(value = "班级报名开始时间")
    private Date applyStarttime;
    /**
     * 班级报名结束时间
     */
    @ApiModelProperty(value = "班级报名结束时间")
    private Date applyEndtime;
    /**
     * 班级报名是否已公示
     */
    @TableField(exist = false)
    private Boolean bmIsPublic;
    /**
     * 班级成绩是否已公示
     */
    @TableField(exist = false)
    private Boolean scIsPublic;
    /**
     * 成绩确认按钮出现
     */
    @TableField(exist = false)
    private Boolean isConfirm = false;
}
