package com.zs.create.modules.item.dto;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.zs.create.config.SerializerBigDecimal;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *成绩单预览对象
 * @Author: yc
 * @Date: 2022/03/29/9:37
 * @Description:
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ScItemScoreDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "姓名")
    private String realname;

    @ApiModelProperty(value = "学号")
    private String username;

    @ApiModelProperty(value = "头像")
    private String avatar;

    @ApiModelProperty(value = "头像")
    private String xn;

    /**
     * 性别（1：男 2：女）
     */
    @ApiModelProperty(value = "性别")
    private Integer sex;

    @ApiModelProperty(value = "院系")
    private String college;

    @ApiModelProperty(value = "出生年月")
    @JsonFormat(pattern = "yyyy年MM月", locale = "zh", timezone = "Asia/Shanghai")
    @DateTimeFormat(pattern = "yyyy年MM月",iso =DateTimeFormat.ISO.DATE )
    private Date birthday;

    @ApiModelProperty(value = "政治面貌")
    private String politics;

    @ApiModelProperty(value = "同年级活跃度")
    private Map activity;

    @ApiModelProperty(value = "模块学时")
    private Map hours;

    @ApiModelProperty(value = "模块星级")
    private Map score;

    @ApiModelProperty(value = "总学时")
    @JsonSerialize(using = SerializerBigDecimal.class)
    @JsonFormat(pattern = "0.0", shape = JsonFormat.Shape.STRING)
    private BigDecimal sumHours;

    @ApiModelProperty(value = "总星级")
    @JsonSerialize(using = SerializerBigDecimal.class)
    @JsonFormat(pattern = "0.00", shape = JsonFormat.Shape.STRING)
    private BigDecimal sumScore;

    @ApiModelProperty(value = "维护信息")
    private List <ScItemAchievementDto> contentList;
    @ApiModelProperty(value = "履职")
    private List<ScExperienceDto> work;

    @ApiModelProperty(value = "奖惩")
    private List<ScExperienceDto> honor;

    @ApiModelProperty(value = "履职总时长")
    private BigDecimal workHours;

    @ApiModelProperty(value = "奖惩总时长")
    private BigDecimal honorHours;

    @ApiModelProperty(value = "加密字符串")
    private String key;

}
