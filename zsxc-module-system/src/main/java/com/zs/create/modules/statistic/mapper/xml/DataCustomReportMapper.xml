<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.statistic.mapper.DataCustomReportMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.statistic.entity.DataCustomReportEntity" id="dataCustomReportMap">
        <result property="id" column="id"/>
        <result property="item_name" column="item_name"/>
        <result property="module" column="module"/>
        <result property="form" column="form"/>
        <result property="activity_level" column="activity_level"/>
        <result property="item_category" column="item_category"/>
        <result property="business_dept_id" column="business_dept_id"/>
        <result property="business_dept_yx_id" column="business_dept_yx_id"/>
        <result property="item_lable" column="item_lable"/>
        <result property="link_man" column="link_man"/>
        <result property="tel" column="tel"/>
        <result property="sponsor" column="sponsor"/>
        <result property="ew_sponsor" column="ew_sponsor"/>
        <result property="organizer" column="organizer"/>
        <result property="st" column="st"/>
        <result property="et" column="et"/>
        <result property="hold_time" column="hold_time"/>
        <result property="valid_hour" column="valid_hour"/>
        <result property="service_hour" column="service_hour"/>
        <result property="base_content" column="base_content"/>
        <result property="conceive" column="conceive"/>
        <result property="outlay_money" column="outlay_money"/>
        <result property="people_num" column="people_num"/>
        <result property="apply_st" column="apply_st"/>
        <result property="apply_et" column="apply_et"/>
        <result property="apply_st_et" column="apply_st_et"/>
        <result property="create_time" column="create_time"/>
        <result property="type" column="type"/>
        <result property="examine_status" column="examine_status"/>
        <result property="sign_up_people_num" column="sign_up_people_num"/>
        <result property="participate_in_people_num" column="participate_in_people_num"/>
        <result property="give_hour_people_num" column="give_hour_people_num"/>
    </resultMap>

    <select id="pageList" resultMap="dataCustomReportMap">
        select
        <if test="headerStr!='' and headerStr !=null">
        ${headerStr}
        </if>
     from data_custom_report
        <if test="str!='' and str !=null">
            ${str}
        </if>


    </select>
    <select id="getDataList" resultMap="dataCustomReportMap">
        select
        <if test="headerStr!='' and headerStr !=null">
        ${headerStr}
        </if>
     from data_custom_report
        <if test="str!='' and str !=null">
            ${str}
        </if>


    </select>

</mapper>