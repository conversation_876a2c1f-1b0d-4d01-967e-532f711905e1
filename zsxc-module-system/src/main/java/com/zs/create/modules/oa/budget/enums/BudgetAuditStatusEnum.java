package com.zs.create.modules.oa.budget.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;

public enum BudgetAuditStatusEnum {
    /**
     * 项目审核状态
     */
    STAGING("暂存", -2)
    ,REJECT("申请驳回", -1)
    ,APPLY("申请中", 1)
    ,PUBLISH("申请通过", 10);



    BudgetAuditStatusEnum(String name, Integer code) {
        this.name = name;
        this.code = code;
    }
    private String name;
    @EnumValue
    private Integer code;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    /**
     * 根据code值获取状态名称
     * @param code
     * @return
     */
    public static String getStatusNameByCode(Integer code){
        String statusName = null;
        for(BudgetAuditStatusEnum statusEnum : BudgetAuditStatusEnum.values()){
            if(statusEnum.getCode()==code){
                statusName = statusEnum.getName();
                break;
            }
        }
        return statusName;
    }
}
