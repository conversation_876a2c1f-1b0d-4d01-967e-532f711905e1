package com.zs.create.modules.system.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zs.create.modules.system.entity.SysUserRole;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 用户角色表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2018-12-21
 */
public interface ISysUserRoleService extends IService<SysUserRole> {

    /**
     * 查询所有的用户角色信息
     *
     * @return
     */
    Map<String, String> queryUserRole();

    /**
     * @Description 保存角色用户关系
     * <AUTHOR>
     * @Date 2019/9/27 14:04
     **/
    void saveRoleUser(SysUserRole sysUserRole) ;

    /**
     * 添加角色
     * @param roleCode
     * @param addUsers
     */
    void updateRole(String roleCode, List<String> addUsers,List<String> delUsers);

    List<String> getRoleByUserName(String username);

    /**
     * 根据角色编码查询用户
     * @param roleCode
     * @return
     */
    List<SysUserRole> qryUserList(String roleCode);

    /**
     * 根据用户id和角色编码删除用户角色
     * @param userId
     */
    void removeByUserId(String userId,String role);
}
