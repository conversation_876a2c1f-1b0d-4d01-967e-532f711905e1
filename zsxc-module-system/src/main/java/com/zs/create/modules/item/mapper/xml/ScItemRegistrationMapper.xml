<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.zs.create.modules.item.mapper.ScItemRegistrationMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.zs.create.modules.item.entity.ScItemRegistrationEntity" id="scItemRegistrationMap">
        <result property="id" column="id"/>
        <result property="realname" column="realname"/>
        <result property="userId" column="user_id"/>
        <result property="itemId" column="item_id"/>
        <result property="applyWay" column="apply_way"/>
        <result property="teamId" column="team_id"/>
        <result property="username" column="username"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="itemHours" column="item_hours"/>
        <result property="joined" column="joined"/>
    </resultMap>


    <select id="peopleRegistration" resultType="com.zs.create.modules.item.entity.PeopleSignUpVo">
    SELECT
	  b.realname as userName,
	  b.avatar as userPic,
	  b.username as userXuehao,
	  a.create_time as ct
    FROM
	    sc_item_registration a
    LEFT JOIN sys_user b ON a.user_id = b.id
    WHERE
	    a.ITEM_ID = #{itemId,jdbcType=VARCHAR}
        AND a.DEL_FLAG = '0'
        AND b.del_flag = '0'
        ORDER BY a.create_time DESC
        <if test="peopleSignUpVo.userName != null and peopleSignUpVo.userName!=''">
            and b.`realname` like concat('%',#{peopleSignUpVo.userName} ,'%')
        </if>
        <if test="peopleSignUpVo.userXuehao!=null and peopleSignUpVo.userXuehao!=''">
            and b.username = #{peopleSignUpVo.userXuehao}
        </if>
    </select>


    <select id="libListByProgrammeId" resultType="com.zs.create.modules.lib.entity.ScItemLibraryEntity">
        select * from sc_item_library a
        where a.id in
        (SELECT b.libraryId from sc_libray_programme b
        where b.programmeId=#{programmeId})
    </select>

    <select id="selectRegistrationNum" resultType="com.zs.create.modules.item.entity.ItemRegistrationVo">
        select item_id as itemId,COUNT(*) as ItemRegistrationNum
          from sc_item_registration where del_flag='0'
          and item_id in
            <foreach close=")" collection="itemIds" index="index" item="itemId" open="(" separator=",">
                #{itemId}
            </foreach>
          GROUP BY item_id
    </select>

    <select id="selectBooleanRegistration" resultType="com.zs.create.modules.item.entity.ScItemRegistrationEntity">
          select *
          from sc_item_registration where del_flag='0'
          and item_id in
            <foreach close=")" collection="itemIds" index="index" item="itemId" open="(" separator=",">
                #{itemId}
            </foreach>
    </select>

    <select id="sysUserList" resultType="com.zs.create.modules.system.entity.SysUserVo">
          select a.username,a.realname,b.phone,b.email ,b.status from sys_user a
          LEFT JOIN sc_item_registration b
          on a.id=b.user_id
          where b.item_id=#{itemId}
          and b.del_flag='0'
    </select>

    <select id="applyListUsers" resultType="java.lang.String">
        SELECT user_id FROM sc_item_registration where item_id=#{itemId} and del_flag=0
    </select>

    <select id="selectNotIntersection" resultType="java.lang.Integer">
        SELECT count(a.id) from sc_item a LEFT JOIN sc_item_registration b on a.id=b.item_id
        where b.user_id=#{userId}
        and a.form='0'
        and a.examine_status !="-3"
        and not (a.`st` &gt;= #{et} or a.`et` &lt;= #{st})
        and b.del_flag=0
    </select>

    <select id="getSumNumByDepid" resultType="com.zs.create.modules.statistic.entity.DepartPersonNum">
        SELECT d.id as depId,d.depart_name as depName,
        (SELECT IFNULL(COUNT(c.id),0) as sumNum FROM sys_depart a
         INNER JOIN sc_item b ON a.id=b.business_dept_id
         INNER JOIN sc_item_registration c ON b.id=c.item_id
         WHERE a.pids like CONCAT("%",d.id,"%")
         AND b.module="d"
        and c.del_flag='0'
        <if test="vo.level != null and vo.level != ''">
            and b.activity_level=#{vo.level}
        </if>
        <if test="vo.st != null and vo.et != null">
            and b.st &lt;=#{vo.st} and b.et &gt;= #{vo.et}
        </if>
        <if test="list !=null and list.size()>0">
            AND c.user_id in
            <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>) AS virtue,
        (SELECT IFNULL(COUNT(c.id),0) as sumNum FROM sys_depart a
        INNER JOIN sc_item b ON a.id=b.business_dept_id
        INNER JOIN sc_item_registration c ON b.id=c.item_id
        WHERE a.pids like CONCAT("%",d.id,"%")
        AND b.module="z"
        and c.del_flag=0
        <if test="vo.level != null and vo.level != ''">
            and b.activity_level=#{vo.level}
        </if>
        <if test="vo.st != null and vo.et != null">
            and b.st &lt;=#{vo.st} and b.et &gt;= #{vo.et}
        </if>
        <if test="list !=null and list.size()>0">
            AND c.user_id in
            <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>) AS wisdom,
        (SELECT IFNULL(COUNT(c.id),0) as sumNum FROM sys_depart a
        INNER JOIN sc_item b ON a.id=b.business_dept_id
        INNER JOIN sc_item_registration c ON b.id=c.item_id
        WHERE a.pids like CONCAT("%",d.id,"%")
        AND b.module="t"
        and c.del_flag=0
        <if test="vo.level != null and vo.level != ''">
            and b.activity_level=#{vo.level}
        </if>
        <if test="vo.st != null and vo.et != null">
            and b.st &lt;=#{vo.st} and b.et &gt;= #{vo.et}
        </if>
        <if test="list !=null and list.size()>0">
            AND c.user_id in
            <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>) AS body,
        (SELECT IFNULL(COUNT(c.id),0) as sumNum FROM sys_depart a
        INNER JOIN sc_item b ON a.id=b.business_dept_id
        INNER JOIN sc_item_registration c ON b.id=c.item_id
        WHERE a.pids like CONCAT("%",d.id,"%")
        AND b.module="m"
        and c.del_flag=0
        <if test="vo.level != null and vo.level != ''">
            and b.activity_level=#{vo.level}
        </if>
        <if test="vo.st != null and vo.et != null">
            and b.st &lt;=#{vo.st} and b.et &gt;= #{vo.et}
        </if>
        <if test="list !=null and list.size()>0">
            AND c.user_id in
            <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>) AS pretty,
        (SELECT IFNULL(COUNT(c.id),0) as sumNum FROM sys_depart a
        INNER JOIN sc_item b ON a.id=b.business_dept_id
        INNER JOIN sc_item_registration c ON b.id=c.item_id
        WHERE a.pids like CONCAT("%",d.id,"%")
        AND b.module="l"
        and c.del_flag=0
        <if test="vo.level != null and vo.level != ''">
            and b.activity_level=#{vo.level}
        </if>
        <if test="vo.st != null and vo.et != null">
            and b.st &lt;=#{vo.st} and b.et &gt;= #{vo.et}
        </if>
        <if test="list !=null and list.size()>0">
            AND c.user_id in
            <foreach close=")" collection="list" index="index" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>) AS work
        FROM sys_depart d
        WHERE d.org_type in ("2","14")
        <if test="vo.depId != null and vo.depId != ''">
            and d.id=#{vo.depId}
        </if>

    </select>

    <select id="itemNumList" resultMap="scItemRegistrationMap">
     SELECT c.id,c.user_id,COUNT(c.id) as itemNum,d.realname,#{vo.depId} as depId FROM sys_depart a
     INNER JOIN sc_item b ON a.id=b.business_dept_id
     INNER JOIN sc_participate_item c ON b.id=c.item_id
     INNER JOIN sys_user d ON d.id=c.user_id
     <where>
         and c.del_flag=0
         and exists (select  1 from  (select create_by , item_id  from sc_item_sign union all select create_by , item_id from sc_item_works ) t where create_by =  c.user_id   and c.item_id = item_id and c.del_flag=0)
         <if test="vo.depId != null and vo.depId != ''">
             and a.pids LIKE concat(#{vo.depId} ,'%')
         </if>
            and b.examine_status in('40','34')
            <if test="vo.level != null and vo.level != ''">
                and b.activity_level=#{vo.level}
            </if>
            <if test="vo.st != null and vo.et != null">
                and b.st &gt;=#{vo.st} and b.et &lt;=#{vo.et}
            </if>
            <if test="users !=null and users.size()>0">
                AND c.create_by in
                <foreach close=")" collection="users" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
     </where>
     GROUP BY c.user_id ORDER BY c.user_id
    </select>

    <select id="itemNumListExportXls" resultMap="scItemRegistrationMap">
        SELECT c.id,c.user_id,COUNT(c.id) as itemNum,d.realname,#{vo.depId} as depId FROM sys_depart a
        INNER JOIN sc_item b ON a.id=b.business_dept_id
        INNER JOIN sc_participate_item c ON b.id=c.item_id
        INNER JOIN sys_user d ON d.id=c.user_id
        <where>
            and c.del_flag=0
            and exists (select  1 from  (select create_by , item_id  from sc_item_sign union all select create_by , item_id from sc_item_works ) t where create_by =  c.user_id   and c.item_id = item_id and c.del_flag=0)
            <if test="vo.depId != null and vo.depId != ''">
                and a.pids LIKE concat(#{vo.depId} ,'%')
            </if>
            and b.examine_status in('40','34')
            <if test="vo.level != null and vo.level != ''">
                and b.activity_level=#{vo.level}
            </if>
            <if test="vo.st != null and vo.et != null">
                and b.st &gt;=#{vo.st} and b.et &lt;=#{vo.et}
            </if>
            <if test="users !=null and users.size()>0">
                AND c.create_by in
                <foreach close=")" collection="users" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY c.user_id ORDER BY c.user_id
    </select>

    <select id="listPersonNums" resultType="com.zs.create.modules.statistic.entity.DepartPersonNum">
        SELECT
        a.pids,
        b.module,
        IFNULL(COUNT(c.id), 0) AS sumNum
        FROM
        sys_depart a
        INNER JOIN sc_item b ON a.id = b.business_dept_id
        INNER JOIN sc_participate_item c ON b.id = c.item_id
        <where>
            and c.del_flag=0
            and b.examine_status in('40','34')
            and exists (select  1 from  (select create_by , item_id  from sc_item_sign union all select create_by , item_id from sc_item_works ) t
            where create_by =  c.user_id  and c.item_id = item_id) and
            <foreach collection="pids" item="item" index="index"  open="(" separator="or" close=")">
                 a.pids LIKE concat(#{item} ,'%')
            </foreach>
            <if test="vo.level != null and vo.level != ''">
                and b.activity_level=#{vo.level}
            </if>
            <if test="vo.st != null and vo.et != null">
                and b.st &gt;=#{vo.st} and b.et &lt;=#{vo.et}
            </if>
            <if test="users !=null and users.size()>0">
                AND c.create_by in
                <foreach close=")" collection="users" index="index" item="item" open="(" separator=",">
                    #{item}
                </foreach>
            </if>
<!--            <if test="vo.depId != null and vo.depId != ''">-->
<!--                  and a.pids LIKE concat(#{vo.depId} ,'%')-->
<!--            </if>-->
        </where>
        GROUP BY
        b.module,
        a.pids

    </select>

    <select id="queryUserId" resultType="java.lang.String">
        SELECT DISTINCT ir.user_id
        FROM sc_item_registration ir
                 INNER JOIN sc_item i ON i.id = ir.item_id
        WHERE ir.create_time >= #{start}
          AND ir.create_time &lt; #{end}
          AND i.del_flag = 0
          and ir.del_flag=0
    </select>
    <select id="selectBmNum" resultType="java.lang.Integer">
        select count(1)
        from sc_item_registration
        where item_id = #{item_id}
    </select>

    <select id="queryParticipateNum" resultType="com.zs.create.modules.statistic.entity.ScItemInfoNumDto">
        SELECT
            item_id  itemId,
            count(1) infoNum
        from sc_item_registration
        where del_flag=0
        GROUP BY item_id
    </select>
</mapper>
